require 'sidekiq/web'

Rails.application.routes.draw do
  devise_for :users, controllers: { registrations: 'registrations' }

  Sidekiq::Web.use Rack::Auth::Basic do |username, password|
    username == Rails.application.credentials.sidekiq[:username] && password == Rails.application.credentials.sidekiq[:password]
  end if Rails.env.production? || Rails.env.staging?
  mount Sidekiq::Web, at: "/sidekiq"

  authenticated :user do
    root 'dashboard#index', as: :authenticated_root
  end

  devise_scope :user do
    root 'devise/sessions#new'
  end

  resources :users, only: [:edit] do
    patch :update_api_key, on: :member
  end

  resources :auth, only: [:index]
  get 'kylas-auth', to: 'kylas_auth#index'
  get 'auth/facebook/callback', to: 'auth#callback'
  get 'webhook', to: 'auth#webhook'
  post 'webhook', to: 'facebook#webhook'

  post '/webhooks/handler', to: 'webhooks#handler'

  resources :forms do
    resources :mapped_fields, only: [:create, :destroy]
    collection do
      get '/products-lookup', to: 'forms#products_lookup'
      post 'sync'
      get 'sync-complete'
    end
    member do
      patch :save_by_user
    end
  end
  patch 'save-default-mapping', to: 'forms#save_default_mapping'

  resources :failed_leads, only: [:index] do
    member do
      get :retry
    end
    post :bulk_retry, on: :collection
  end

  get '/resync-jobs', to: 'bulk_jobs#index', as: :bulk_jobs
  get 'help', to: 'dashboard#help'
end

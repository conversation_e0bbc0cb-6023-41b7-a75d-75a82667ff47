require 'sidekiq/web'

Rails.application.routes.draw do
  devise_for :users, controllers: { registrations: 'registrations' }

  Sidekiq::Web.use Rack::Auth::Basic do |username, password|
    username == Rails.application.credentials.sidekiq[:username] && password == Rails.application.credentials.sidekiq[:password]
  end if Rails.env.production? || Rails.env.staging?
  mount Sidekiq::Web, at: "/sidekiq"

  authenticated :user do
    root 'dashboard#index', as: :authenticated_root
  end

  devise_scope :user do
    root 'devise/sessions#new'
  end

  resources :users, only: [:edit] do
    patch :update_api_key, on: :member
  end

  resources :auth, only: [:index]
  get 'kylas-auth', to: 'kylas_auth#index'
  get 'auth/facebook/callback', to: 'auth#callback'
  get 'webhook', to: 'auth#webhook'
  post 'webhook', to: 'facebook#webhook'

  # Instagram OAuth routes
  get 'auth/instagram_graph/callback', to: 'instagram/accounts#callback'

  post '/webhooks/handler', to: 'webhooks#handler'

  resources :forms do
    resources :mapped_fields, only: [:create, :destroy]
    collection do
      get '/products-lookup', to: 'forms#products_lookup'
      post 'sync'
      get 'sync-complete'
    end
    member do
      patch :save_by_user
    end
  end
  patch 'save-default-mapping', to: 'forms#save_default_mapping'

  resources :failed_leads, only: [:index] do
    member do
      get :retry
    end
    post :bulk_retry, on: :collection
  end

  get '/resync-jobs', to: 'bulk_jobs#index', as: :bulk_jobs
  get 'help', to: 'dashboard#help'

  # Instagram Integration Routes
  namespace :instagram do
    resources :accounts do
      member do
        patch :toggle_status
        post :sync_data
      end
    end

    resources :posts, only: [:index, :show] do
      member do
        post :sync_comments
        patch :toggle_monitoring
      end
    end

    resources :conversations, only: [:index, :show] do
      member do
        post :sync_messages
        patch :mark_as_lead
      end
    end

    resources :leads, only: [:index, :show] do
      member do
        post :retry_processing
        patch :update_qualification
      end
      collection do
        post :bulk_retry
        post :bulk_qualify
      end
    end

    resources :field_mappings, only: [:create, :update, :destroy]

    resources :analytics, only: [:index] do
      collection do
        get :engagement
        get :lead_performance
        get :conversion_funnel
      end
    end

    # Instagram webhook endpoint
    post '/webhook', to: 'webhooks#handler'
    get '/webhook', to: 'webhooks#verify'
  end
end

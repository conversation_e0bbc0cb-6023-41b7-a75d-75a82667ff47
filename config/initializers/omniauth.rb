Rails.application.config.middleware.use OmniAuth::Builder do
  provider :facebook, Rails.application.credentials.facebook[:app_id], Rails.application.credentials.facebook[:app_secret],
    scope: 'email,ads_management,read_insights,manage_pages',  token_params: { parse: :json }, display: 'popup'

  # Instagram OAuth provider (using Facebook app with Instagram permissions)
  provider :instagram_graph, Rails.application.credentials.facebook[:app_id], Rails.application.credentials.facebook[:app_secret],
    scope: 'instagram_basic,instagram_manage_comments,instagram_manage_messages,pages_show_list,pages_read_engagement',
    token_params: { parse: :json }, display: 'popup'
end

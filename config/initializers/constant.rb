if Rails.env.development? || Rails.env.test? || Rails.env.staging?
  APP_KYLAS_HOST = 'https://api-qa.sling-dev.com'
  BASE_HOST = 'https://app-qa.sling-dev.com'
elsif Rails.env.production?
  APP_KYLAS_HOST = 'https://api.kylas.io'
  BASE_HOST = 'https://app.kylas.io'
elsif Rails.env.selldo_staging?
  APP_KYLAS_HOST = 'https://api-qa.sell-do.com'
  BASE_HOST = 'https://app-qa.sell-do.com'
elsif Rails.env.selldo_production?
  APP_KYLAS_HOST = 'https://api.sell.do'
  BASE_HOST = 'https://crm.sell.do'
end

if Rails.env.selldo_production? || Rails.env.selldo_staging?
  APP_NAME = 'Sell Do'
  LOGO_URL = 'https://selldo-assets.sgp1.digitaloceanspaces.com/images/blue-logo.png'
  TERMS_URL = 'https://www.sell.do/terms-of-service'
  PRIVACY_URL = 'https://www.sell.do/privacy-policy'
  CONTACT_URL = 'https://www.sell.do/contact-us'
  SUPPORT_URL = 'https://support.sell.do'
else
  APP_NAME = 'Kylas'
  LOGO_URL = 'https://assets.kylas.io/images/blue-logo.png'
  TERMS_URL = 'https://kylas.io/terms-of-service'
  PRIVACY_URL = 'https://kylas.io/privacy'
  CONTACT_URL = 'https://www.kylas.io/contact'
  SUPPORT_URL = 'https://support.kylas.io'
end

FB_WEBHOOK_CHALLENGE = 'kylassalescrm'

STATIC_FB_FORM_ATTRIBUTES = [
  { label: 'Ad name', key: 'ad_name' },
  { label: 'Ad ID', key: 'ad_id' },
  { label: 'Campaign name', key: 'campaign_name' },
  { label: 'Campaign ID', key: 'campaign_id' },
  { label: 'Ad set name', key: 'adset_name' },
  { label: 'Ad set ID', key: 'adset_id' },
  { label: 'Created time', key: 'created_time' },
  { label: 'Form ID', key: 'form_id' },
  { label: 'Platform', key: 'platform' },
  { label: 'Lead Gen ID', key: 'leadgen_id' }
]

if Rails.env.staging? || Rails.env.production? || Rails.env.selldo_staging? || Rails.env.selldo_production?
  LOGDNA = Logdna::Ruby.new(
    Rails.application.credentials.log_dna_ingestion_key,
    { app: "kylas-fblead-#{Rails.env}", env: Rails.env }
  )

  Rails.logger = LOGDNA
end

LEAD = 'lead'
CONTACT = 'contact'
FAIL = 'fail'
KYLAS_APP_VIEW_DETAILS_LINK = "#{BASE_HOST}/marketplace/all-apps/app-details/#{Rails.application.credentials.kylas[:marketplace_app_id]}"
EXCEPT_CONTROLLER = %w[devise/sessions kylas_auth].freeze
STANDARD_PICKLIST_FIELDS = ['country', 'timezone', 'companyBusinessType', 'companyIndustry', 'companyCountry', 'requirementCurrency'].freeze
CS_EMAIL_ID = '<EMAIL>'.freeze
EXISTING = 'existing'
NEW = 'new'
IN_PROGRESS = 'in_progress'
COMPLETED = 'completed'
FAILED = 'failed'
LEADS = 'Leads'

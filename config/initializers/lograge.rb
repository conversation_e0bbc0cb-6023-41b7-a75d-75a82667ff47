# frozen_string_literal: true

Rails.application.configure do
  config.lograge.keep_original_rails_log = true
  config.lograge.formatter = Lograge::Formatters::Json.new

  if Rails.env.staging? || Rails.env.production? || Rails.env.selldo_staging? || Rails.env.selldo_production?
    config.lograge.enabled = true
    config.lograge.logger = LOGDNA
  end

  except_params = %w[controller action]
  config.lograge.custom_options = lambda do |event|
    log_hash = {
      log_level: event.payload[:level],
      host: event.payload[:host],
      request_time: event.time || Time.now,
      process_id: Process.pid,
      remote_ip: event.payload[:remote_ip],
      ip: event.payload[:ip],
      x_forwarded_for: event.payload[:x_forwarded_for],
      rails_env: Rails.env,
      request_id: event.payload[:headers]['action_dispatch.request_id'],
      user_id: event.payload[:user_id],
      exception: event.payload[:exception]
    }.compact
    params = event.payload[:params].except(*except_params)
    log_hash.merge!(params: params) if params.present?
    log_hash
  end
end

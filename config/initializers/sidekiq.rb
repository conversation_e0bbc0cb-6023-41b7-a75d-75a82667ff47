Sidekiq.configure_server do |config|
  config.redis = { :url => Rails.application.credentials.redis[:url], namespace: 'kylas_fb' }
  config.options[:concurrency] = 5

  # Configure queues with priorities (Instagram queues added)
  config.queues = %w[critical default instagram instagram_webhooks low]
end

Sidekiq.configure_client do |config|
  config.redis = { :namespace => 'kylas_fb', :url => Rails.application.credentials.redis[:url] }
end

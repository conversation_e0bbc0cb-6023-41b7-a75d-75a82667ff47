# Use this file to easily define all of your cron jobs.
#
# It's helpful, but not entirely necessary to understand cron before proceeding.
# http://en.wikipedia.org/wiki/Cron

# Example:
#
# set :output, "/path/to/my/cron_log.log"
#
# every 2.hours do
#   command "/usr/bin/some_great_command"
#   runner "MyModel.some_method"
#   rake "some:great:rake:task"
# end
#
# every 4.days do
#   runner "AnotherModel.prune_old_records"
# end

# Learn more: http://github.com/javan/whenever

# frozen_string_literal: true

every 1.day do
  rake 'clear_leads_from_model:clear_data'
end

every 1.hour do
  rake 'send_email_to_tenant_about_entity:send_email_to_tenant'
end

every 1.day do
  rake 'bulk_jobs:cleanup'
end

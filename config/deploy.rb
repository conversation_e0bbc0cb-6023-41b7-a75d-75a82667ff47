require 'mina/rails'
require 'mina/git'
# require 'mina/rbenv'  # for rbenv support. (https://rbenv.org)
require 'mina/rvm'    # for rvm support. (https://rvm.io)

# Basic settings:
#   domain       - The hostname to SSH to.
#   deploy_to    - Path to deploy into.
#   repository   - Git repo to clone from. (needed by mina/git)
#   branch       - Branch name to deploy. (needed by mina/git)

set :application_name, 'kylas_fblead'
set :deploy_to, '/var/www/kylas_fblead'
set :repository, '**************:amuratech/kylas_fblead.git'
set :rvm_use_path, '/usr/local/rvm/scripts/rvm'
# Optional settings:
set :user, 'root'          # Username in the server to SSH to.
#   set :port, '30000'           # SSH port number.
set :forward_agent, true     # SSH forward_agent.
env = ENV['on'] || 'staging'

if env == 'production'
  branch = ENV['branch'] || 'master'
  ip = '*************'
  set :shared_files, fetch(:shared_files, []).push('config/credentials/production.key')
elsif env == 'selldo_staging'
  branch = ENV['branch'] || 'develop'
  ip = '*************'
  set :shared_files, fetch(:shared_files, []).push('config/credentials/selldo_staging.key')
elsif env == 'selldo_production'
  branch = ENV['branch'] || 'master'
  ip = '***************'
  set :shared_files, fetch(:shared_files, []).push('config/credentials/selldo_production.key')
else
  branch = ENV['branch'] || 'dev'
  ip = '*************'
  set :bundle_bin, "/usr/local/rvm/rubies/ruby-3.0.0/bin/bundler"
  set :shared_files, fetch(:shared_files, []).push('config/credentials/staging.key')
end

set :domain, ip
set :branch, branch
set :rails_env, env

# This task is the environment that is loaded for all remote run commands, such as
# `mina deploy` or `mina rake`.
task :remote_environment do
  # If you're using rbenv, use this to load the rbenv environment.
  # Be sure to commit your .ruby-version or .rbenv-version to your repository.
  # invoke :'rbenv:load'

  # For those using RVM, use this to load an RVM version@gemset.
  # invoke :'rvm:use', 'ruby-1.9.3-p125@default'
  invoke :'rvm:use', 'ruby-3.0.0'
end

# Put any custom commands you need to run at setup
# All paths in `shared_dirs` and `shared_paths` will be created on their own.
task :setup do
  command %(touch "#{fetch(:deploy_to)}/shared/log/production.log")
  command %(touch "#{fetch(:deploy_to)}/shared/log/staging.log")
  set :shared_files, fetch(:shared_files, []).push('config/credentials/staging.key')
  set :shared_dirs, fetch(:shared_dirs, []).push('tmp', 'log', 'tmp/cache/bootsnap/')
  set :shared_files, fetch(:shared_files, []).push('log/staging.log')
  set :shared_files, fetch(:shared_files, []).push('log/production.log')
  command %[chmod 0666 "#{fetch(:deploy_to)}/shared/log/production.log"]

  command %[chmod 0666 "#{fetch(:deploy_to)}/shared/log/staging.log"]

  command %(mkdir -p "#{fetch(:deploy_to)}/shared/pids/")
  command %(mkdir -p "#{fetch(:deploy_to)}/shared/log/")

  # command %{rbenv install 2.3.0 --skip-existing}
end

desc "Deploys the current version to the server."
task :deploy do
  # uncomment this line to make sure you pushed your local branch to the remote origin
  # invoke :'git:ensure_pushed'
  deploy do
    if ['production', 'selldo_production', 'staging'].include? fetch(:rails_env)
      invoke :'nvm:load'
    end

    # Put things that will set up an empty directory into a fully set-up
    # instance of your project.
    invoke :'git:clone'
    invoke :'deploy:link_shared_paths'
    invoke :'bundle:install'
    invoke :'rails:db_migrate'
    invoke :'rails:assets_precompile'
    invoke :'deploy:cleanup'

    on :launch do
      in_path(fetch(:current_path)) do
        invoke :'rvm:use', 'ruby-3.0.0'
        command %{mkdir -p tmp/}
        command %{touch tmp/restart.txt}
        command %{bundle exec whenever -i kylas_fblead --update-crontab --set 'environment=#{env}'}

        command %{service sidekiq_fblead restart}
      end
    end
  end

  # you can use `run :local` to run tasks on local machine before of after the deploy scripts
  # run(:local){ say 'done' }
end

namespace :nvm do
  task :load do
    command 'echo "-----> Loading nvm"'
    command %{ source ~/.nvm/nvm.sh }
    command %{ nvm use 14.13.1 }
    command 'echo "-----> Now using nvm v.`nvm --version`"'
  end
end

# For help in making your deploy script, see the Mina documentation:
#
#  - https://github.com/mina-deploy/mina/tree/master/docs

const path    = require("path")
const webpack = require("webpack")

// Extracts CSS into .css file
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const RemoveEmptyScriptsPlugin = require('webpack-remove-empty-scripts');

const mode = process.env.NODE_ENV === 'development' ? 'development' : process.env.NODE_ENV == 'staging' ? 'staging' : 'production';

module.exports = {
  mode,
  entry: {
    application: [
      './app/javascript/packs/application.js',
      './app/javascript/stylesheets/application.scss',
    ],
  },
  module: {
    rules: [
      {
        test: /\.(js)$/,
        exclude: /node_modules/,
        use: ['babel-loader'],
      },
      {
        test: /\.s[ac]ss$/i,
        use: [MiniCssExtractPlugin.loader, 'css-loader', 'sass-loader']
      },
      {
        test: /\.(png|jpe?g|gif|eot|woff2|woff|ttf|svg)$/i,
        use: 'file-loader'
      },
    ],
  },
  resolve: {
    // Add additional file types
    extensions: ['.js', '.scss', '.css', '.png', '.svg', '.jpeg', '.ico', '.jpg'],
  },
  output: {
    filename: "[name].js",
    sourceMapFilename: "[name].js.map",
    path: path.resolve(__dirname, '..', '..', 'app/assets/builds')
  },
  plugins: [
    new webpack.optimize.LimitChunkCountPlugin({
      maxChunks: 1
    }),
    new MiniCssExtractPlugin(),
    new RemoveEmptyScriptsPlugin(),
    new webpack.ProvidePlugin({
      $: "jquery",
      jQuery: "jquery",
      Popper: ['popper.js', 'default']
    })
  ],
  optimization: {
    moduleIds: 'deterministic',
    removeEmptyChunks: true
  }
}
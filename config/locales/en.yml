# Files in the config/locales directory are used for internationalization
# and are automatically loaded by Rails. If you want to use locales other
# than English, add the necessary files in this directory.
#
# To use the locales, use `I18n.t`:
#
#     I18n.t 'hello'
#
# In views, this is aliased to just `t`:
#
#     <%= t('hello') %>
#
# To use a different locale, set it with `I18n.locale`:
#
#     I18n.locale = :es
#
# This would use the information in config/locales/es.yml.
#
# The following keys must be escaped otherwise they will not be retrieved by
# the default I18n backend:
#
# true, false, on, off, yes, no
#
# Instead, surround them with single quotes.
#
# en:
#   'true': 'foo'
#
# To learn more, please read the Rails Internationalization guide
# available at https://guides.rubyonrails.org/i18n.html.

en:
  hello: "Hello world"
  credentials_mismatch_message: 'Signed out because your kylas user and marketplace app user mismatched'
  application_reinstall_message: 'Application is not installed properly, Please reinstall the application.'
  error_message: 'Something went wrong!'
  views:
    pagination:
      previous: "&lt; Previous"
      next: "Next &gt;"
      truncate: "..."
  default_mapping:
    title: "Default Mapping"
    tooltip_text: "This will add the default mapping in form. You can also add custom field mapping in addition to default mapping."
  form:
    information: "Add: Subscribe to incoming leads | Update: Update Campaign / Source | Remove: Cancel subscription"
    error_loading_forms: 'We are facing facebook permission issue! Please reconnect and try again!'
    existing_forms: "Existing Forms"
    new_forms: "New Forms"
    successfully_saved: 'Form saved successfully.'
    failed_to_save: 'Failed to save the form.'
    sync_in_progress: 'Syncing is in progress. Please refresh the page after sometime.'
    fetch_forms: 'Fetch Forms'
    fetching_forms: 'Fetching forms...'
    filter_forms: 'Filter Forms'
    title: 'Fetch Facebook Lead Forms'
    description: 'Fetch lead forms from your connected Facebook pages.'
  connected_account:
    not_found: 'Connected account not found.'
  utm_field:
    message: 'Enter a %{field} to map fix value for new %{entity} when no value found from facebook'
  failed_leads:
    search_placeholder: "Search Facebook LeadGen ID"
    search_tooltip: "Search by LeadGen ID"
    filter_button: "Filter Sync Logs"
    clear_button: "Clear"
    apply_button: "Apply"
    entity_id_label: "Kylas Entity ID"
    entity_id_placeholder: "Enter Kylas Entity ID"
    sync_status_label: "Sync Status"
    start_date_label: "Start Date"
    end_date_label: "End Date"
    success: "Success"
    fail: "Fail"
    form_name: "Form Name"
    leadgen_id: "Facebook LeadGen ID"
    entity_type: "Kylas Entity Type"
    entity_id: "Kylas Entity ID"
    sync_status: "Sync Status"
    sync_error: "Sync Error"
    created_at: "Created At Range"
    actions: "Actions"
    lead: "Lead"
    contact: "Contact"
    none: "-"
    success: "Success"
    fail: "Fail"
    sync_now: "Sync Now"
    sync_status_placeholder: 'Select Sync Status'
    select_all: "Select all %{count} failed logs"
    unselect_all: "Unselect all"
    resync: "Resync"
    sync_logs: "Sync Logs"
    bulk_resync_success: "%{retry_count} Leads Sync logs are being rescheduled successfully"
    no_selected_logs: 'No logs selected to resync'
    no_failed_logs: 'No failed logs found to resync'
    another_bulk_job_in_progress: 'A resync job is already in progress. Please try again after some time.'
  bulk_job:
    job_id: 'Job Id'
    status: 'Status'
    total_count: 'Total Count'
    passed: 'Passed'
    failed: 'Failed'
    created_at: 'Created At'
    created_by: 'Created By'
    bulk_job_logs: 'All Resync Job Logs'


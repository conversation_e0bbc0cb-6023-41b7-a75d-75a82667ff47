# Instagram Integration Architecture Plan

## Overview

This document outlines the comprehensive plan for integrating Instagram functionality into the Kylas FB Lead application. The Instagram integration will be completely separate from the existing Facebook Lead Ads flow, ensuring no disruption to current functionality.

## Instagram API Capabilities Analysis

### Available Instagram APIs (Post-December 2024)

#### 1. Instagram Graph API
**Primary API for business accounts and professional features**

**Core Capabilities:**
- **Account Management**: Access Instagram Business and Creator accounts
- **Media Management**: Retrieve posts, stories, reels, and IGTV content
- **Publishing**: Create and publish content programmatically
- **Insights & Analytics**: Access detailed performance metrics
- **Comment Management**: Read, reply to, and moderate comments
- **Hashtag Research**: Search and analyze hashtag performance
- **Business Discovery**: Get metadata about other business accounts

**Key Endpoints:**
- `/{ig-user-id}` - User account information
- `/{ig-user-id}/media` - User's media objects
- `/{ig-media-id}` - Individual media details
- `/{ig-media-id}/comments` - Media comments
- `/{ig-media-id}/insights` - Media performance metrics
- `/{ig-user-id}/insights` - Account insights
- `/ig_hashtag_search` - Hashtag search
- `/{ig-hashtag-id}/top_media` - Top posts for hashtag

#### 2. Instagram Messaging API
**For direct message and customer communication**

**Core Capabilities:**
- **Direct Messages**: Send and receive Instagram DMs
- **Message Threading**: Manage conversation threads
- **Media Messaging**: Send/receive images, videos, and files
- **Quick Replies**: Set up automated response options
- **Private Replies**: Respond privately to public comments
- **Webhooks**: Real-time message notifications

**Key Endpoints:**
- `/{ig-user-id}/conversations` - List conversations
- `/{conversation-id}/messages` - Conversation messages
- `/{ig-user-id}/messages` - Send messages
- `/{message-id}` - Message details

#### 3. Instagram Webhooks
**Real-time notifications for various Instagram events**

**Supported Events:**
- `messages` - New direct messages
- `messaging_postbacks` - Button/quick reply interactions
- `messaging_optins` - User opt-ins for messaging
- `comments` - New comments on posts
- `mentions` - When account is mentioned
- `story_insights` - Story performance updates

### Lead Generation Opportunities

#### 1. Comment-to-Lead Conversion
- Monitor comments on posts for lead indicators
- Extract contact information from comments
- Qualify leads based on comment content and user profile
- Auto-respond to potential leads

#### 2. Direct Message Lead Capture
- Process incoming DMs for lead qualification
- Extract contact details from conversations
- Set up automated lead qualification flows
- Route qualified leads to Kylas

#### 3. Hashtag and Mention Monitoring
- Track brand mentions and relevant hashtags
- Identify potential customers discussing related topics
- Engage with prospects through comments or DMs
- Convert interactions to leads

#### 4. Story Interaction Tracking
- Monitor story replies and reactions
- Track story mentions and shares
- Capture engagement data for lead scoring

## Architecture Design

### Database Schema Extensions

#### New Models for Instagram Integration

```ruby
# Instagram Account Model
class InstagramAccount < ApplicationRecord
  belongs_to :user
  has_many :instagram_posts
  has_many :instagram_conversations
  has_many :instagram_leads
  
  # Fields: instagram_user_id, username, account_type, access_token, etc.
end

# Instagram Post Model
class InstagramPost < ApplicationRecord
  belongs_to :instagram_account
  has_many :instagram_comments
  has_many :instagram_leads, through: :instagram_comments
  
  # Fields: media_id, media_type, caption, permalink, timestamp, etc.
end

# Instagram Comment Model
class InstagramComment < ApplicationRecord
  belongs_to :instagram_post
  has_one :instagram_lead
  
  # Fields: comment_id, text, username, timestamp, is_lead, etc.
end

# Instagram Conversation Model
class InstagramConversation < ApplicationRecord
  belongs_to :instagram_account
  has_many :instagram_messages
  has_one :instagram_lead
  
  # Fields: conversation_id, participant_id, last_message_time, etc.
end

# Instagram Message Model
class InstagramMessage < ApplicationRecord
  belongs_to :instagram_conversation
  
  # Fields: message_id, text, sender_id, timestamp, message_type, etc.
end

# Instagram Lead Model
class InstagramLead < ApplicationRecord
  belongs_to :user
  belongs_to :instagram_account
  belongs_to :form, optional: true
  
  # Fields: source_type, source_id, raw_data, kylas_lead_id, status, etc.
end

# Instagram Webhook Event Model
class InstagramWebhookEvent < ApplicationRecord
  belongs_to :instagram_account
  
  # Fields: event_type, event_data, processed_at, status, etc.
end
```

### Service Layer Architecture

#### Instagram Services Structure

```
app/services/instagram/
├── authentication/
│   ├── oauth_handler.rb
│   └── token_refresh.rb
├── data_fetching/
│   ├── account_data.rb
│   ├── media_data.rb
│   ├── comment_data.rb
│   ├── message_data.rb
│   └── insights_data.rb
├── lead_processing/
│   ├── comment_processor.rb
│   ├── message_processor.rb
│   ├── mention_processor.rb
│   └── lead_qualifier.rb
├── kylas_integration/
│   ├── create_lead_from_instagram.rb
│   ├── create_contact_from_instagram.rb
│   └── field_mapper.rb
├── webhook_handlers/
│   ├── message_webhook.rb
│   ├── comment_webhook.rb
│   └── mention_webhook.rb
└── automation/
    ├── auto_responder.rb
    ├── lead_scorer.rb
    └── engagement_tracker.rb
```

### Controller Architecture

#### Instagram Controllers

```
app/controllers/instagram/
├── base_controller.rb
├── accounts_controller.rb
├── posts_controller.rb
├── comments_controller.rb
├── conversations_controller.rb
├── leads_controller.rb
├── webhooks_controller.rb
├── analytics_controller.rb
└── settings_controller.rb
```

### Background Job Architecture

#### Instagram-Specific Jobs

```
app/jobs/instagram/
├── account_sync_job.rb
├── media_sync_job.rb
├── comment_processor_job.rb
├── message_processor_job.rb
├── lead_qualification_job.rb
├── webhook_processor_job.rb
├── insights_sync_job.rb
└── bulk_lead_sync_job.rb
```

## Integration Flow Design

### 1. Instagram Account Connection Flow

```
User Dashboard → Instagram Integration → Connect Account → OAuth Flow → 
Account Selection → Permission Grant → Token Storage → Account Sync
```

### 2. Lead Capture Flows

#### Comment-to-Lead Flow
```
Instagram Comment → Webhook Notification → Comment Analysis → 
Lead Qualification → Field Mapping → Kylas Lead Creation → Notification
```

#### DM-to-Lead Flow
```
Instagram DM → Webhook Notification → Message Analysis → 
Lead Qualification → Conversation Context → Kylas Lead Creation → Auto-Response
```

#### Mention-to-Lead Flow
```
Instagram Mention → Webhook Notification → Mention Analysis → 
User Profile Check → Lead Qualification → Kylas Lead Creation → Engagement
```

### 3. Data Processing Pipeline

```
Raw Instagram Data → Data Validation → Lead Qualification → 
Field Mapping → Kylas API Call → Success/Failure Handling → 
Notification/Retry Logic
```

## Field Mapping Strategy

### Instagram Data to Kylas Field Mapping

#### From Comments
| Instagram Field | Kylas Field | Processing |
|----------------|-------------|------------|
| `username` | `lastName` | Direct mapping |
| `comment_text` | `description` | Lead context |
| `user_profile.bio` | `companyName` | If business profile |
| `user_profile.email` | `emails` | If available in bio |
| `user_profile.phone` | `phoneNumbers` | Extract from bio/comment |
| `post_context` | `source` | Post URL/ID |
| `engagement_score` | Custom Field | Calculated metric |

#### From Direct Messages
| Instagram Field | Kylas Field | Processing |
|----------------|-------------|------------|
| `sender_username` | `lastName` | Direct mapping |
| `message_text` | `description` | Conversation context |
| `sender_profile.bio` | `companyName` | If business profile |
| `contact_info` | `emails/phoneNumbers` | Extract from messages |
| `conversation_id` | `source` | Message thread reference |
| `message_count` | Custom Field | Engagement level |

### Lead Qualification Rules

#### Comment Qualification Criteria
- Contains contact information (email, phone)
- Includes buying intent keywords
- Asks product/service questions
- Requests more information
- Shows interest in offerings

#### DM Qualification Criteria
- Direct inquiry about products/services
- Contains contact information
- Requests quotes or pricing
- Asks for business information
- Shows purchase intent

#### Engagement Scoring
- Profile completeness (bio, contact info, follower count)
- Interaction history with account
- Comment/message quality and length
- Response time and frequency
- Profile type (business vs personal)

## Technical Implementation Plan

### Phase 1: Foundation (Weeks 1-2)
1. Database schema creation
2. Basic Instagram OAuth implementation
3. Account connection flow
4. Basic webhook setup

### Phase 2: Core Features (Weeks 3-4)
1. Media and comment fetching
2. Direct message handling
3. Basic lead qualification
4. Kylas integration for Instagram leads

### Phase 3: Advanced Features (Weeks 5-6)
1. Advanced lead scoring
2. Automated responses
3. Analytics and insights
4. Bulk operations and retry mechanisms

### Phase 4: UI and Polish (Weeks 7-8)
1. Instagram-specific UI components
2. Dashboard integration
3. Reporting and analytics views
4. Documentation and testing

## Security and Compliance

### Data Protection
- Encrypt Instagram access tokens
- Secure webhook endpoint validation
- Rate limiting for API calls
- User consent for data processing

### API Compliance
- Follow Instagram API terms of service
- Implement proper rate limiting
- Handle API deprecations gracefully
- Maintain audit logs for API usage

## Monitoring and Analytics

### Key Metrics to Track
- Instagram leads generated
- Conversion rates by source (comments, DMs, mentions)
- Response times for lead processing
- API usage and rate limiting
- Error rates and retry success

### Reporting Features
- Instagram lead performance dashboard
- Source attribution reporting
- Engagement analytics
- ROI tracking for Instagram marketing

This architecture ensures a robust, scalable Instagram integration that complements the existing Facebook Lead Ads functionality while maintaining complete separation of concerns.

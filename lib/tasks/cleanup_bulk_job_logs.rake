# frozen_string_literal: true

namespace :bulk_jobs do
  desc "Clean up resync jobs for all users"
  task cleanup: :environment do
    puts "Starting resync jobs cleanup task..."
    
    User.find_each do |user|
      begin
        log_count = user.bulk_jobs.count
        
        if log_count >= 40
          cutoff_date = 7.days.ago
          old_logs = user.bulk_jobs.where('created_at < ?', cutoff_date)
          
          if old_logs.any?
            deleted_count = old_logs.count
            old_logs.delete_all
            puts "Cleaned up #{deleted_count} resync jobs for user #{user.id}"
          else
            puts "No old logs to clean for user #{user.id}"
          end
        else
          puts "user #{user.id} has only #{log_count} logs, skipping cleanup"
        end
      rescue => e
        Rails.logger.error "Error cleaning up logs for user #{user.id}: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end
    end
    
    puts "Bulk job logs cleanup task completed"
  end
end

# Run the rake task with: bundle exec rake bulk_jobs:cleanup

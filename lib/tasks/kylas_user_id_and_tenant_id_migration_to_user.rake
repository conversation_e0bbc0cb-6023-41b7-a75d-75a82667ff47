# frozen_string_literal: true

namespace :user_model_migration do
  task :kylas_user_id_and_tenant_id_to_user, [:user_id] => :environment do |t, args|

    records =
      if args[:user_id].present?
        User.where(id: args[:user_id])
      else
        User.where(kylas_user_id: nil).or(User.where(kylas_tenant_id: nil))
      end

    records.each do |user|
      user_response = Kylas::GetUserDetails.new(token: user.get_access_token, user: user).call

      response = user.update(kylas_tenant_id: user_response[:tenant_id], kylas_user_id: user_response[:id]) if user_response&.include?(:tenant_id)

      if response.present?
        puts "===User with id: #{user.id} updated successfully with kylas tenant id: #{user.kylas_tenant_id} and kylas user id: #{user.kylas_user_id}==="
      else
        puts "===User with id: #{user.id} not updated successfully==="
      end
    rescue StandardError => e
      puts "===Exception raised while updating user with id: #{user.id}. Error = #{e.message}==="
    end
  end
end

# Command to execute task:
# RAILS_ENV=development bundle exec rake user_model_migration:kylas_user_id_and_tenant_id_to_user

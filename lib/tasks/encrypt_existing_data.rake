# frozen_string_literal: true

namespace :encrypt_existing_data do
  # run task using command: rake encrypt_existing_data:user_migration
  task user_migration: :environment do
    puts 'User Encrypt Migration - Started'
    user_encrypted_fields = %i[kylas_api_key kylas_access_token kylas_refresh_token]
    User.find_each do |user|
      bind_var = binding
      user_encrypted_fields.each do |field|
        already_encrypted = user.encrypted_attribute?(field)
        next if already_encrypted

        bind_var.local_variable_set("fblead_#{field}_new_value".to_sym, nil)
        ActiveRecord::Encryption.without_encryption do
          bind_var.local_variable_set("fblead_#{field}_new_value".to_sym, user.send(field))
          user.update_columns(field => nil)
        end
        user.send("#{field}=", bind_var.local_variable_get("fblead_#{field}_new_value".to_sym))
      end
      puts "Couldn't encrypt for user##{user.id}" unless user.save(validate: false)
    end
    puts 'User Encrypt Migration - Completed'
  end

  task connected_account_migration: :environment do
    # run task using command: rake encrypt_existing_data:connected_account_migration
    puts 'ConnectedAccount Encrypt Migration - Started'
    connected_account_encrypted_fields = %i[email_id access_token refresh_token]
    ConnectedAccount.find_each do |connected_account|
      bind_var = binding
      connected_account_encrypted_fields.each do |field|
        already_encrypted = connected_account.encrypted_attribute?(field)
        next if already_encrypted

        bind_var.local_variable_set("fblead_#{field}_new_value".to_sym, nil)
        ActiveRecord::Encryption.without_encryption do
          bind_var.local_variable_set("fblead_#{field}_new_value".to_sym, connected_account.send(field))
          connected_account.update_columns(field => nil)
        end
        connected_account.send("#{field}=", bind_var.local_variable_get("fblead_#{field}_new_value".to_sym))
      end
      connected_account.encryption_skip_callback = true
      unless connected_account.save(validate: false)
        puts "Couldn't encrypt for connected_account##{connected_account.id}"
      end
    end
    puts 'ConnectedAccount Encrypt Migration - Completed'
  end
end

namespace :migrate_form_data_to_utm do
  desc 'Migrate form data yo UTM fields' do
    task perform: :environment do
      Form.all.each do |form|
        lead_utm = form.utm_fields.create(entity_type: LEAD, campaign_id: form.campaign_id, kylas_source_id: form.kylas_source_id, owner_id: form.owner_id)
        contact_utm = form.utm_fields.create(entity_type: CONTACT)

        Rails.logger.info "Form ID = #{form.id} - created utm fields - #{lead_utm.id}, #{contact_utm.id}"
      end
    end
  end
end

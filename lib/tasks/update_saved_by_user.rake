
namespace :forms do
  # run task using command: rake forms:update_saved_by_user
  desc 'Update saved_by_user to true for all forms'
  task update_saved_by_user: :environment do
    forms_count = Form.count
    puts "Updating saved_by_user to true for #{forms_count} forms - Started"
    Form.update_all(saved_by_user: true)
    puts "Updating saved_by_user to true for #{forms_count} forms - Completed"
  end
end

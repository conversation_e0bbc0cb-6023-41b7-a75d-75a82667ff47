# frozen_string_literal: true

class Facebook::Messenger::BotManagement
  def initialize(page)
    @page = page
    @user = page.user
  end

  def setup_welcome_message(message_text)
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      response = set_welcome_message(message_text)
      
      if response
        { success: true, message: 'Welcome message set successfully' }
      else
        { success: false, error: 'Failed to set welcome message' }
      end
    rescue => e
      Rails.logger.error "Facebook welcome message setup error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  def setup_persistent_menu(menu_items)
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      response = set_persistent_menu(menu_items)
      
      if response
        { success: true, message: 'Persistent menu set successfully' }
      else
        { success: false, error: 'Failed to set persistent menu' }
      end
    rescue => e
      Rails.logger.error "Facebook persistent menu setup error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  def setup_auto_responses(responses_config)
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      # Store auto-response configuration
      @page.update!(
        page_data: @page.page_data.merge(
          'messenger_auto_responses' => responses_config
        )
      )
      
      { success: true, message: 'Auto-responses configured successfully' }
    rescue => e
      Rails.logger.error "Facebook auto-responses setup error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  def send_message(recipient_id, message_data)
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      response = send_message_api(recipient_id, message_data)
      
      if response
        # Log the sent message
        log_sent_message(recipient_id, message_data, response)
        { success: true, message_id: response['message_id'] }
      else
        { success: false, error: 'Failed to send message' }
      end
    rescue => e
      Rails.logger.error "Facebook send message error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  def send_template_message(recipient_id, template_type, template_data)
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      message_data = build_template_message(template_type, template_data)
      send_message(recipient_id, message_data)
    rescue => e
      Rails.logger.error "Facebook template message error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  def process_incoming_message(message_data)
    begin
      # Find or create conversation
      conversation = find_or_create_conversation(message_data)
      
      # Create message record
      message = create_message_record(conversation, message_data)
      
      # Check for auto-response triggers
      check_auto_response_triggers(conversation, message)
      
      # Process for lead potential
      Facebook::LeadProcessing::MessageAnalysisJob.perform_later(message.id)
      
      { success: true, conversation: conversation, message: message }
    rescue => e
      Rails.logger.error "Facebook incoming message processing error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @user.connected_account&.access_token.present?
  end

  def set_welcome_message(message_text)
    access_token = @user.connected_account.access_token
    
    url = "https://graph.facebook.com/v18.0/#{@page.source_id}/messenger_profile"
    payload = {
      greeting: [
        {
          locale: 'default',
          text: message_text
        }
      ],
      access_token: access_token
    }

    response = RestClient.post(url, payload.to_json, { content_type: :json })
    JSON.parse(response.body) if response.code == 200
  end

  def set_persistent_menu(menu_items)
    access_token = @user.connected_account.access_token
    
    url = "https://graph.facebook.com/v18.0/#{@page.source_id}/messenger_profile"
    payload = {
      persistent_menu: [
        {
          locale: 'default',
          composer_input_disabled: false,
          call_to_actions: menu_items
        }
      ],
      access_token: access_token
    }

    response = RestClient.post(url, payload.to_json, { content_type: :json })
    JSON.parse(response.body) if response.code == 200
  end

  def send_message_api(recipient_id, message_data)
    access_token = @user.connected_account.access_token
    
    url = "https://graph.facebook.com/v18.0/#{@page.source_id}/messages"
    payload = {
      recipient: { id: recipient_id },
      message: message_data,
      access_token: access_token
    }

    response = RestClient.post(url, payload.to_json, { content_type: :json })
    JSON.parse(response.body) if response.code == 200
  end

  def build_template_message(template_type, template_data)
    case template_type
    when 'generic'
      build_generic_template(template_data)
    when 'button'
      build_button_template(template_data)
    when 'list'
      build_list_template(template_data)
    else
      { text: template_data[:text] || 'Hello!' }
    end
  end

  def build_generic_template(data)
    {
      attachment: {
        type: 'template',
        payload: {
          template_type: 'generic',
          elements: data[:elements] || []
        }
      }
    }
  end

  def build_button_template(data)
    {
      attachment: {
        type: 'template',
        payload: {
          template_type: 'button',
          text: data[:text],
          buttons: data[:buttons] || []
        }
      }
    }
  end

  def build_list_template(data)
    {
      attachment: {
        type: 'template',
        payload: {
          template_type: 'list',
          elements: data[:elements] || [],
          buttons: data[:buttons] || []
        }
      }
    }
  end

  def find_or_create_conversation(message_data)
    sender_id = message_data.dig('sender', 'id')
    
    conversation = @page.facebook_conversations.find_by(participant_id: sender_id)
    
    unless conversation
      conversation = @page.facebook_conversations.create!(
        conversation_id: "#{@page.source_id}_#{sender_id}",
        participant_id: sender_id,
        participant_name: message_data.dig('sender', 'name') || 'Unknown User',
        updated_time: Time.current,
        message_count: 0,
        can_reply: true
      )
    end
    
    conversation
  end

  def create_message_record(conversation, message_data)
    conversation.facebook_messages.create!(
      message_id: message_data['mid'],
      message: message_data['text'],
      from_id: message_data.dig('sender', 'id'),
      from_name: message_data.dig('sender', 'name'),
      to_id: @page.source_id,
      to_name: @page.name,
      created_time: Time.at(message_data['timestamp'] / 1000),
      is_from_page: false,
      message_type: determine_message_type(message_data),
      attachment_url: extract_attachment_url(message_data),
      attachment_type: extract_attachment_type(message_data)
    )
  end

  def check_auto_response_triggers(conversation, message)
    auto_responses = @page.page_data.dig('messenger_auto_responses') || {}
    
    auto_responses.each do |trigger, response_config|
      if message_matches_trigger?(message, trigger)
        send_auto_response(conversation, response_config)
        break # Send only the first matching response
      end
    end
  end

  def message_matches_trigger?(message, trigger)
    case trigger['type']
    when 'keyword'
      message.message.downcase.include?(trigger['value'].downcase)
    when 'greeting'
      greeting_keywords = ['hi', 'hello', 'hey', 'good morning', 'good afternoon']
      greeting_keywords.any? { |keyword| message.message.downcase.include?(keyword) }
    when 'question'
      message.message.include?('?')
    else
      false
    end
  end

  def send_auto_response(conversation, response_config)
    message_data = {
      text: response_config['message']
    }
    
    send_message(conversation.participant_id, message_data)
  end

  def log_sent_message(recipient_id, message_data, response)
    # Find conversation and log the sent message
    conversation = @page.facebook_conversations.find_by(participant_id: recipient_id)
    return unless conversation

    conversation.facebook_messages.create!(
      message_id: response['message_id'],
      message: message_data[:text] || '[Template Message]',
      from_id: @page.source_id,
      from_name: @page.name,
      to_id: recipient_id,
      created_time: Time.current,
      is_from_page: true,
      message_type: message_data[:attachment] ? 'template' : 'text'
    )
  end

  def determine_message_type(message_data)
    return 'image' if message_data['attachments']&.any? { |a| a['type'] == 'image' }
    return 'video' if message_data['attachments']&.any? { |a| a['type'] == 'video' }
    return 'audio' if message_data['attachments']&.any? { |a| a['type'] == 'audio' }
    return 'file' if message_data['attachments']&.any? { |a| a['type'] == 'file' }
    'text'
  end

  def extract_attachment_url(message_data)
    attachment = message_data.dig('attachments', 0)
    return nil unless attachment

    attachment.dig('payload', 'url')
  end

  def extract_attachment_type(message_data)
    attachment = message_data.dig('attachments', 0)
    return nil unless attachment

    attachment['type']
  end
end

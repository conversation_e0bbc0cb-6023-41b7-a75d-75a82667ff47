# frozen_string_literal: true

class Facebook::Messenger::AutomatedResponses
  def initialize(page)
    @page = page
    @user = page.user
  end

  def process_message_for_automation(facebook_message)
    return unless should_auto_respond?(facebook_message)

    begin
      response_type = determine_response_type(facebook_message)
      
      case response_type
      when 'greeting'
        send_greeting_response(facebook_message)
      when 'business_inquiry'
        send_business_inquiry_response(facebook_message)
      when 'lead_qualification'
        send_lead_qualification_response(facebook_message)
      when 'contact_request'
        send_contact_information_response(facebook_message)
      when 'pricing_inquiry'
        send_pricing_response(facebook_message)
      when 'support_request'
        send_support_response(facebook_message)
      end
      
    rescue => e
      Rails.logger.error "Automated response error: #{e.message}"
    end
  end

  def setup_lead_qualification_flow(qualification_questions)
    flow_config = {
      'lead_qualification_flow' => {
        'enabled' => true,
        'questions' => qualification_questions,
        'trigger_keywords' => ['interested', 'more info', 'tell me more', 'details']
      }
    }
    
    update_automation_config(flow_config)
  end

  def setup_appointment_booking_flow(booking_config)
    flow_config = {
      'appointment_booking_flow' => {
        'enabled' => true,
        'calendar_link' => booking_config[:calendar_link],
        'available_times' => booking_config[:available_times],
        'trigger_keywords' => ['book', 'appointment', 'schedule', 'meeting']
      }
    }
    
    update_automation_config(flow_config)
  end

  private

  def should_auto_respond?(message)
    return false if message.is_from_page?
    return false if message.created_time < 5.minutes.ago # Don't respond to old messages
    
    # Check if we've already responded recently
    recent_responses = message.facebook_conversation.facebook_messages
                             .where(is_from_page: true)
                             .where('created_time > ?', 1.hour.ago)
    
    recent_responses.count < 3 # Limit to 3 auto-responses per hour
  end

  def determine_response_type(message)
    text = message.message.downcase
    
    # Greeting detection
    greeting_keywords = ['hi', 'hello', 'hey', 'good morning', 'good afternoon', 'good evening']
    return 'greeting' if greeting_keywords.any? { |keyword| text.include?(keyword) }
    
    # Business inquiry detection
    business_keywords = ['business', 'service', 'company', 'work', 'professional']
    return 'business_inquiry' if business_keywords.any? { |keyword| text.include?(keyword) }
    
    # Lead qualification triggers
    lead_keywords = ['interested', 'more info', 'tell me more', 'details', 'learn more']
    return 'lead_qualification' if lead_keywords.any? { |keyword| text.include?(keyword) }
    
    # Contact request detection
    contact_keywords = ['contact', 'phone', 'email', 'reach out', 'get in touch']
    return 'contact_request' if contact_keywords.any? { |keyword| text.include?(keyword) }
    
    # Pricing inquiry detection
    pricing_keywords = ['price', 'cost', 'how much', 'quote', 'estimate', 'pricing']
    return 'pricing_inquiry' if pricing_keywords.any? { |keyword| text.include?(keyword) }
    
    # Support request detection
    support_keywords = ['help', 'support', 'problem', 'issue', 'question']
    return 'support_request' if support_keywords.any? { |keyword| text.include?(keyword) }
    
    nil
  end

  def send_greeting_response(message)
    response_text = get_automation_config('greeting_response') || 
                   "Hi there! 👋 Thanks for reaching out to #{@page.name}. How can we help you today?"
    
    send_automated_message(message.facebook_conversation, response_text)
  end

  def send_business_inquiry_response(message)
    response_data = {
      text: "Thanks for your interest in our business! I'd love to learn more about your needs.",
      quick_replies: [
        {
          content_type: 'text',
          title: 'Learn About Services',
          payload: 'SERVICES_INFO'
        },
        {
          content_type: 'text',
          title: 'Get a Quote',
          payload: 'REQUEST_QUOTE'
        },
        {
          content_type: 'text',
          title: 'Schedule Call',
          payload: 'SCHEDULE_CALL'
        }
      ]
    }
    
    send_automated_message(message.facebook_conversation, response_data)
  end

  def send_lead_qualification_response(message)
    qualification_flow = get_automation_config('lead_qualification_flow')
    
    if qualification_flow && qualification_flow['enabled']
      first_question = qualification_flow['questions'].first
      send_automated_message(message.facebook_conversation, first_question['text'])
      
      # Mark conversation for lead qualification tracking
      message.facebook_conversation.update!(
        conversation_context: message.facebook_conversation.conversation_context.merge(
          'qualification_flow_active' => true,
          'current_question_index' => 0
        )
      )
    else
      default_response = "I'd love to learn more about your needs! What specific information are you looking for?"
      send_automated_message(message.facebook_conversation, default_response)
    end
  end

  def send_contact_information_response(message)
    contact_info = build_contact_info_message
    send_automated_message(message.facebook_conversation, contact_info)
  end

  def send_pricing_response(message)
    pricing_response = get_automation_config('pricing_response') || 
                      "Thanks for your interest in our pricing! I'll send you our pricing information right away."
    
    # Send pricing template if configured
    pricing_template = get_automation_config('pricing_template')
    if pricing_template
      send_template_message(message.facebook_conversation, 'generic', pricing_template)
    else
      send_automated_message(message.facebook_conversation, pricing_response)
    end
  end

  def send_support_response(message)
    support_response = "I'm here to help! Can you tell me more about what you need assistance with?"
    
    response_data = {
      text: support_response,
      quick_replies: [
        {
          content_type: 'text',
          title: 'Technical Issue',
          payload: 'TECH_SUPPORT'
        },
        {
          content_type: 'text',
          title: 'Billing Question',
          payload: 'BILLING_SUPPORT'
        },
        {
          content_type: 'text',
          title: 'General Question',
          payload: 'GENERAL_SUPPORT'
        }
      ]
    }
    
    send_automated_message(message.facebook_conversation, response_data)
  end

  def send_automated_message(conversation, message_data)
    bot_management = Facebook::Messenger::BotManagement.new(@page)
    
    if message_data.is_a?(String)
      message_data = { text: message_data }
    end
    
    result = bot_management.send_message(conversation.participant_id, message_data)
    
    if result[:success]
      # Mark as automated response
      conversation.facebook_messages.where(message_id: result[:message_id]).update_all(
        extracted_data: { automated_response: true }
      )
    end
    
    result
  end

  def send_template_message(conversation, template_type, template_data)
    bot_management = Facebook::Messenger::BotManagement.new(@page)
    bot_management.send_template_message(conversation.participant_id, template_type, template_data)
  end

  def build_contact_info_message
    contact_elements = []
    
    # Add phone if available
    if @page.page_data.dig('contact_info', 'phone')
      contact_elements << "📞 Phone: #{@page.page_data['contact_info']['phone']}"
    end
    
    # Add email if available
    if @page.page_data.dig('contact_info', 'email')
      contact_elements << "📧 Email: #{@page.page_data['contact_info']['email']}"
    end
    
    # Add website if available
    if @page.page_data.dig('contact_info', 'website')
      contact_elements << "🌐 Website: #{@page.page_data['contact_info']['website']}"
    end
    
    if contact_elements.any?
      "Here's how you can reach us:\n\n#{contact_elements.join("\n")}"
    else
      "Thanks for wanting to get in touch! Someone from our team will reach out to you soon."
    end
  end

  def get_automation_config(key)
    @page.page_data.dig('messenger_automation', key)
  end

  def update_automation_config(new_config)
    current_config = @page.page_data['messenger_automation'] || {}
    updated_config = current_config.merge(new_config)
    
    @page.update!(
      page_data: @page.page_data.merge('messenger_automation' => updated_config)
    )
  end
end

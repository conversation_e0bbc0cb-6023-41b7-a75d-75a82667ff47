# frozen_string_literal: true

class Facebook::DataFetching::PostsData
  def initialize(page)
    @page = page
    @user = page.user
  end

  def call
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      posts_data = fetch_posts_list
      
      if posts_data
        process_posts_data(posts_data)
        { success: true, count: posts_data.length }
      else
        { success: false, error: 'Failed to fetch posts data' }
      end
    rescue => e
      Rails.logger.error "Facebook posts data fetch error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @user.connected_account&.access_token.present?
  end

  def fetch_posts_list
    access_token = @user.connected_account.access_token
    
    url = "https://graph.facebook.com/v18.0/#{@page.source_id}/posts"
    params = {
      fields: 'id,message,story,link,picture,created_time,type,likes.summary(true),comments.summary(true),shares,reactions.summary(true),insights.metric(post_impressions,post_reach)',
      limit: 50,
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    data['data'] if data
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Facebook posts API error: #{e.response.body}"
    handle_api_error(e)
    nil
  end

  def process_posts_data(posts_list)
    posts_list.each do |post_item|
      create_or_update_post(post_item)
    end
  end

  def create_or_update_post(post_data)
    post = @page.facebook_posts.find_or_initialize_by(post_id: post_data['id'])
    
    post.assign_attributes(
      message: post_data['message'],
      story: post_data['story'],
      link: post_data['link'],
      picture: post_data['picture'],
      post_type: post_data['type'] || 'status',
      published_at: parse_timestamp(post_data['created_time']),
      likes_count: extract_count(post_data, 'likes'),
      comments_count: extract_count(post_data, 'comments'),
      shares_count: post_data.dig('shares', 'count') || 0,
      reactions_count: extract_count(post_data, 'reactions'),
      reactions_breakdown: extract_reactions_breakdown(post_data),
      reach: extract_insights_metric(post_data, 'post_reach'),
      impressions: extract_insights_metric(post_data, 'post_impressions'),
      is_published: true
    )

    if post.save
      # Schedule comment fetching for posts with comments
      if post.comments_count > 0
        Facebook::DataFetching::CommentSyncJob.perform_later(post.id)
      end
      
      # Schedule detailed insights fetching
      Facebook::DataFetching::PostInsightsJob.perform_later(post.id)
    end

    post
  end

  def extract_count(post_data, field)
    post_data.dig(field, 'summary', 'total_count') || 0
  end

  def extract_reactions_breakdown(post_data)
    reactions = post_data.dig('reactions', 'data') || []
    breakdown = {}
    
    reactions.each do |reaction|
      type = reaction['type']
      breakdown[type] = (breakdown[type] || 0) + 1
    end
    
    breakdown
  end

  def extract_insights_metric(post_data, metric_name)
    insights = post_data.dig('insights', 'data') || []
    metric = insights.find { |i| i['name'] == metric_name }
    metric&.dig('values', 0, 'value') || 0
  end

  def parse_timestamp(timestamp_str)
    return nil unless timestamp_str
    
    Time.parse(timestamp_str)
  rescue ArgumentError
    nil
  end

  def handle_api_error(error)
    error_data = JSON.parse(error.response.body) rescue {}
    error_message = error_data.dig('error', 'message') || 'Unknown API error'
    
    case error.response.code
    when 401
      Rails.logger.warn "Facebook token expired for user: #{@user.id}"
    when 403
      Rails.logger.warn "Facebook permission denied for page: #{@page.source_id}"
    when 429
      Rails.logger.warn "Facebook rate limited for page: #{@page.source_id}"
    end
  end
end

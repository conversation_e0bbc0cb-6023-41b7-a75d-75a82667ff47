# frozen_string_literal: true

class Facebook::DataFetching::EventsData
  def initialize(page)
    @page = page
    @user = page.user
  end

  def call
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      events_data = fetch_events_list
      
      if events_data
        process_events_data(events_data)
        { success: true, count: events_data.length }
      else
        { success: false, error: 'Failed to fetch events data' }
      end
    rescue => e
      Rails.logger.error "Facebook events data fetch error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @user.connected_account&.access_token.present?
  end

  def fetch_events_list
    access_token = @user.connected_account.access_token
    
    url = "https://graph.facebook.com/v18.0/#{@page.source_id}/events"
    params = {
      fields: 'id,name,description,start_time,end_time,timezone,place,cover,type,category,attending_count,declined_count,maybe_count,noreply_count,interested_count,is_canceled,is_draft,is_online,ticket_uri',
      limit: 50,
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    data['data'] if data
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Facebook events API error: #{e.response.body}"
    handle_api_error(e)
    nil
  end

  def process_events_data(events_list)
    events_list.each do |event_item|
      create_or_update_event(event_item)
    end
  end

  def create_or_update_event(event_data)
    event = @page.facebook_events.find_or_initialize_by(event_id: event_data['id'])
    
    event.assign_attributes(
      name: event_data['name'],
      description: event_data['description'],
      start_time: parse_timestamp(event_data['start_time']),
      end_time: parse_timestamp(event_data['end_time']),
      timezone: event_data['timezone'],
      location: extract_location(event_data),
      venue_id: event_data.dig('place', 'id'),
      cover_photo_url: event_data.dig('cover', 'source'),
      event_type: event_data['type'] || 'public',
      category: event_data['category'],
      attending_count: event_data['attending_count'] || 0,
      declined_count: event_data['declined_count'] || 0,
      maybe_count: event_data['maybe_count'] || 0,
      noreply_count: event_data['noreply_count'] || 0,
      interested_count: event_data['interested_count'] || 0,
      is_canceled: event_data['is_canceled'] || false,
      is_draft: event_data['is_draft'] || false,
      is_online: event_data['is_online'] || false,
      ticket_uri: event_data['ticket_uri'],
      place_data: extract_place_data(event_data)
    )

    if event.save
      # Set monitoring based on lead potential
      event.update!(is_monitored: event.should_monitor_for_leads?)
      
      # Schedule attendee processing for high-potential events
      if event.is_monitored? && event.total_responses > 10
        Facebook::LeadProcessing::EventAttendeesProcessorJob.perform_later(event.id)
      end
      
      # Schedule insights fetching
      Facebook::DataFetching::EventInsightsJob.perform_later(event.id)
    end

    event
  end

  def extract_location(event_data)
    place = event_data['place']
    return nil unless place
    
    location_parts = []
    location_parts << place['name'] if place['name']
    
    if place['location']
      location = place['location']
      address_parts = []
      address_parts << location['street'] if location['street']
      address_parts << location['city'] if location['city']
      address_parts << location['state'] if location['state']
      address_parts << location['country'] if location['country']
      
      location_parts << address_parts.join(', ') if address_parts.any?
    end
    
    location_parts.join(' - ')
  end

  def extract_place_data(event_data)
    place = event_data['place']
    return {} unless place
    
    {
      'id' => place['id'],
      'name' => place['name'],
      'location' => place['location'] || {},
      'overall_rating' => place['overall_rating'],
      'category_list' => place['category_list'] || []
    }
  end

  def parse_timestamp(timestamp_str)
    return nil unless timestamp_str
    
    Time.parse(timestamp_str)
  rescue ArgumentError
    nil
  end

  def handle_api_error(error)
    error_data = JSON.parse(error.response.body) rescue {}
    error_message = error_data.dig('error', 'message') || 'Unknown API error'
    
    case error.response.code
    when 401
      Rails.logger.warn "Facebook token expired for user: #{@user.id}"
    when 403
      Rails.logger.warn "Facebook permission denied for events on page: #{@page.source_id}"
    when 429
      Rails.logger.warn "Facebook rate limited for events fetch"
    end
  end
end

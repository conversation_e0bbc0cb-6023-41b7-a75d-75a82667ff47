# frozen_string_literal: true

class Facebook::DataFetching::CommentData
  def initialize(facebook_post)
    @post = facebook_post
    @page = @post.page
    @user = @page.user
  end

  def call
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      comments_data = fetch_comments
      
      if comments_data
        process_comments_data(comments_data)
        { success: true, count: comments_data.length }
      else
        { success: false, error: 'Failed to fetch comments data' }
      end
    rescue => e
      Rails.logger.error "Facebook comments fetch error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @user.connected_account&.access_token.present?
  end

  def fetch_comments
    access_token = @user.connected_account.access_token
    
    url = "https://graph.facebook.com/v18.0/#{@post.post_id}/comments"
    params = {
      fields: 'id,message,from,created_time,like_count,comment_count,can_hide,can_remove,can_like,user_likes,parent',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    data['data'] if data
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Facebook comments API error: #{e.response.body}"
    handle_api_error(e)
    nil
  end

  def process_comments_data(comments_list)
    comments_list.each do |comment_data|
      create_or_update_comment(comment_data)
    end
  end

  def create_or_update_comment(comment_data)
    comment = @post.facebook_comments.find_or_initialize_by(comment_id: comment_data['id'])
    
    comment.assign_attributes(
      message: comment_data['message'],
      from_id: comment_data.dig('from', 'id'),
      from_name: comment_data.dig('from', 'name'),
      commented_at: parse_timestamp(comment_data['created_time']),
      like_count: comment_data['like_count'] || 0,
      comment_count: comment_data['comment_count'] || 0,
      can_hide: comment_data['can_hide'] || false,
      can_remove: comment_data['can_remove'] || false,
      can_like: comment_data['can_like'] || false,
      user_likes: comment_data['user_likes'] || false,
      parent_comment_id: comment_data.dig('parent', 'id'),
      user_profile_data: extract_user_profile(comment_data['from'])
    )

    if comment.save
      # Schedule lead processing for new comments
      if comment.message.present? && !comment.is_processed?
        Facebook::LeadProcessing::CommentAnalysisJob.perform_later(comment.id)
      end
      
      # Fetch replies if any
      if comment.comment_count > 0
        fetch_comment_replies(comment)
      end
    end

    comment
  end

  def fetch_comment_replies(parent_comment)
    access_token = @user.connected_account.access_token
    
    url = "https://graph.facebook.com/v18.0/#{parent_comment.comment_id}/comments"
    params = {
      fields: 'id,message,from,created_time,like_count,user_likes',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    
    if data && data['data']
      data['data'].each do |reply_data|
        create_comment_reply(reply_data, parent_comment)
      end
    end
  rescue => e
    Rails.logger.error "Failed to fetch comment replies: #{e.message}"
  end

  def create_comment_reply(reply_data, parent_comment)
    reply = @post.facebook_comments.find_or_initialize_by(comment_id: reply_data['id'])
    
    reply.assign_attributes(
      message: reply_data['message'],
      from_id: reply_data.dig('from', 'id'),
      from_name: reply_data.dig('from', 'name'),
      commented_at: parse_timestamp(reply_data['created_time']),
      like_count: reply_data['like_count'] || 0,
      user_likes: reply_data['user_likes'] || false,
      parent_comment_id: parent_comment.comment_id,
      user_profile_data: extract_user_profile(reply_data['from'])
    )

    if reply.save && reply.message.present?
      Facebook::LeadProcessing::CommentAnalysisJob.perform_later(reply.id)
    end
  end

  def extract_user_profile(user_data)
    return {} unless user_data

    {
      'id' => user_data['id'],
      'name' => user_data['name']
    }
  end

  def parse_timestamp(timestamp_str)
    return nil unless timestamp_str
    
    Time.parse(timestamp_str)
  rescue ArgumentError
    nil
  end

  def handle_api_error(error)
    error_data = JSON.parse(error.response.body) rescue {}
    
    case error.response.code
    when 401
      Rails.logger.warn "Facebook token expired for user: #{@user.id}"
    when 403
      Rails.logger.warn "Facebook permission denied for comments on post: #{@post.post_id}"
    when 429
      Rails.logger.warn "Facebook rate limited for comments fetch"
    end
  end
end

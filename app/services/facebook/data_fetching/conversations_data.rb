# frozen_string_literal: true

class Facebook::DataFetching::ConversationsData
  def initialize(page)
    @page = page
    @user = page.user
  end

  def call
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      conversations_data = fetch_conversations_list
      
      if conversations_data
        process_conversations_data(conversations_data)
        { success: true, count: conversations_data.length }
      else
        { success: false, error: 'Failed to fetch conversations data' }
      end
    rescue => e
      Rails.logger.error "Facebook conversations data fetch error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @user.connected_account&.access_token.present?
  end

  def fetch_conversations_list
    access_token = @user.connected_account.access_token
    
    url = "https://graph.facebook.com/v18.0/#{@page.source_id}/conversations"
    params = {
      fields: 'id,participants,updated_time,message_count,unread_count,can_reply,is_subscribed',
      limit: 50,
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    data['data'] if data
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Facebook conversations API error: #{e.response.body}"
    handle_api_error(e)
    nil
  end

  def process_conversations_data(conversations_list)
    conversations_list.each do |conversation_item|
      create_or_update_conversation(conversation_item)
    end
  end

  def create_or_update_conversation(conversation_data)
    conversation = @page.facebook_conversations.find_or_initialize_by(conversation_id: conversation_data['id'])
    
    participant = extract_participant(conversation_data)
    
    conversation.assign_attributes(
      participant_id: participant[:id],
      participant_name: participant[:name],
      updated_time: parse_timestamp(conversation_data['updated_time']),
      message_count: conversation_data['message_count'] || 0,
      unread_count: conversation_data['unread_count'] || 0,
      can_reply: conversation_data['can_reply'] || false,
      is_subscribed: conversation_data['is_subscribed'] || false,
      participant_profile_data: participant[:profile_data]
    )

    if conversation.save
      # Fetch messages for this conversation
      Facebook::DataFetching::MessagesSyncJob.perform_later(conversation.id)
      
      # Schedule lead analysis if there are messages
      if conversation.message_count > 0
        Facebook::LeadProcessing::ConversationAnalysisJob.perform_later(conversation.id)
      end
    end

    conversation
  end

  def extract_participant(conversation_data)
    participants = conversation_data['participants'] || []
    
    # Find the participant that's not the page
    participant = participants.find { |p| p['id'] != @page.source_id }
    
    if participant
      {
        id: participant['id'],
        name: participant['name'],
        profile_data: {
          'id' => participant['id'],
          'name' => participant['name'],
          'email' => participant['email']
        }
      }
    else
      {
        id: 'unknown',
        name: 'Unknown User',
        profile_data: {}
      }
    end
  end

  def parse_timestamp(timestamp_str)
    return nil unless timestamp_str
    
    Time.parse(timestamp_str)
  rescue ArgumentError
    nil
  end

  def handle_api_error(error)
    error_data = JSON.parse(error.response.body) rescue {}
    error_message = error_data.dig('error', 'message') || 'Unknown API error'
    
    case error.response.code
    when 401
      Rails.logger.warn "Facebook token expired for user: #{@user.id}"
    when 403
      Rails.logger.warn "Facebook permission denied for conversations on page: #{@page.source_id}"
    when 429
      Rails.logger.warn "Facebook rate limited for conversations fetch"
    end
  end
end

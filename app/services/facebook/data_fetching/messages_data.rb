# frozen_string_literal: true

class Facebook::DataFetching::MessagesData
  def initialize(facebook_conversation)
    @conversation = facebook_conversation
    @page = @conversation.page
    @user = @page.user
  end

  def call
    return { success: false, error: 'No access token' } unless valid_token?

    begin
      messages_data = fetch_messages
      
      if messages_data
        process_messages_data(messages_data)
        { success: true, count: messages_data.length }
      else
        { success: false, error: 'Failed to fetch messages data' }
      end
    rescue => e
      Rails.logger.error "Facebook messages fetch error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @user.connected_account&.access_token.present?
  end

  def fetch_messages
    access_token = @user.connected_account.access_token
    
    url = "https://graph.facebook.com/v18.0/#{@conversation.conversation_id}/messages"
    params = {
      fields: 'id,message,from,to,created_time,attachments',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    data['data'] if data
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Facebook messages API error: #{e.response.body}"
    handle_api_error(e)
    nil
  end

  def process_messages_data(messages_list)
    messages_list.each do |message_data|
      create_or_update_message(message_data)
    end
  end

  def create_or_update_message(message_data)
    message = @conversation.facebook_messages.find_or_initialize_by(message_id: message_data['id'])
    
    # Determine if message is from page
    from_id = message_data.dig('from', 'id')
    is_from_page = from_id == @page.source_id
    
    message.assign_attributes(
      message: message_data['message'],
      from_id: from_id,
      from_name: message_data.dig('from', 'name'),
      to_id: message_data.dig('to', 'id'),
      to_name: message_data.dig('to', 'name'),
      created_time: parse_timestamp(message_data['created_time']),
      is_from_page: is_from_page,
      message_type: determine_message_type(message_data),
      attachment_url: extract_attachment_url(message_data),
      attachment_type: extract_attachment_type(message_data),
      attachments_data: extract_attachments_data(message_data)
    )

    if message.save
      # Process message for lead potential if from customer
      if !is_from_page && message.message.present?
        Facebook::LeadProcessing::MessageAnalysisJob.perform_later(message.id)
      end
    end

    message
  end

  def determine_message_type(message_data)
    if message_data['attachments'].present?
      attachment = message_data['attachments'].first
      case attachment['type']
      when 'image'
        'image'
      when 'video'
        'video'
      when 'audio'
        'audio'
      when 'file'
        'file'
      else
        'text'
      end
    else
      'text'
    end
  end

  def extract_attachment_url(message_data)
    attachment = message_data.dig('attachments', 0)
    return nil unless attachment

    attachment.dig('image_data', 'url') || 
    attachment.dig('video_data', 'url') || 
    attachment.dig('file_url')
  end

  def extract_attachment_type(message_data)
    attachment = message_data.dig('attachments', 0)
    return nil unless attachment

    attachment['type']
  end

  def extract_attachments_data(message_data)
    attachments = message_data['attachments'] || []
    
    attachments.map do |attachment|
      {
        'type' => attachment['type'],
        'name' => attachment['name'],
        'size' => attachment['size'],
        'url' => attachment['image_data']&.dig('url') || attachment['video_data']&.dig('url') || attachment['file_url'],
        'mime_type' => attachment['mime_type']
      }
    end
  end

  def parse_timestamp(timestamp_str)
    return nil unless timestamp_str
    
    Time.parse(timestamp_str)
  rescue ArgumentError
    nil
  end

  def handle_api_error(error)
    error_data = JSON.parse(error.response.body) rescue {}
    
    case error.response.code
    when 401
      Rails.logger.warn "Facebook token expired for user: #{@user.id}"
    when 403
      Rails.logger.warn "Facebook permission denied for messages in conversation: #{@conversation.conversation_id}"
    when 429
      Rails.logger.warn "Facebook rate limited for messages fetch"
    end
  end
end

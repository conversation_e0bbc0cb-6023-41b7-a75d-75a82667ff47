
class FetchFormAttributes

  def initialize(form)
    @form = form
  end

  def call
    fetch_form_attributes
  end

  private

  def fetch_form_attributes
    page_graph = Koala::Facebook::API.new(@form.page.access_token)
    form_attributes = page_graph.get_object("#{@form.source_id}?fields=id,questions").with_indifferent_access[:questions]

    conditional_questions = []
    form_attributes&.each do |attribute|
      if attribute['key'] == 'conditional_question_1'
        conditional_questions = attribute['dependent_conditional_questions']&.map { |dependent_field| { key: dependent_field['field_key'], label: dependent_field['name'] } }
      end
    end

    form_attributes += conditional_questions if conditional_questions.present?
    form_attributes + STATIC_FB_FORM_ATTRIBUTES
  end
end

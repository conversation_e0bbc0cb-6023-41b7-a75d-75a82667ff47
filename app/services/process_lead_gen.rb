class ProcessLeadGen

  def initialize(params, failed_lead = nil)
    params = params.first
    @page_id = params["page_id"]
    @form_id = params["form_id"]
    @leadgen_id = params["leadgen_id"]
    @connected_account = nil
    @form = nil
    @failed_lead = failed_lead
    @entity_type = nil
  end

  def call
    if @failed_lead
      @form = @failed_lead.form
      process_lead
    else
      Form.where(source_id: @form_id, saved_by_user: true).each do |form|
        @form = form
        process_lead
      end
    end
  end

  private

  def process_lead
    @mapped_fields     = @form.mapped_fields
    @connected_account = @form.user.connected_account
    data = fetch_lead
    return false if data.blank?

    response_status = save_lead_to_kylas data if @form.create_lead_check
    response_status = save_contact_to_kylas data if @form.create_contact_check

    response_status
  end

  def fetch_lead
    user_graph = Koala::Facebook::API.new(@connected_account.refresh_token)
    begin
      data = user_graph.get_object("#{@leadgen_id}?fields=ad_name,ad_id,campaign_name,campaign_id,adset_name,adset_id,field_data,created_time,form_id,platform")

      return data
    rescue Exception => e
      Rails.logger.info "Exception while fetching data of lead #{@leadgen_id} : #{e.to_s}"
      failed_lead = FailedLead.find_by(lead_gen_id: @leadgen_id, form_id: @form.id)
      
      if failed_lead
        failed_lead.update(error: e.to_s, updated_at: Time.current)
      else
        FailedLead.create(lead_gen_id: @leadgen_id, form_id: @form.id, error: e.to_s)
      end
      nil
    end
  end
  def save_lead_to_kylas data
    raw_data = {}
    @entity_type = LEAD

    begin
      data['field_data'].each do |field|
        raw_data = get_raw_data_for_custom_fields(raw_data, field)
      end

      lead_utm_fields = @form.lead_utm_fields
      raw_data['source'] = lead_utm_fields.kylas_source_id
      raw_data['campaign'] = lead_utm_fields.campaign_id
      raw_data['products'] = set_products
      raw_data['ownerId'] = lead_utm_fields.owner_id

      raw_data = get_custom_data_from_lead_gen(raw_data, data)

      raw_data['subSource'] = lead_utm_fields.sub_source unless raw_data['subSource'].present?
      raw_data['utmSource'] = lead_utm_fields.utm_source unless raw_data['utmSource'].present?
      raw_data['utmCampaign'] = lead_utm_fields.utm_campaign unless raw_data['utmCampaign'].present?
      raw_data['utmMedium'] = lead_utm_fields.utm_medium unless raw_data['utmMedium'].present?
      raw_data['utmContent'] = lead_utm_fields.utm_content unless raw_data['utmContent'].present?
      raw_data['utmTerm'] = lead_utm_fields.utm_term unless raw_data['utmTerm'].present?

      result = Kylas::CreateLeadInKylas.new(@connected_account.user, raw_data).call

      if result[:success]
        lead_gen_log = FailedLead.find_or_create_by(lead_gen_id: @leadgen_id, form_id: @form.id)
        lead_gen_log.update(status: 1, kylas_lead_id: result[:id])
      end

      result[:success] ? (return true) : (raise result[:error_message])
    rescue Exception => e
      Rails.logger.info "Exception while sending lead to kylas: #{e.to_s} #{raw_data}"
      @failed_lead.present? ? @failed_lead.update(raw_data: raw_data, error: e.to_s) : FailedLead.create(lead_gen_id: @leadgen_id, form_id: @form.id, raw_data: raw_data, error: e.to_s)

      return false
    end
  end
  def save_contact_to_kylas data
    raw_data = {}
    @entity_type = CONTACT

    begin
      data['field_data'].each do |field|
        raw_data = get_raw_data_for_custom_fields(raw_data, field)
      end

      contact_utm_fields = @form.contact_utm_fields
      raw_data['source'] = contact_utm_fields.kylas_source_id
      raw_data['campaign'] = contact_utm_fields.campaign_id
      raw_data['subSource'] = contact_utm_fields.sub_source unless raw_data['subSource'].present?
      raw_data['utmSource'] = contact_utm_fields.utm_source unless raw_data['utmSource'].present?
      raw_data['utmCampaign'] = contact_utm_fields.utm_campaign unless raw_data['utmCampaign'].present?
      raw_data['utmMedium'] = contact_utm_fields.utm_medium unless raw_data['utmMedium'].present?
      raw_data['utmContent'] = contact_utm_fields.utm_content unless raw_data['utmContent'].present?
      raw_data['utmTerm'] = contact_utm_fields.utm_term unless raw_data['utmTerm'].present?

      result = Kylas::CreateContactInKylas.new(@connected_account.user, raw_data).call

      if result[:success]
        contact_gen_log = FailedLead.find_or_create_by(lead_gen_id: @leadgen_id, form_id: @form.id)
        contact_gen_log.update(status: 1, kylas_contact_id: result[:id])
      end

      result[:success] ? (return true) : (raise result[:error_message])
    rescue Exception => e
      Rails.logger.info "Exception while sending contact to kylas: #{e.to_s} #{raw_data}"

      return false
    end
  end
  def set_products
    return [] if @form.product_id.blank?

    kylas_products = Kylas::GetProducts.new(@connected_account.user).by_ids([@form.product_id])[:products]

    return [] if kylas_products.blank?

    selected_product = kylas_products.select {|product| product['id'].to_s == @form.product_id.to_s}[0]
    selected_product.present? ? [{id: @form.product_id, name: selected_product['name']}] : []
  end

  def get_raw_data_for_custom_fields(raw_data, field)
    return updated_raw_data(raw_data, field['name'], field['values'].try(:first))
  end

  def get_custom_data_from_lead_gen(raw_data, data)
    STATIC_FB_FORM_ATTRIBUTES.each do |attr|
      raw_data = updated_raw_data(raw_data, attr[:key], data[attr[:key]])
    end

    raw_data = updated_raw_data(raw_data, 'leadgen_id', @leadgen_id)
  end

  def updated_raw_data(raw_data, fb_attr_name, value)
    mapped_field = get_kylas_mapped_field(fb_attr_name)
    custom_mapped_kylas_key = mapped_field&.kylas_field_name || nil

    return raw_data if custom_mapped_kylas_key.blank?

    if(mapped_field.is_custom_kylas_attribute)
      raw_data['customFieldValues'] = {} unless raw_data['customFieldValues'].present?
      if mapped_field.kylas_field_type == 'PICK_LIST'
        custom_picklist_value = get_picklist_value(custom_mapped_kylas_key, mapped_field, value)
        raw_data['customFieldValues'][custom_mapped_kylas_key] = custom_picklist_value if custom_picklist_value.present?
      else
        raw_data['customFieldValues'][custom_mapped_kylas_key] = get_raw_data_value(custom_mapped_kylas_key, value)
      end
    else
      if mapped_field.kylas_field_type == 'PICK_LIST'
        raw_data[custom_mapped_kylas_key] = get_picklist_value(custom_mapped_kylas_key, mapped_field, value)
      else
        raw_data[custom_mapped_kylas_key] = get_raw_data_value(custom_mapped_kylas_key, value)
      end
    end

    raw_data
  end

  def get_raw_data_value(key, value)
    return value  unless ['companyphones', 'phonenumbers', 'emails'].include?(key.downcase)

    if key.downcase.eql? 'emails'
      [{type: 'OFFICE', primary: true, value: value}]
    else
      phone = Phonelib.parse(value, @connected_account.user.country)
      phone.raw_national.present? ? [{type: 'MOBILE', code: phone.country, dialCode: phone.country_code, value: phone.raw_national}] : nil
    end
  end

  def get_kylas_mapped_field(key)
    @mapped_fields.where("LOWER(fb_field_name) = ? AND entity_type = ?", key.downcase, @entity_type).first
  end

  def get_picklist_value(custom_mapped_kylas_key, mapped_field, facebook_field_value)
    picklist = {}
    picklist_value = nil

    if mapped_field.entity_type == LEAD
      @lead_fields_response ||= Kylas::EntityFields.new(@connected_account.user, LEAD).fetch

      picklist = @lead_fields_response[:data].detect { |field| field['name'] == custom_mapped_kylas_key } if @lead_fields_response[:success]
    elsif mapped_field.entity_type == CONTACT
      @contact_fields_response ||= Kylas::EntityFields.new(@connected_account.user, CONTACT).fetch

      picklist = @contact_fields_response[:data].detect { |field| field['name'] == custom_mapped_kylas_key } if @contact_fields_response[:success]
    end

    facebook_field_value = facebook_field_value.is_a?(String) ? facebook_field_value.downcase : facebook_field_value

    mapped_picklist = picklist&.dig('picklist', 'values')&.detect { |picklist_value|
      picklist_value['displayName']&.downcase == facebook_field_value
    }

    if STANDARD_PICKLIST_FIELDS.include?(custom_mapped_kylas_key)
      picklist_value = mapped_picklist['name'] if mapped_picklist.present?
    else
      picklist_value = mapped_picklist['id'] if mapped_picklist.present?
    end
    picklist_value
  end
end

# frozen_string_literal: true
class FetchAndSaveForms

  def initialize user
    @user =  user
  end
  def call
    return [] unless @user.pages
    return [] unless @user.connected_account.try(:is_active).present?
    forms = fetch_forms
  end

  private

  def fetch_forms
    forms = []
    @user.pages.each do |page|
      page_graph = Koala::Facebook::API.new(page.access_token)
      page_forms = page_graph.get_connection('me', 'leadgen_forms')
      forms << page_forms.map { |f| build_or_fetch_form(f, page) }
      loop do
        page_forms = page_forms.next_page
        break if page_forms.blank?
        forms << page_forms.map { |f| build_or_fetch_form(f, page) }
      end
    end
    forms.flatten.compact
  end

  def build_or_fetch_form f, page
    return nil unless f['status'].eql?('ACTIVE')
    Form.find_or_create_by(source_id: f['id'], user_id: @user.id, page_id: page.id, name: f['name'])
  end
end

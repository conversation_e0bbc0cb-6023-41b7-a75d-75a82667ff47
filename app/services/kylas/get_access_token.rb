require 'rest-client'

module <PERSON><PERSON><PERSON>

  class GetAccessToken
    def initialize(refresh_token)
      @refresh_token = refresh_token
    end

    def call
      begin
        url = APP_KYLAS_HOST + "/oauth/token"
        cred = "#{Rails.application.credentials.kylas[:app_id]}:#{Rails.application.credentials.kylas[:app_secret]}"
        encoded_credentials = Base64::encode64(cred).gsub("\n",'')

        url = "#{url}?grant_type=refresh_token&refresh_token=#{@refresh_token}" 
        response = RestClient.post(url, 
                                   {}.to_json,
                                   { 'Authorization' => "Basic #{encoded_credentials}", 'Content-Type' => 'application/x-www-form-urlencoded'})
        if response.code.eql?(200)
          res = JSON.parse(response.body)
          { success: true, 
            access_token: res["access_token"],
            refresh_token: res["refresh_token"],
            expires_in: (DateTime.now.to_i + res["expires_in"])
          }
        else
          { success: false, error_message: response.body }
        end
      rescue RestClient::NotFound
        Rails.logger.error "Create Lead - 404"
        { error_message: 'Invalid Data!', success: false }
      rescue RestClient::InternalServerError
        Rails.logger.error "Create Lead - 500"
        { error_message: 'Internal server error!', success: false }
      rescue RestClient::BadRequest
        Rails.logger.error "Create Lead - 400"
        { error_message: 'Invalid Data!', success: false }
      end
    end
  end
end

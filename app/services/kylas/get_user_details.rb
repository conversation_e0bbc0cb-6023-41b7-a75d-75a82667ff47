require 'rest-client'
include UserAgentHelper

module Kylas
  class GetUserDetails

    def initialize(api_key: nil, token: nil, user:)
      @api_key = api_key
      @token = token
      @user = user
    end

    def call
      fetch
    end

    private

    def fetch
      begin
        body = request_and_get_data

        { id: body['id'], tenant_id: body['tenantId'] }
      rescue RestClient::NotFound
        Rails.logger.error "Get User details - 404"
        { error_message: 'Invalid Data!' }
      rescue RestClient::InternalServerError
        Rails.logger.error "Get User details - 500"
        { error_message: 'Internal server error!' }
      rescue RestClient::BadRequest
        Rails.logger.error "Get User details - 400"
        { error_message: 'Invalid Data!' }
      end
    end

    def request_and_get_data
      url = APP_KYLAS_HOST + "/v1/users/me"

      if @token
        response = RestClient.get(url, {
          'Authorization' => "Bearer #{@token}",
          content_type: :json,
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @user.kylas_tenant_id, kylas_user_id: @user.kylas_user_id)
        })
      else
        response = RestClient.get(url, {
          'api-key': @api_key,
          content_type: :json,
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @user.kylas_tenant_id, kylas_user_id: @user.kylas_user_id, api_key: true)
        })
      end

      response.body ? JSON.parse(response.body).with_indifferent_access : {}
    end
  end
end

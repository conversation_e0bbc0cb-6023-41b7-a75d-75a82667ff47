require 'rest-client'

module <PERSON><PERSON><PERSON>
  class ExchangeCode

    def initialize(current_user, code)
      @current_user = current_user
      @code = code
    end

    def call
      begin
        url = APP_KYLAS_HOST + "/oauth/token"
        cred = "#{Rails.application.credentials.kylas[:app_id]}:#{Rails.application.credentials.kylas[:app_secret]}"
        encoded_credentials = Base64::encode64(cred).gsub("\n",'')
        redirect_uri = Rails.application.credentials.kylas[:redirect_uri]
        url = "#{url}?grant_type=authorization_code&code=#{@code}&redirect_uri=#{redirect_uri}" 
        response = RestClient.post(url, 
                                   nil,
                                   { 'Authorization' => "Basic #{encoded_credentials}", 'Content-Type' => 'application/x-www-form-urlencoded'})
        if response.code.eql?(200)
          res = JSON.parse(response.body)
          { success: true, 
            access_token: res["access_token"],
            refresh_token: res["refresh_token"],
            expires_in: res["expires_in"].to_i
          }
        else
          # Do we need this????
          { success: false, error_message: response.body }
        end
      rescue RestClient::NotFound
        Rails.logger.error "Create Lead - 404"
        { error_message: 'Invalid Data!', success: false }
      rescue RestClient::InternalServerError
        Rails.logger.error "Create Lead - 500"
        { error_message: 'Internal server error!', success: false }
      rescue RestClient::BadRequest
        Rails.logger.error "Create Lead - 400"
        { error_message: 'Invalid Data!', success: false }
      end
    end
  end
end

# frozen_string_literal: true

require 'rest-client'
include UserAgentHelper

module Kyla<PERSON>
  class GetLeadAttributes
    def initialize(current_user)
      @current_user = current_user
      @campaign_item_name = 'campaign'
      @source_item_name   = 'source'
      @product_item_name  = 'products'
      @owner_item_name    = 'ownerId'
      @types_to_be_selected = %w[TEXT_FIELD PICK_LIST EMAIL PHONE]
      @internal_fields = %w[createdViaId createdViaName createdViaType updatedViaId updatedViaName updatedViaType]
      @sub_source_name = 'subSource'
      @utm_source_name = 'utmSource'
      @utm_campaign_name = 'utmCampaign'
      @utm_medium_name = 'utmMedium'
      @utm_content_name = 'utmContent'
      @utm_term_name = 'utmTerm'
    end

    def call
      if @current_user.kylas_api_key.blank? && @current_user.kylas_refresh_token.blank?
        return { campaigns: [], sources: [], attributes: [] }
      end

      fetch_lead_fields
    end

    private

    def fetch_lead_fields
      url = "#{APP_KYLAS_HOST}/v1/entities/lead/fields?entityType=lead&custom-only=false&sort=createdAt,asc&page=0&size=10"

      begin
        if @current_user.kylas_refresh_token
          access_token = get_access_token
          response = RestClient.get(url, {
            'Authorization' => "Bearer #{access_token}",
            'User-Agent' => kylas_user_agent(kylas_tenant_id: @current_user.kylas_tenant_id, kylas_user_id: @current_user.kylas_user_id)
          })
        else
          response = RestClient.get(url, {
            'api-key': @current_user.kylas_api_key,
            'User-Agent' => kylas_user_agent(kylas_tenant_id: @current_user.kylas_tenant_id, kylas_user_id: @current_user.kylas_user_id, api_key: true)
          })
        end
        @response_data = JSON.parse(response.body)
        parse_and_get_data
      rescue RestClient::NotFound
        Rails.logger.error 'Get Lead Fields - 404'
        { error_message: 'Invalid Data!' }
      rescue RestClient::InternalServerError
        Rails.logger.error 'Get Lead Fields - 500'
        { error_message: 'Internal server error!' }
      rescue RestClient::BadRequest
        Rails.logger.error 'Get Lead Fields - 400'
        { error_message: 'Invalid Data!' }
      rescue RestClient::ExceptionWithResponse => e
        Rails.logger.error "Get Lead Fields - #{e.http_code}"
        { error_message: e.message }
      end
    end

    def parse_and_get_data
      {
        campaign_display_name: campaign_data.first,
        campaigns: campaign_data.last,
        source_display_name: source_data.first,
        sources: source_data.last,
        product_display_name: get_field_display_name(name: @product_item_name),
        owner_display_name: get_field_display_name(name: @owner_item_name),
        attributes: all_formatted_layout_items,
        sub_source_display_name: get_field_display_name(name: @sub_source_name),
        utm_source_display_name: get_field_display_name(name: @utm_source_name),
        utm_campaign_display_name: get_field_display_name(name: @utm_campaign_name),
        utm_medium_display_name: get_field_display_name(name: @utm_medium_name),
        utm_content_display_name: get_field_display_name(name: @utm_content_name),
        utm_term_display_name: get_field_display_name(name: @utm_term_name)
      }
    end

    def campaign_data
      campaign = @response_data.detect { |field| field['name'] == @campaign_item_name }
      campaign_display_name = campaign['displayName']
      campaigns = campaign.dig('picklist', 'values').map { |c| { id: c['id'], name: c['displayName'] } }
      [campaign_display_name, campaigns]
    end

    def source_data
      source = @response_data.detect { |field| field['name'] == @source_item_name }
      source_display_name = source['displayName']
      sources = source.dig('picklist', 'values').map { |s| { id: s['id'], name: s['displayName'] } }
      [source_display_name, sources]
    end

    def get_field_display_name(name:)
      attribute = @response_data.detect { |field| field['name'] == name }
      attribute['displayName']
    end

    def all_formatted_layout_items
      layout_items = @response_data.select { |field| @types_to_be_selected.include?(field['type']) && @internal_fields.exclude?(field['name']) }
      layout_items.map do |field|
        { label: field['displayName'], key: field['name'], id: field['id'], is_custom: !field['standard'], active: field['active'], field_type: field['type'] }
      end
    end

    def get_access_token
      return @current_user.kylas_access_token if @current_user.kylas_access_token_expires_at.to_i > DateTime.now.to_i

      res = Kylas::GetAccessToken.new(@current_user.kylas_refresh_token).call
      return nil unless res[:success]

      @current_user.update(kylas_access_token: res[:access_token], kylas_refresh_token: res[:refresh_token],
                           kylas_access_token_expires_at: Time.at(res[:expires_in].to_i))
      res[:access_token]
    end
  end
end

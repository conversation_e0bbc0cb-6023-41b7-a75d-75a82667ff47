require 'rest-client'
include UserAgentHelper

module Kylas
  class TenantsDetails
    def initialize(user:)
      @user = user
    end

    def fetch
      begin
        response_body = request_and_get_data
        { success: true, data: response_body }
      rescue RestClient::BadRequest
        Rails.logger.error "Get User Country - 400"
        { success: false, data: 'Invalid Data!' }

      rescue RestClient::NotFound
        Rails.logger.error "Get User Country - 404"
        { success: false, data: 'Invalid Data!' }
      rescue RestClient::InternalServerError
        Rails.logger.error "Get User Country - 500"
        { success: false, data: 'Internal server error!' }
      rescue RestClient::BadRequest
        Rails.logger.error "Get User Country - 400"
        { success: false, data: 'Invalid Data!' }
      end
    end

    def request_and_get_data
      url = APP_KYLAS_HOST + "/v1/tenants"
      if @user.kylas_api_key?
        response = RestClient.get(url, {
          'api-key': @user.kylas_api_key,
          content_type: :json,
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @user.kylas_tenant_id, kylas_user_id: @user.kylas_user_id, api_key: true)
        })
      else
        response = RestClient.get(url, {
          'Authorization' => "Bearer #{@user.get_access_token}",
          content_type: :json,
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @user.kylas_tenant_id, kylas_user_id: @user.kylas_user_id)
        })
      end
      response.body ? JSON.parse(response.body).with_indifferent_access : {}
    end
  end
end

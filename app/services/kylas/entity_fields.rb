# frozen_string_literal: true

require 'rest-client'

include UserAgentHelper

module <PERSON><PERSON><PERSON>
  class EntityFields
    def initialize(user, entity_type)
      @user = user
      @entity_type = entity_type
    end

    def fetch
      url = "#{APP_KYLAS_HOST}/v1/entities/#{@entity_type}/fields?entityType=#{@entity_type}&custom-only=false&sort=createdAt,asc"

      if @user.kylas_refresh_token.present?
        response = RestClient.get(url, {
          'Authorization' => "Bearer #{@user.get_access_token}",
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @user.kylas_tenant_id, kylas_user_id: @user.kylas_user_id)
        })
      else
        response = RestClient.get(url, {
          'api-key': @user.kylas_api_key,
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @user.kylas_tenant_id, kylas_user_id: @user.kylas_user_id, api_key: true)
        })
      end

      response_body =
        begin
          JSON.parse(response.body)
        rescue
          response.body
        end

      if response.code == 200
        { success: true, data: response_body }
      else
        Rails.logger.error "Error while fetching #{@entity_type} fields from kylas - status_code: #{response.code}, error_message: #{response_body}"
        { success: false, data: response_body }
      end
    rescue RestClient::NotFound
      Rails.logger.error "Get #{@entity_type} Fields - 404"
      { success: false, data: 'Invalid Data!' }
    rescue RestClient::InternalServerError
      Rails.logger.error "Get #{@entity_type} Fields - 500"
      { success: false, data: 'Internal server error!' }
    rescue RestClient::BadRequest
      Rails.logger.error "Get #{@entity_type} Fields - 400"
      { success: false, data: 'Invalid Data!' }
    rescue  RestClient::ExceptionWithResponse => e
      Rails.logger.error "Get #{@entity_type} Fields - #{e.http_code}"
      { success: false, data: e.message }
    end
  end
end

require 'rest-client'
include UserAgentHelper

module <PERSON><PERSON><PERSON>
  class GetUsers

    def initialize(current_user)
      @current_user = current_user
      @users = []
      @page = 0
    end

    def call
      return { users: @users } if @current_user.kylas_api_key.blank? && @current_user.kylas_refresh_token.blank?

      fetch_users
    end

    private

    def fetch_users
      begin
        loop do
          body = request_and_get_data
          @users += body[:content]

          break if body[:last].eql?(true)
          @page = @page + 1
        end

        { users: @users }
      rescue RestClient::NotFound
        Rails.logger.error "Get Lead Fields - 404"
        { error_message: 'Invalid Data!' }
      rescue RestClient::InternalServerError
        Rails.logger.error "Get Lead Fields - 500"
        { error_message: 'Internal server error!' }
      rescue RestClient::BadRequest
        Rails.logger.error "Get Lead Fields - 400"
        { error_message: 'Invalid Data!' }
      end
    end

    def request_and_get_data
      url = APP_KYLAS_HOST + "/v1/users/search?sort=firstName&page=#{@page}&size=100"
      payload = { fields: ["firstName", "lastName", "id"] }

      if @current_user.kylas_refresh_token
        access_token = get_access_token
        response = RestClient.post(url, payload.to_json, {
          'Authorization' => "Bearer #{access_token}",
          content_type: :json,
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @current_user.kylas_tenant_id, kylas_user_id: @current_user.kylas_user_id)
        })
      else
        response = RestClient.post(url, payload.to_json, {
          'api-key': @current_user.kylas_api_key,
          content_type: :json,
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @current_user.kylas_tenant_id, kylas_user_id: @current_user.kylas_user_id, api_key: true)
        })
      end

      response.body ? JSON.parse(response.body).with_indifferent_access : {}
    end

    def get_access_token
      return @current_user.kylas_access_token if(@current_user.kylas_access_token_expires_at.to_i > DateTime.now.to_i)
      res = Kylas::GetAccessToken.new(@current_user.kylas_refresh_token).call
      return nil unless res[:success]
      @current_user.update(kylas_access_token: res[:access_token], kylas_refresh_token: res[:refresh_token],
                           kylas_access_token_expires_at: (res[:expires_in].to_i))
      res[:access_token]
    end
  end
end

require 'rest-client'
include UserAgentHelper

module <PERSON><PERSON><PERSON>
  class GetProducts

    def initialize(current_user)
      @current_user = current_user
    end

    def call
      return { products: [] } if @current_user.kylas_api_key.blank? && @current_user.kylas_refresh_token.blank?

      fetch_products
    end

    def lookup(q)
      return { products: [] } if @current_user.kylas_api_key.blank? && @current_user.kylas_refresh_token.blank?

      if q.present?
        query_rule = {
          operator: 'contains',
          id: 'name',
          field: 'name',
          type: 'string',
          value: q,
          relatedFieldIds: nil
        }
      end

      fetch_products({ query_rule: query_rule })
    end

    def by_ids(ids)
      return { products: [] } if @current_user.kylas_api_key.blank? && @current_user.kylas_refresh_token.blank?

      if ids.present?
        query_rule = {
          operator: 'in',
          id: 'id',
          field: 'id',
          type: 'double',
          value: ids.join(","),
          relatedFieldIds: nil
        }
      end

      fetch_products({ query_rule: query_rule })
    end

    private

    def fetch_products(data = {})
      begin
        body = request_and_get_data(10,0, data[:query_rule])
        return { products: body[:content] }

      rescue RestClient::NotFound
        Rails.logger.error "Get Products - 404"
        { error_message: 'Invalid Data!' }
      rescue RestClient::InternalServerError
        Rails.logger.error "Get Products - 500"
        { error_message: 'Internal server error!' }
      rescue RestClient::BadRequest
        Rails.logger.error "Get Products - 400"
        { error_message: 'Invalid Data!' }
      end
    end

    def request_and_get_data(size = 10, page = 0, query_rule = nil)
      url = APP_KYLAS_HOST + "/v1/products/search?page=#{page}&size=#{size}&sort=name,asc"
      rules = [
        {
          operator: 'equal',
          field: 'isActive',
          type: 'boolean',
          value: true
        }
      ]

      rules << query_rule if query_rule.present?

      payload = {
        rules: rules
      }

      if @current_user.kylas_refresh_token
        access_token = get_access_token
        response = RestClient.post(url, payload.to_json, {
          'Authorization' => "Bearer #{access_token}",
          content_type: :json,
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @current_user.kylas_tenant_id, kylas_user_id: @current_user.kylas_user_id)
        })
      else
        response = RestClient.post(url, payload.to_json, {
          'api-key': @current_user.kylas_api_key,
          content_type: :json,
          'User-Agent' => kylas_user_agent(kylas_tenant_id: @current_user.kylas_tenant_id, kylas_user_id: @current_user.kylas_user_id, api_key: true)
        })
      end

      response.body ? JSON.parse(response.body).with_indifferent_access : {}

    end

    def get_access_token
      return @current_user.kylas_access_token if(@current_user.kylas_access_token_expires_at.to_i > DateTime.now.to_i)
      res = Kylas::GetAccessToken.new(@current_user.kylas_refresh_token).call
      return nil unless res[:success]
      @current_user.update(kylas_access_token: res[:access_token], kylas_refresh_token: res[:refresh_token],
                           kylas_access_token_expires_at: Time.at(res[:expires_in].to_i))
      res[:access_token]
    end
  end
end

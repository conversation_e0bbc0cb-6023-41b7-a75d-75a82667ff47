# frozen_string_literal: true
require "net/http"
include UserAgentHelper

module <PERSON><PERSON><PERSON>
  class Webhook
    def initialize(user)
      @user = user
    end

    def create
      url = URI("#{APP_KYLAS_HOST}/v1/webhooks")
      https = Net::HTTP.new(url.host, url.port)
      https.use_ssl = true
      request = Net::HTTP::Post.new(url)
      request["Content-Type"] = "application/json"
      request["api-key"] = @user.kylas_api_key
      request["User-Agent"] = kylas_user_agent(kylas_tenant_id: @user.kylas_tenant_id, kylas_user_id: @user.kylas_user_id,api_key: true)
      request.body = JSON.dump(webhook_payload)
      response = https.request(request)
      response_body = begin
        JSON.parse(response.body)
      rescue JSON::ParserError, TypeError
        response.body
      end

      if response.code == '201'
        Rails.logger.info "Success response received from kylas while creating fbleads webhook - #{response_body}"
        { success: true, data: response_body }
      else
        Rails.logger.error "Error response received from kylas while creating fbleads webhook - #{response_body}"
        { success: false, data: response_body }
      end

    rescue StandardError => e

    Rails.logger.error "Exception while creating status webhook - #{e.message}"
      { success: false, data: I18n.t('error_message') }
    end

      def webhook_payload
        {
          name: 'Webhook for kylas facebook lead integration application.',
          requestType: 'POST',
          url: "#{Rails.application.credentials.marketplace.app_host}/webhooks/handler",
          authenticationType: 'API_KEY',
          authenticationKey: Base64.strict_encode64(
            { 'keyName': 'Api-Key', 'value':  @user.kylas_api_key }.to_json
          ),
          events: webhook_events_list,
          system: true,
          active: true
        }
      end

      def webhook_events_list
        %w[ACCOUNT_UPDATED]
      end
  end
end

require 'rest-client'
include UserAgentHelper

module Kylas
  class CreateLeadIn<PERSON>ylas

    def initialize(current_user, payload)
      @current_user = current_user
      @payload      = payload
    end

    def call
      return { error_message: "No API key OR auth token present", success: false } if @current_user.kylas_api_key.blank? && @current_user.kylas_refresh_token.blank?

      create_lead
    end

    private

    def create_lead
      begin
        url = APP_KYLAS_HOST + "/v1/leads/"
        if @current_user.kylas_refresh_token
          access_token = get_access_token
          response = RestClient.post(url, @payload.to_json,  {
            content_type: :json,
            'Authorization' => "Bearer #{@current_user.reload.kylas_access_token}",
            'User-Agent' => kylas_user_agent(kylas_tenant_id: @current_user.kylas_tenant_id, kylas_user_id: @current_user.kylas_user_id)
          })
        else
          response = RestClient.post(url,
            @payload.to_json,  {
              content_type: :json,
              'api-key': @current_user.kylas_api_key,
              'User-Agent' => kylas_user_agent(kylas_tenant_id: @current_user.kylas_tenant_id, kylas_user_id: @current_user.kylas_user_id, api_key: true)
            })
        end
        if response.code === 200
          lead = JSON.parse(response.body)
          { success: true, id: lead['id'] }
        else
          { success: false, error_message: response.body }
        end
      rescue RestClient::NotFound => e
        Rails.logger.error "Create Lead - 404"
        get_parsed_error(e, 'Invalid Data!')
      rescue RestClient::InternalServerError => e
        Rails.logger.error "Create Lead - 500"
        get_parsed_error(e, 'Internal server error!')
      rescue RestClient::BadRequest => e
        Rails.logger.error "Create Lead - 400"
        get_parsed_error(e, 'Invalid Data!')
      rescue RestClient::Forbidden => e
        Rails.logger.error "Create Lead - 403"
        get_parsed_error(e, 'Not enough permission')
      end
    end

    def get_access_token
      return @current_user.kylas_access_token if(@current_user.kylas_access_token_expires_at.to_i > DateTime.now.to_i)
      res = Kylas::GetAccessToken.new(@current_user.kylas_refresh_token).call
      return nil unless res[:success]
      @current_user.update(kylas_access_token: res[:access_token], kylas_refresh_token: res[:refresh_token],
                           kylas_access_token_expires_at: (res[:expires_in].to_i))
      res[:access_token]
    end

    def get_parsed_error(e, default_message)
      if e.response
        res = JSON.parse(e.response)
        return {error_message: "#{res['message']} | #{res['errorDetails']}" , success: false}
      else
        return { error_message: default_message, success: false }
      end
    end
  end
end

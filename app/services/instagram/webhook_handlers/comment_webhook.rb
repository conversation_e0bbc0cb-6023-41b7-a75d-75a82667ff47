# frozen_string_literal: true

class Instagram::WebhookHandlers::CommentWebhook
  def initialize(webhook_event)
    @webhook_event = webhook_event
    @account = webhook_event.instagram_account
    @event_data = webhook_event.event_data
  end

  def process
    return unless @account&.is_active?

    case @event_data['field']
    when 'comments'
      process_comment_event
    when 'live_comments'
      process_live_comment_event
    end
  end

  private

  def process_comment_event
    comment_data = @event_data['value'] || {}
    
    case comment_data['verb']
    when 'add'
      process_new_comment(comment_data)
    when 'edit'
      process_edited_comment(comment_data)
    when 'remove'
      process_removed_comment(comment_data)
    when 'hide'
      process_hidden_comment(comment_data)
    end
  end

  def process_new_comment(comment_data)
    media_id = comment_data['media_id']
    comment_id = comment_data['id']
    
    # Find the Instagram post
    post = @account.instagram_posts.find_by(media_id: media_id)
    
    unless post
      # If post doesn't exist, fetch it first
      Instagram::DataFetching::SinglePostJob.perform_later(@account.id, media_id)
      return
    end

    # Check if comment already exists
    return if post.instagram_comments.exists?(comment_id: comment_id)

    # Create comment record
    comment = create_comment_record(post, comment_data)
    
    # Schedule comment analysis for lead potential
    Instagram::CommentAnalysisJob.perform_later(comment.id) if comment
  end

  def process_edited_comment(comment_data)
    comment_id = comment_data['id']
    
    # Find existing comment
    comment = InstagramComment.find_by(comment_id: comment_id)
    return unless comment

    # Update comment text
    comment.update!(
      text: comment_data['text'],
      is_processed: false # Re-process for lead potential
    )

    # Re-analyze for lead potential
    Instagram::CommentAnalysisJob.perform_later(comment.id)
  end

  def process_removed_comment(comment_data)
    comment_id = comment_data['id']
    
    # Find and remove comment
    comment = InstagramComment.find_by(comment_id: comment_id)
    comment&.destroy
  end

  def process_hidden_comment(comment_data)
    comment_id = comment_data['id']
    
    # Find and mark comment as hidden
    comment = InstagramComment.find_by(comment_id: comment_id)
    comment&.update!(is_hidden: true)
  end

  def process_live_comment_event
    # Handle live video comments
    live_comment_data = @event_data['value'] || {}
    
    # For now, we'll treat live comments similar to regular comments
    # but with a different source type
    process_new_comment(live_comment_data.merge('source_type' => 'live'))
  end

  def create_comment_record(post, comment_data)
    comment = post.instagram_comments.create!(
      comment_id: comment_data['id'],
      text: comment_data['text'],
      username: extract_username(comment_data),
      user_id: comment_data['from']['id'],
      commented_at: Time.at(comment_data['created_time']),
      like_count: 0, # Will be updated when we fetch full comment data
      reply_count: 0,
      is_hidden: false,
      user_profile_data: extract_user_profile(comment_data['from'])
    )

    # Update post comment count
    post.increment!(:comment_count)

    # Fetch full comment data including likes and replies
    Instagram::DataFetching::SingleCommentJob.perform_later(comment.id)

    comment
  rescue ActiveRecord::RecordInvalid => e
    Rails.logger.error "Failed to create Instagram comment: #{e.message}"
    nil
  end

  def extract_username(comment_data)
    comment_data.dig('from', 'username') || 
    comment_data.dig('from', 'name') || 
    "user_#{comment_data.dig('from', 'id')}"
  end

  def extract_user_profile(user_data)
    {
      'id' => user_data['id'],
      'username' => user_data['username'],
      'name' => user_data['name'],
      'profile_picture_url' => user_data['profile_picture_url']
    }
  end
end

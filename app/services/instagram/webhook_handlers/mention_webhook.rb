# frozen_string_literal: true

class Instagram::WebhookHandlers::MentionWebhook
  def initialize(webhook_event)
    @webhook_event = webhook_event
    @account = webhook_event.instagram_account
    @event_data = webhook_event.event_data
  end

  def process
    return unless @account&.is_active?

    case @event_data['field']
    when 'mentions'
      process_mention_event
    when 'story_mentions'
      process_story_mention_event
    end
  end

  private

  def process_mention_event
    mention_data = @event_data['value'] || {}
    
    case mention_data['verb']
    when 'add'
      process_new_mention(mention_data)
    end
  end

  def process_new_mention(mention_data)
    media_id = mention_data['media_id']
    mention_id = mention_data['id']
    
    # Create mention lead directly since mentions are typically high-intent
    create_mention_lead(mention_data)
  end

  def process_story_mention_event
    story_mention_data = @event_data['value'] || {}
    
    # Story mentions are also high-intent interactions
    create_story_mention_lead(story_mention_data)
  end

  def create_mention_lead(mention_data)
    # Extract user information
    user_data = mention_data['from'] || {}
    username = user_data['username'] || user_data['id']
    
    # Check if lead already exists for this mention
    existing_lead = @account.instagram_leads.find_by(
      source_type: 'mention',
      source_id: mention_data['id']
    )
    return if existing_lead

    # Create Instagram lead
    lead_data = {
      user: @account.user,
      instagram_account: @account,
      source_type: 'mention',
      source_id: mention_data['id'],
      lead_username: username,
      lead_user_id: user_data['id'],
      lead_score: calculate_mention_score(mention_data),
      raw_data: prepare_mention_raw_data(mention_data),
      processed_data: prepare_mention_processed_data(mention_data),
      contact_info: extract_mention_contact_info(mention_data),
      qualification_status: 'qualified' # Mentions are typically qualified leads
    }

    # Find appropriate form for mapping
    form = find_matching_form
    lead_data[:form] = form if form

    instagram_lead = InstagramLead.create!(lead_data)
    
    Rails.logger.info "Created Instagram mention lead: #{instagram_lead.id}"
    
    # Process immediately since mentions are high-priority
    Instagram::LeadProcessorJob.perform_later(instagram_lead.id)
  end

  def create_story_mention_lead(story_mention_data)
    # Extract user information
    user_data = story_mention_data['from'] || {}
    username = user_data['username'] || user_data['id']
    
    # Check if lead already exists for this story mention
    existing_lead = @account.instagram_leads.find_by(
      source_type: 'story_reply',
      source_id: story_mention_data['id']
    )
    return if existing_lead

    # Create Instagram lead
    lead_data = {
      user: @account.user,
      instagram_account: @account,
      source_type: 'story_reply',
      source_id: story_mention_data['id'],
      lead_username: username,
      lead_user_id: user_data['id'],
      lead_score: calculate_story_mention_score(story_mention_data),
      raw_data: prepare_story_mention_raw_data(story_mention_data),
      processed_data: prepare_story_mention_processed_data(story_mention_data),
      contact_info: extract_story_mention_contact_info(story_mention_data),
      qualification_status: 'qualified' # Story mentions are typically qualified leads
    }

    # Find appropriate form for mapping
    form = find_matching_form
    lead_data[:form] = form if form

    instagram_lead = InstagramLead.create!(lead_data)
    
    Rails.logger.info "Created Instagram story mention lead: #{instagram_lead.id}"
    
    # Process immediately since story mentions are high-priority
    Instagram::LeadProcessorJob.perform_later(instagram_lead.id)
  end

  def calculate_mention_score(mention_data)
    score = 60 # Base score for mentions (high intent)
    
    # Add score based on user profile
    user_data = mention_data['from'] || {}
    score += 10 if user_data['follower_count'].to_i > 100
    score += 15 if user_data['account_type'] == 'BUSINESS'
    
    # Add score if mention includes text
    score += 10 if mention_data['text'].present?
    
    [score, 100].min # Cap at 100
  end

  def calculate_story_mention_score(story_mention_data)
    score = 70 # Base score for story mentions (very high intent)
    
    # Add score based on user profile
    user_data = story_mention_data['from'] || {}
    score += 10 if user_data['follower_count'].to_i > 100
    score += 15 if user_data['account_type'] == 'BUSINESS'
    
    [score, 100].min # Cap at 100
  end

  def prepare_mention_raw_data(mention_data)
    {
      'mention' => {
        'id' => mention_data['id'],
        'media_id' => mention_data['media_id'],
        'text' => mention_data['text'],
        'created_time' => mention_data['created_time']
      },
      'user_profile' => mention_data['from'] || {},
      'media_info' => mention_data['media'] || {}
    }
  end

  def prepare_story_mention_raw_data(story_mention_data)
    {
      'story_mention' => {
        'id' => story_mention_data['id'],
        'story_id' => story_mention_data['story_id'],
        'text' => story_mention_data['text'],
        'created_time' => story_mention_data['created_time']
      },
      'user_profile' => story_mention_data['from'] || {}
    }
  end

  def prepare_mention_processed_data(mention_data)
    user_data = mention_data['from'] || {}
    
    {
      'name' => user_data['username'],
      'source_context' => "Mentioned in Instagram post: #{mention_data['text']&.truncate(100)}",
      'engagement_score' => calculate_mention_score(mention_data),
      'mention_type' => 'post_mention'
    }
  end

  def prepare_story_mention_processed_data(story_mention_data)
    user_data = story_mention_data['from'] || {}
    
    {
      'name' => user_data['username'],
      'source_context' => "Mentioned in Instagram story",
      'engagement_score' => calculate_story_mention_score(story_mention_data),
      'mention_type' => 'story_mention'
    }
  end

  def extract_mention_contact_info(mention_data)
    # Try to extract contact info from mention text
    text = mention_data['text'] || ''
    info = {}
    
    # Extract email
    email_regex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
    emails = text.scan(email_regex)
    info['email'] = emails.first if emails.any?
    
    # Extract phone
    phone_regex = /(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/
    phones = text.scan(phone_regex).flatten.reject(&:blank?)
    info['phone'] = phones.first if phones.any?
    
    info
  end

  def extract_story_mention_contact_info(story_mention_data)
    # Story mentions typically don't contain contact info in the mention itself
    # but we can check if the user profile has contact info
    {}
  end

  def find_matching_form
    # Try to find a form that matches this Instagram account
    # For now, return the first active form for the user
    @account.user.forms.where(saved_by_user: true).first
  end
end

# frozen_string_literal: true

class Instagram::WebhookHandlers::MessageWebhook
  def initialize(webhook_event)
    @webhook_event = webhook_event
    @account = webhook_event.instagram_account
    @event_data = webhook_event.event_data
  end

  def process
    return unless @account&.is_active?

    case @event_data['field']
    when 'messages'
      process_message_event
    when 'messaging_postbacks'
      process_postback_event
    when 'messaging_optins'
      process_optin_event
    end
  end

  private

  def process_message_event
    messaging_data = @event_data['messaging'] || []
    
    messaging_data.each do |message_data|
      process_individual_message(message_data)
    end
  end

  def process_individual_message(message_data)
    sender_id = message_data.dig('sender', 'id')
    recipient_id = message_data.dig('recipient', 'id')
    
    # Skip if message is from the business account itself
    return if sender_id == @account.instagram_user_id

    # Find or create conversation
    conversation = find_or_create_conversation(sender_id, message_data)
    
    # Create message record
    create_message_record(conversation, message_data)
    
    # Schedule conversation analysis for lead potential
    Instagram::ConversationAnalysisJob.perform_later(conversation.id)
  end

  def find_or_create_conversation(sender_id, message_data)
    # Try to find existing conversation
    conversation = @account.instagram_conversations.find_by(participant_id: sender_id)
    
    unless conversation
      # Create new conversation
      conversation = @account.instagram_conversations.create!(
        conversation_id: generate_conversation_id(sender_id),
        participant_id: sender_id,
        participant_username: extract_username(message_data),
        last_message_time: Time.current,
        message_count: 0,
        status: 'active'
      )
      
      # Fetch participant profile data
      fetch_participant_profile(conversation)
    end
    
    conversation
  end

  def create_message_record(conversation, message_data)
    message_id = message_data['message']['mid']
    
    # Skip if message already exists
    return if conversation.instagram_messages.exists?(message_id: message_id)

    message = conversation.instagram_messages.create!(
      message_id: message_id,
      text: message_data.dig('message', 'text'),
      sender_id: message_data.dig('sender', 'id'),
      sender_username: extract_username(message_data),
      sent_at: Time.at(message_data['timestamp'] / 1000),
      message_type: determine_message_type(message_data['message']),
      attachment_url: extract_attachment_url(message_data['message']),
      attachment_type: extract_attachment_type(message_data['message']),
      is_from_business: false,
      is_read: false
    )

    # Update conversation stats
    conversation.update!(
      last_message_time: message.sent_at,
      message_count: conversation.instagram_messages.count,
      unread_count: conversation.unread_count + 1
    )

    message
  end

  def process_postback_event
    # Handle button clicks and quick replies
    postback_data = @event_data['postback'] || {}
    sender_id = @event_data.dig('sender', 'id')
    
    # Find conversation
    conversation = @account.instagram_conversations.find_by(participant_id: sender_id)
    return unless conversation

    # Create postback message record
    conversation.instagram_messages.create!(
      message_id: "postback_#{Time.current.to_i}",
      text: postback_data['title'] || postback_data['payload'],
      sender_id: sender_id,
      sent_at: Time.current,
      message_type: 'postback',
      is_from_business: false,
      extracted_data: { postback: postback_data }
    )

    # Update conversation
    conversation.update!(
      last_message_time: Time.current,
      message_count: conversation.instagram_messages.count
    )
  end

  def process_optin_event
    # Handle user opt-ins for messaging
    optin_data = @event_data['optin'] || {}
    sender_id = @event_data.dig('sender', 'id')
    
    # Find or create conversation
    conversation = find_or_create_conversation(sender_id, @event_data)
    
    # Mark conversation as opted in
    conversation.update!(
      conversation_context: conversation.conversation_context.merge(
        'opted_in' => true,
        'optin_ref' => optin_data['ref'],
        'optin_time' => Time.current
      )
    )
  end

  def generate_conversation_id(participant_id)
    "#{@account.instagram_user_id}_#{participant_id}"
  end

  def extract_username(message_data)
    message_data.dig('sender', 'username') || 
    message_data.dig('sender', 'id') || 
    "user_#{message_data.dig('sender', 'id')}"
  end

  def determine_message_type(message)
    if message['attachments'].present?
      attachment = message['attachments'].first
      attachment['type'] || 'file'
    else
      'text'
    end
  end

  def extract_attachment_url(message)
    attachment = message.dig('attachments', 0)
    return nil unless attachment

    attachment.dig('payload', 'url')
  end

  def extract_attachment_type(message)
    attachment = message.dig('attachments', 0)
    return nil unless attachment

    attachment['type']
  end

  def fetch_participant_profile(conversation)
    # Schedule job to fetch participant profile data
    Instagram::DataFetching::ProfileDataJob.perform_later(conversation.id)
  end
end

# frozen_string_literal: true

class Instagram::LeadProcessing::CommentProcessor
  def initialize(instagram_comment)
    @comment = instagram_comment
    @post = @comment.instagram_post
    @account = @post.instagram_account
    @user = @account.user
  end

  def call
    return { success: false, error: 'Comment already processed' } if @comment.is_processed?

    begin
      # Analyze comment for lead potential
      analyze_comment
      
      # Create lead if qualified
      if @comment.is_lead_candidate?
        create_instagram_lead
      end
      
      { success: true, is_lead: @comment.is_lead_candidate? }
    rescue => e
      Rails.logger.error "Instagram comment processing error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def analyze_comment
    # Process the comment for lead scoring and qualification
    @comment.process_for_lead!
  end

  def create_instagram_lead
    return if @comment.instagram_lead.present?

    # Prepare lead data
    lead_data = {
      user: @user,
      instagram_account: @account,
      instagram_comment: @comment,
      source_type: 'comment',
      source_id: @comment.comment_id,
      lead_username: @comment.username,
      lead_user_id: @comment.user_id,
      lead_score: @comment.lead_score,
      raw_data: prepare_raw_data,
      processed_data: prepare_processed_data,
      contact_info: @comment.extracted_contact_info
    }

    # Find appropriate form for mapping
    form = find_matching_form
    lead_data[:form] = form if form

    # Create the lead
    instagram_lead = InstagramLead.create!(lead_data)
    
    Rails.logger.info "Created Instagram lead from comment: #{instagram_lead.id}"
    
    instagram_lead
  end

  def prepare_raw_data
    {
      'comment' => {
        'id' => @comment.comment_id,
        'text' => @comment.text,
        'username' => @comment.username,
        'user_id' => @comment.user_id,
        'timestamp' => @comment.commented_at,
        'like_count' => @comment.like_count,
        'reply_count' => @comment.reply_count
      },
      'post' => {
        'id' => @post.media_id,
        'type' => @post.media_type,
        'caption' => @post.caption,
        'permalink' => @post.permalink,
        'published_at' => @post.published_at
      },
      'user_profile' => @comment.user_profile_data,
      'extracted_contact_info' => @comment.extracted_contact_info
    }
  end

  def prepare_processed_data
    data = {}
    
    # Extract name from username or profile
    data['name'] = extract_name
    
    # Extract contact information
    if @comment.extracted_contact_info.present?
      data['email'] = @comment.extracted_contact_info['email']
      data['phone'] = @comment.extracted_contact_info['phone']
      data['website'] = @comment.extracted_contact_info['website']
    end
    
    # Extract business information from profile
    if @comment.user_profile_data.present?
      data['bio'] = @comment.user_profile_data['bio']
      data['follower_count'] = @comment.user_profile_data['follower_count']
    end
    
    # Add context
    data['source_context'] = "Comment on Instagram post: #{@post.caption&.truncate(100)}"
    data['engagement_score'] = calculate_engagement_score
    
    data
  end

  def extract_name
    # Try to extract a real name from username or bio
    username = @comment.username
    
    # If username looks like a real name (contains spaces or dots)
    if username.include?('.') || username.include?('_')
      # Convert username to a more readable format
      username.gsub(/[._]/, ' ').titleize
    else
      username
    end
  end

  def calculate_engagement_score
    score = 0
    
    # Comment engagement
    score += @comment.like_count * 2
    score += @comment.reply_count * 5
    
    # Text quality
    score += 10 if @comment.text.length > 50
    score += 20 if @comment.text.length > 100
    
    # Profile completeness
    if @comment.user_profile_data.present?
      score += 15 if @comment.user_profile_data['bio'].present?
      score += 10 if @comment.user_profile_data['follower_count'].to_i > 100
    end
    
    score
  end

  def find_matching_form
    # Try to find a form that matches this Instagram account
    # For now, return the first active form for the user
    @user.forms.where(saved_by_user: true).first
  end
end

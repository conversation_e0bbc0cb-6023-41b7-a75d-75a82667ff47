# frozen_string_literal: true

class Instagram::LeadProcessing::MessageProcessor
  def initialize(instagram_conversation)
    @conversation = instagram_conversation
    @account = @conversation.instagram_account
    @user = @account.user
  end

  def call
    return { success: false, error: 'Conversation already processed' } if @conversation.is_processed?

    begin
      # Analyze conversation for lead potential
      analyze_conversation
      
      # Create lead if qualified
      if @conversation.is_lead_candidate?
        create_instagram_lead
      end
      
      { success: true, is_lead: @conversation.is_lead_candidate? }
    rescue => e
      Rails.logger.error "Instagram conversation processing error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def analyze_conversation
    # Process the conversation for lead scoring and qualification
    @conversation.process_for_lead!
  end

  def create_instagram_lead
    return if @conversation.instagram_lead.present?

    # Prepare lead data
    lead_data = {
      user: @user,
      instagram_account: @account,
      instagram_conversation: @conversation,
      source_type: 'message',
      source_id: @conversation.conversation_id,
      lead_username: @conversation.participant_username,
      lead_user_id: @conversation.participant_id,
      lead_score: @conversation.lead_score,
      raw_data: prepare_raw_data,
      processed_data: prepare_processed_data,
      contact_info: @conversation.extracted_contact_info
    }

    # Find appropriate form for mapping
    form = find_matching_form
    lead_data[:form] = form if form

    # Create the lead
    instagram_lead = InstagramLead.create!(lead_data)
    
    Rails.logger.info "Created Instagram lead from conversation: #{instagram_lead.id}"
    
    instagram_lead
  end

  def prepare_raw_data
    {
      'conversation' => {
        'id' => @conversation.conversation_id,
        'participant_id' => @conversation.participant_id,
        'participant_username' => @conversation.participant_username,
        'message_count' => @conversation.message_count,
        'last_message_time' => @conversation.last_message_time
      },
      'messages' => @conversation.instagram_messages.map do |message|
        {
          'id' => message.message_id,
          'text' => message.text,
          'sender_id' => message.sender_id,
          'sent_at' => message.sent_at,
          'is_from_business' => message.is_from_business,
          'message_type' => message.message_type,
          'contains_contact_info' => message.contains_contact_info
        }
      end,
      'participant_profile' => @conversation.participant_profile_data,
      'extracted_contact_info' => @conversation.extracted_contact_info,
      'conversation_context' => @conversation.conversation_context
    }
  end

  def prepare_processed_data
    data = {}
    
    # Extract name from username or profile
    data['name'] = extract_name
    
    # Extract contact information
    if @conversation.extracted_contact_info.present?
      data['email'] = @conversation.extracted_contact_info['email']
      data['phone'] = @conversation.extracted_contact_info['phone']
      data['website'] = @conversation.extracted_contact_info['website']
      data['company'] = @conversation.extracted_contact_info['company']
    end
    
    # Extract business information from profile
    if @conversation.participant_profile_data.present?
      data['bio'] = @conversation.participant_profile_data['bio']
      data['follower_count'] = @conversation.participant_profile_data['follower_count']
    end
    
    # Add conversation context
    data['source_context'] = generate_conversation_summary
    data['engagement_score'] = calculate_engagement_score
    data['conversation_topics'] = @conversation.conversation_context['conversation_topics'] || []
    
    data
  end

  def extract_name
    # Try to extract a real name from username or conversation
    username = @conversation.participant_username
    
    # Look for name in messages
    customer_messages = @conversation.instagram_messages.customer_messages.pluck(:text).join(' ')
    name_patterns = [
      /my name is ([A-Z][a-z]+ [A-Z][a-z]+)/i,
      /i'm ([A-Z][a-z]+ [A-Z][a-z]+)/i,
      /call me ([A-Z][a-z]+)/i
    ]
    
    name_patterns.each do |pattern|
      match = customer_messages.match(pattern)
      return match[1] if match
    end
    
    # Fallback to formatted username
    if username.include?('.') || username.include?('_')
      username.gsub(/[._]/, ' ').titleize
    else
      username
    end
  end

  def generate_conversation_summary
    customer_messages = @conversation.instagram_messages.customer_messages.limit(3).pluck(:text)
    summary = customer_messages.join(' ').truncate(200)
    
    "Instagram DM conversation: #{summary}"
  end

  def calculate_engagement_score
    score = 0
    
    # Message volume
    score += @conversation.message_count * 5
    score += @conversation.instagram_messages.customer_messages.count * 10
    
    # Response patterns
    score += 20 if @conversation.customer_initiated_conversation?
    score += 15 if @conversation.recent_activity?
    
    # Content quality
    avg_message_length = @conversation.instagram_messages.customer_messages.average('LENGTH(text)').to_i
    score += 10 if avg_message_length > 50
    score += 20 if avg_message_length > 100
    
    # Profile completeness
    if @conversation.participant_profile_data.present?
      score += 15 if @conversation.participant_profile_data['bio'].present?
      score += 10 if @conversation.participant_profile_data['follower_count'].to_i > 100
    end
    
    score
  end

  def find_matching_form
    # Try to find a form that matches this Instagram account
    # For now, return the first active form for the user
    @user.forms.where(saved_by_user: true).first
  end
end

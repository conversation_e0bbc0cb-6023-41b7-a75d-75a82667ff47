# frozen_string_literal: true

class Instagram::DataFetching::MediaData
  def initialize(instagram_account)
    @account = instagram_account
  end

  def call
    return { success: false, error: 'Invalid access token' } unless valid_token?

    begin
      media_data = fetch_media_list
      
      if media_data
        process_media_data(media_data)
        { success: true, count: media_data.length }
      else
        { success: false, error: 'Failed to fetch media data' }
      end
    rescue => e
      Rails.logger.error "Instagram media data fetch error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @account.access_token.present? && !@account.token_expired?
  end

  def fetch_media_list
    access_token = @account.get_valid_access_token
    return nil unless access_token

    url = "https://graph.instagram.com/#{@account.instagram_user_id}/media"
    params = {
      fields: 'id,media_type,media_url,thumbnail_url,permalink,caption,timestamp,like_count,comments_count,shares_count,saves_count',
      limit: 50, # Fetch recent 50 posts
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    data['data'] if data
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Instagram media API error: #{e.response.body}"
    handle_api_error(e)
    nil
  end

  def process_media_data(media_list)
    media_list.each do |media_item|
      create_or_update_post(media_item)
    end
  end

  def create_or_update_post(media_data)
    post = @account.instagram_posts.find_or_initialize_by(media_id: media_data['id'])
    
    post.assign_attributes(
      media_type: media_data['media_type'],
      caption: media_data['caption'],
      permalink: media_data['permalink'],
      media_url: media_data['media_url'],
      thumbnail_url: media_data['thumbnail_url'],
      published_at: parse_timestamp(media_data['timestamp']),
      like_count: media_data['like_count'] || 0,
      comment_count: media_data['comments_count'] || 0,
      share_count: media_data['shares_count'] || 0,
      save_count: media_data['saves_count'] || 0
    )

    if post.save
      # Schedule comment fetching for posts with comments
      if post.comment_count > 0
        Instagram::DataFetching::CommentSyncJob.perform_later(post.id)
      end
      
      # Schedule insights fetching for business accounts
      if @account.business_account?
        Instagram::DataFetching::InsightsSyncJob.perform_later(post.id)
      end
    end

    post
  end

  def parse_timestamp(timestamp_str)
    return nil unless timestamp_str
    
    Time.parse(timestamp_str)
  rescue ArgumentError
    nil
  end

  def handle_api_error(error)
    error_data = JSON.parse(error.response.body) rescue {}
    error_message = error_data.dig('error', 'message') || 'Unknown API error'
    
    case error.response.code
    when 401
      @account.update!(is_active: false)
      Rails.logger.warn "Instagram token expired for account: #{@account.username}"
    when 403
      Rails.logger.warn "Instagram permission denied for account: #{@account.username}"
    when 429
      Rails.logger.warn "Instagram rate limited for account: #{@account.username}"
    end
  end
end

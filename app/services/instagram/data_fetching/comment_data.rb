# frozen_string_literal: true

class Instagram::DataFetching::CommentData
  def initialize(instagram_post)
    @post = instagram_post
    @account = @post.instagram_account
  end

  def call
    return { success: false, error: 'Invalid access token' } unless valid_token?

    begin
      comments_data = fetch_comments
      
      if comments_data
        process_comments_data(comments_data)
        { success: true, count: comments_data.length }
      else
        { success: false, error: 'Failed to fetch comments data' }
      end
    rescue => e
      Rails.logger.error "Instagram comments fetch error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @account.access_token.present? && !@account.token_expired?
  end

  def fetch_comments
    access_token = @account.get_valid_access_token
    return nil unless access_token

    url = "https://graph.instagram.com/#{@post.media_id}/comments"
    params = {
      fields: 'id,text,username,timestamp,like_count,replies_count,hidden,user',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    data['data'] if data
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Instagram comments API error: #{e.response.body}"
    handle_api_error(e)
    nil
  end

  def process_comments_data(comments_list)
    comments_list.each do |comment_data|
      create_or_update_comment(comment_data)
    end
  end

  def create_or_update_comment(comment_data)
    comment = @post.instagram_comments.find_or_initialize_by(comment_id: comment_data['id'])
    
    comment.assign_attributes(
      text: comment_data['text'],
      username: comment_data['username'],
      user_id: comment_data.dig('user', 'id'),
      commented_at: parse_timestamp(comment_data['timestamp']),
      like_count: comment_data['like_count'] || 0,
      reply_count: comment_data['replies_count'] || 0,
      is_hidden: comment_data['hidden'] || false,
      user_profile_data: extract_user_profile(comment_data['user'])
    )

    if comment.save
      # Schedule lead processing for new comments
      if comment.text.present? && !comment.is_processed?
        Instagram::LeadProcessing::CommentAnalysisJob.perform_later(comment.id)
      end
      
      # Fetch replies if any
      if comment.reply_count > 0
        fetch_comment_replies(comment)
      end
    end

    comment
  end

  def fetch_comment_replies(parent_comment)
    access_token = @account.get_valid_access_token
    return unless access_token

    url = "https://graph.instagram.com/#{parent_comment.comment_id}/replies"
    params = {
      fields: 'id,text,username,timestamp,like_count,user',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    
    if data && data['data']
      data['data'].each do |reply_data|
        create_comment_reply(reply_data, parent_comment)
      end
    end
  rescue => e
    Rails.logger.error "Failed to fetch comment replies: #{e.message}"
  end

  def create_comment_reply(reply_data, parent_comment)
    reply = @post.instagram_comments.find_or_initialize_by(comment_id: reply_data['id'])
    
    reply.assign_attributes(
      text: reply_data['text'],
      username: reply_data['username'],
      user_id: reply_data.dig('user', 'id'),
      commented_at: parse_timestamp(reply_data['timestamp']),
      like_count: reply_data['like_count'] || 0,
      parent_comment_id: parent_comment.comment_id,
      user_profile_data: extract_user_profile(reply_data['user'])
    )

    if reply.save && reply.text.present?
      Instagram::LeadProcessing::CommentAnalysisJob.perform_later(reply.id)
    end
  end

  def extract_user_profile(user_data)
    return {} unless user_data

    {
      'id' => user_data['id'],
      'username' => user_data['username'],
      'profile_picture_url' => user_data['profile_picture_url']
    }
  end

  def parse_timestamp(timestamp_str)
    return nil unless timestamp_str
    
    Time.parse(timestamp_str)
  rescue ArgumentError
    nil
  end

  def handle_api_error(error)
    error_data = JSON.parse(error.response.body) rescue {}
    
    case error.response.code
    when 401
      @account.update!(is_active: false)
    when 403
      Rails.logger.warn "Instagram permission denied for comments on post: #{@post.media_id}"
    when 429
      Rails.logger.warn "Instagram rate limited for comments fetch"
    end
  end
end

# frozen_string_literal: true

class Instagram::DataFetching::AccountData
  def initialize(instagram_account)
    @account = instagram_account
  end

  def call
    return { success: false, error: 'Invalid access token' } unless valid_token?

    begin
      account_data = fetch_account_info
      
      if account_data
        update_account_info(account_data)
        { success: true, data: account_data }
      else
        { success: false, error: 'Failed to fetch account data' }
      end
    rescue => e
      Rails.logger.error "Instagram account data fetch error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @account.access_token.present? && !@account.token_expired?
  end

  def fetch_account_info
    access_token = @account.get_valid_access_token
    return nil unless access_token

    url = "https://graph.instagram.com/#{@account.instagram_user_id}"
    params = {
      fields: 'id,username,account_type,media_count,followers_count,follows_count,biography,website,profile_picture_url',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    JSON.parse(response.body) if response.code == 200
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Instagram API error: #{e.response.body}"
    handle_api_error(e)
    nil
  end

  def update_account_info(data)
    @account.update!(
      username: data['username'],
      account_type: data['account_type'],
      media_count: data['media_count'] || 0,
      follower_count: data['followers_count'] || 0,
      following_count: data['follows_count'] || 0,
      bio: data['biography'],
      website: data['website'],
      profile_picture_url: data['profile_picture_url']
    )
  end

  def handle_api_error(error)
    error_data = JSON.parse(error.response.body) rescue {}
    error_message = error_data.dig('error', 'message') || 'Unknown API error'
    
    case error.response.code
    when 401
      # Token expired or invalid
      @account.update!(is_active: false)
      Rails.logger.warn "Instagram token expired for account: #{@account.username}"
    when 403
      # Permission denied
      Rails.logger.warn "Instagram permission denied for account: #{@account.username}"
    when 429
      # Rate limited
      Rails.logger.warn "Instagram rate limited for account: #{@account.username}"
    end
  end
end

# frozen_string_literal: true

class Instagram::DataFetching::MessageData
  def initialize(instagram_conversation)
    @conversation = instagram_conversation
    @account = @conversation.instagram_account
  end

  def call
    return { success: false, error: 'Invalid access token' } unless valid_token?

    begin
      messages_data = fetch_messages
      
      if messages_data
        process_messages_data(messages_data)
        { success: true, count: messages_data.length }
      else
        { success: false, error: 'Failed to fetch messages data' }
      end
    rescue => e
      Rails.logger.error "Instagram messages fetch error: #{e.message}"
      { success: false, error: e.message }
    end
  end

  private

  def valid_token?
    @account.access_token.present? && !@account.token_expired?
  end

  def fetch_messages
    access_token = @account.get_valid_access_token
    return nil unless access_token

    url = "https://graph.instagram.com/#{@conversation.conversation_id}/messages"
    params = {
      fields: 'id,message,from,to,created_time,attachments',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    data['data'] if data
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Instagram messages API error: #{e.response.body}"
    handle_api_error(e)
    nil
  end

  def process_messages_data(messages_list)
    messages_list.each do |message_data|
      create_or_update_message(message_data)
    end
  end

  def create_or_update_message(message_data)
    message = @conversation.instagram_messages.find_or_initialize_by(message_id: message_data['id'])
    
    # Determine if message is from business account
    sender_id = message_data.dig('from', 'id')
    is_from_business = sender_id == @account.instagram_user_id
    
    message.assign_attributes(
      text: message_data['message'],
      sender_id: sender_id,
      sender_username: message_data.dig('from', 'username'),
      sent_at: parse_timestamp(message_data['created_time']),
      is_from_business: is_from_business,
      message_type: determine_message_type(message_data),
      attachment_url: extract_attachment_url(message_data),
      attachment_type: extract_attachment_type(message_data),
      attachment_metadata: extract_attachment_metadata(message_data)
    )

    if message.save
      # Process message for lead potential if from customer
      if !is_from_business && message.text.present?
        Instagram::LeadProcessing::MessageAnalysisJob.perform_later(message.id)
      end
    end

    message
  end

  def determine_message_type(message_data)
    if message_data['attachments'].present?
      attachment = message_data['attachments'].first
      case attachment['type']
      when 'image'
        'image'
      when 'video'
        'video'
      when 'audio'
        'audio'
      else
        'file'
      end
    else
      'text'
    end
  end

  def extract_attachment_url(message_data)
    attachment = message_data.dig('attachments', 0)
    return nil unless attachment

    attachment['payload']['url']
  end

  def extract_attachment_type(message_data)
    attachment = message_data.dig('attachments', 0)
    return nil unless attachment

    attachment['type']
  end

  def extract_attachment_metadata(message_data)
    attachment = message_data.dig('attachments', 0)
    return {} unless attachment

    {
      'type' => attachment['type'],
      'size' => attachment.dig('payload', 'size'),
      'name' => attachment.dig('payload', 'name'),
      'mime_type' => attachment.dig('payload', 'mime_type')
    }
  end

  def parse_timestamp(timestamp_str)
    return nil unless timestamp_str
    
    Time.parse(timestamp_str)
  rescue ArgumentError
    nil
  end

  def handle_api_error(error)
    error_data = JSON.parse(error.response.body) rescue {}
    
    case error.response.code
    when 401
      @account.update!(is_active: false)
    when 403
      Rails.logger.warn "Instagram permission denied for messages in conversation: #{@conversation.conversation_id}"
    when 429
      Rails.logger.warn "Instagram rate limited for messages fetch"
    end
  end
end

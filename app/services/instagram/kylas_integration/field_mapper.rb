# frozen_string_literal: true

class Instagram::KylasIntegration::FieldMap<PERSON>
  def initialize(instagram_account)
    @account = instagram_account
    @user = instagram_account.user
  end

  def available_instagram_fields
    {
      profile: profile_fields,
      comment: comment_fields,
      message: message_fields
    }
  end

  def available_kylas_fields
    {
      lead: kylas_lead_fields,
      contact: kylas_contact_fields
    }
  end

  def create_mapping(params)
    mapping = @account.instagram_field_mappings.build(
      user: @user,
      instagram_field_name: params[:instagram_field_name],
      kylas_field_name: params[:kylas_field_name],
      entity_type: params[:entity_type],
      source_type: params[:source_type],
      is_custom_kylas_attribute: params[:is_custom_kylas_attribute] || false,
      kylas_field_type: params[:kylas_field_type],
      extraction_pattern: params[:extraction_pattern],
      is_active: true
    )

    if mapping.save
      { success: true, mapping: mapping }
    else
      { success: false, errors: mapping.errors.full_messages }
    end
  end

  def update_mapping(mapping_id, params)
    mapping = @account.instagram_field_mappings.find(mapping_id)
    
    if mapping.update(params.slice(:kylas_field_name, :is_custom_kylas_attribute, :kylas_field_type, :extraction_pattern, :is_active))
      { success: true, mapping: mapping }
    else
      { success: false, errors: mapping.errors.full_messages }
    end
  end

  def delete_mapping(mapping_id)
    mapping = @account.instagram_field_mappings.find(mapping_id)
    
    if mapping.destroy
      { success: true }
    else
      { success: false, errors: mapping.errors.full_messages }
    end
  end

  def preview_mapping(instagram_data, mapping_params)
    # Preview what the mapping would extract from sample data
    field_name = mapping_params[:instagram_field_name]
    source_type = mapping_params[:source_type]
    extraction_pattern = mapping_params[:extraction_pattern]

    extracted_value = extract_value_from_data(instagram_data, field_name, source_type, extraction_pattern)
    
    {
      instagram_field: field_name,
      source_type: source_type,
      sample_data: get_sample_data(instagram_data, source_type),
      extracted_value: extracted_value,
      kylas_field: mapping_params[:kylas_field_name]
    }
  end

  private

  def profile_fields
    [
      { name: 'username', label: 'Username', description: 'Instagram username' },
      { name: 'bio', label: 'Bio', description: 'Instagram bio/description' },
      { name: 'follower_count', label: 'Follower Count', description: 'Number of followers' },
      { name: 'following_count', label: 'Following Count', description: 'Number of accounts following' },
      { name: 'website', label: 'Website', description: 'Website URL from profile' },
      { name: 'bio_email', label: 'Email from Bio', description: 'Email extracted from bio text' },
      { name: 'bio_phone', label: 'Phone from Bio', description: 'Phone number extracted from bio text' }
    ]
  end

  def comment_fields
    [
      { name: 'comment_text', label: 'Comment Text', description: 'Full comment text' },
      { name: 'comment_likes', label: 'Comment Likes', description: 'Number of likes on comment' },
      { name: 'comment_email', label: 'Email from Comment', description: 'Email extracted from comment text' },
      { name: 'comment_phone', label: 'Phone from Comment', description: 'Phone number extracted from comment text' }
    ]
  end

  def message_fields
    [
      { name: 'message_text', label: 'Message Text', description: 'Combined text from all messages' },
      { name: 'message_count', label: 'Message Count', description: 'Number of messages in conversation' },
      { name: 'first_message', label: 'First Message', description: 'Text of the first message' },
      { name: 'message_email', label: 'Email from Messages', description: 'Email extracted from message text' },
      { name: 'message_phone', label: 'Phone from Messages', description: 'Phone number extracted from message text' }
    ]
  end

  def kylas_lead_fields
    # This would typically fetch from Kylas API
    # For now, return common lead fields
    [
      { name: 'firstName', label: 'First Name', type: 'text', is_standard: true },
      { name: 'lastName', label: 'Last Name', type: 'text', is_standard: true },
      { name: 'emails', label: 'Email', type: 'email', is_standard: true },
      { name: 'phoneNumbers', label: 'Phone', type: 'phone', is_standard: true },
      { name: 'description', label: 'Description', type: 'textarea', is_standard: true },
      { name: 'companyName', label: 'Company', type: 'text', is_standard: true },
      { name: 'designation', label: 'Designation', type: 'text', is_standard: true },
      { name: 'website', label: 'Website', type: 'url', is_standard: true }
    ]
  end

  def kylas_contact_fields
    # This would typically fetch from Kylas API
    # For now, return common contact fields
    [
      { name: 'firstName', label: 'First Name', type: 'text', is_standard: true },
      { name: 'lastName', label: 'Last Name', type: 'text', is_standard: true },
      { name: 'emails', label: 'Email', type: 'email', is_standard: true },
      { name: 'phoneNumbers', label: 'Phone', type: 'phone', is_standard: true },
      { name: 'companyName', label: 'Company', type: 'text', is_standard: true },
      { name: 'companyPhones', label: 'Company Phone', type: 'phone', is_standard: true },
      { name: 'designation', label: 'Designation', type: 'text', is_standard: true },
      { name: 'website', label: 'Website', type: 'url', is_standard: true }
    ]
  end

  def extract_value_from_data(data, field_name, source_type, extraction_pattern = nil)
    case source_type
    when 'profile'
      extract_from_profile(data, field_name, extraction_pattern)
    when 'comment'
      extract_from_comment(data, field_name, extraction_pattern)
    when 'message'
      extract_from_message(data, field_name, extraction_pattern)
    end
  end

  def extract_from_profile(data, field_name, extraction_pattern)
    profile = data['user_profile'] || data['profile'] || {}
    
    case field_name
    when 'bio_email', 'bio_phone'
      text = profile['bio'] || ''
      extract_with_pattern(text, extraction_pattern)
    else
      profile[field_name.gsub('bio_', '')]
    end
  end

  def extract_from_comment(data, field_name, extraction_pattern)
    comment = data['comment'] || {}
    
    case field_name
    when 'comment_email', 'comment_phone'
      text = comment['text'] || ''
      extract_with_pattern(text, extraction_pattern)
    else
      comment[field_name.gsub('comment_', '')]
    end
  end

  def extract_from_message(data, field_name, extraction_pattern)
    messages = data['messages'] || []
    
    case field_name
    when 'message_text'
      messages.map { |m| m['text'] }.compact.join(' ')
    when 'message_count'
      messages.count
    when 'first_message'
      messages.first&.dig('text')
    when 'message_email', 'message_phone'
      all_text = messages.map { |m| m['text'] }.compact.join(' ')
      extract_with_pattern(all_text, extraction_pattern)
    end
  end

  def extract_with_pattern(text, pattern)
    return nil unless text.present? && pattern.present?
    
    begin
      regex = Regexp.new(pattern)
      matches = text.scan(regex)
      matches.flatten.first
    rescue RegexpError
      nil
    end
  end

  def get_sample_data(data, source_type)
    case source_type
    when 'profile'
      data['user_profile'] || data['profile'] || {}
    when 'comment'
      data['comment'] || {}
    when 'message'
      data['messages']&.first || {}
    end
  end
end

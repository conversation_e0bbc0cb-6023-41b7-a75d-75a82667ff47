# frozen_string_literal: true

class Instagram::KylasIntegration::CreateLeadFromInstagram
  def initialize(instagram_lead, kylas_data)
    @instagram_lead = instagram_lead
    @user = instagram_lead.user
    @kylas_data = kylas_data
  end

  def call
    return { success: false, error: 'Invalid user or data' } unless valid_data?

    begin
      # Apply Instagram-specific field mappings
      mapped_data = apply_instagram_field_mappings(@kylas_data)
      
      # Create lead in Kylas
      result = Kylas::CreateLeadInKylas.new(@user, mapped_data).call
      
      if result[:success]
        Rails.logger.info "Instagram lead created in Kylas: #{result[:id]} from Instagram lead: #{@instagram_lead.id}"
        { success: true, id: result[:id] }
      else
        Rails.logger.error "Failed to create Instagram lead in Kylas: #{result[:error_message]}"
        { success: false, error_message: result[:error_message] }
      end
    rescue => e
      Rails.logger.error "Instagram to <PERSON>ylas lead creation error: #{e.message}"
      { success: false, error_message: e.message }
    end
  end

  private

  def valid_data?
    @user.present? && @kylas_data.present? && @instagram_lead.present?
  end

  def apply_instagram_field_mappings(data)
    # Get active field mappings for this Instagram account
    mappings = @instagram_lead.instagram_account.instagram_field_mappings
                                                .active
                                                .for_leads

    # Apply each mapping
    mappings.each do |mapping|
      mapped_value = extract_mapped_value(mapping)
      next unless mapped_value.present?

      if mapping.is_custom_kylas_attribute?
        data['customFieldValues'] ||= {}
        data['customFieldValues'][mapping.kylas_field_name] = format_field_value(mapped_value, mapping)
      else
        data[mapping.kylas_field_name] = format_field_value(mapped_value, mapping)
      end
    end

    # Add Instagram-specific metadata
    add_instagram_metadata(data)

    data
  end

  def extract_mapped_value(mapping)
    case mapping.source_type
    when 'profile'
      extract_profile_value(mapping.instagram_field_name)
    when 'comment'
      extract_comment_value(mapping.instagram_field_name)
    when 'message'
      extract_message_value(mapping.instagram_field_name)
    end
  end

  def extract_profile_value(field_name)
    profile_data = @instagram_lead.raw_data['user_profile'] || {}
    
    case field_name
    when 'username'
      @instagram_lead.lead_username
    when 'bio'
      profile_data['bio']
    when 'follower_count'
      profile_data['follower_count']
    when 'bio_email'
      extract_email_from_text(profile_data['bio'])
    when 'bio_phone'
      extract_phone_from_text(profile_data['bio'])
    when 'website'
      profile_data['website']
    else
      profile_data[field_name]
    end
  end

  def extract_comment_value(field_name)
    return nil unless @instagram_lead.source_type == 'comment'
    
    comment_data = @instagram_lead.raw_data['comment'] || {}
    
    case field_name
    when 'comment_text'
      comment_data['text']
    when 'comment_likes'
      comment_data['like_count']
    when 'comment_email'
      extract_email_from_text(comment_data['text'])
    when 'comment_phone'
      extract_phone_from_text(comment_data['text'])
    else
      comment_data[field_name]
    end
  end

  def extract_message_value(field_name)
    return nil unless @instagram_lead.source_type == 'message'
    
    messages_data = @instagram_lead.raw_data['messages'] || []
    all_text = messages_data.map { |m| m['text'] }.compact.join(' ')
    
    case field_name
    when 'message_text'
      all_text.truncate(500)
    when 'message_count'
      messages_data.count
    when 'message_email'
      extract_email_from_text(all_text)
    when 'message_phone'
      extract_phone_from_text(all_text)
    when 'first_message'
      messages_data.first&.dig('text')
    else
      nil
    end
  end

  def extract_email_from_text(text)
    return nil unless text.present?
    
    email_regex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
    matches = text.scan(email_regex)
    matches.first
  end

  def extract_phone_from_text(text)
    return nil unless text.present?
    
    phone_regex = /(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/
    matches = text.scan(phone_regex).flatten.reject(&:blank?)
    matches.first
  end

  def format_field_value(value, mapping)
    case mapping.kylas_field_name
    when 'emails'
      format_email_field(value)
    when 'phoneNumbers'
      format_phone_field(value)
    else
      value
    end
  end

  def format_email_field(email)
    return nil unless email.present?
    
    [{ type: 'OFFICE', primary: true, value: email }]
  end

  def format_phone_field(phone)
    return nil unless phone.present?
    
    parsed_phone = Phonelib.parse(phone, @user.country)
    
    if parsed_phone.valid?
      [{
        type: 'MOBILE',
        code: parsed_phone.country,
        dialCode: parsed_phone.country_code,
        value: parsed_phone.raw_national
      }]
    else
      nil
    end
  end

  def add_instagram_metadata(data)
    # Add Instagram-specific custom fields
    data['customFieldValues'] ||= {}
    
    data['customFieldValues'].merge!(
      'instagram_source_type' => @instagram_lead.source_type,
      'instagram_username' => @instagram_lead.lead_username,
      'instagram_account' => @instagram_lead.instagram_account.username,
      'instagram_lead_score' => @instagram_lead.lead_score,
      'instagram_source_url' => @instagram_lead.source_url
    )

    # Add UTM parameters for Instagram
    data['utmSource'] ||= 'instagram'
    data['utmMedium'] ||= 'social'
    data['utmCampaign'] ||= @instagram_lead.instagram_account.username
    data['utmContent'] ||= @instagram_lead.source_type
    data['utmTerm'] ||= 'instagram_lead'

    # Set source information
    data['subSource'] ||= "Instagram #{@instagram_lead.source_type.titleize}"
  end
end

# frozen_string_literal: true

class Instagram::Authentication::TokenRefresh
  def initialize(instagram_account)
    @account = instagram_account
  end

  def call
    return { success: false, error: 'No refresh token available' } unless @account.refresh_token.present?

    begin
      response = refresh_token_request
      
      if response.success?
        token_data = JSON.parse(response.body)
        
        {
          success: true,
          access_token: token_data['access_token'],
          expires_at: calculate_expiry(token_data['expires_in'])
        }
      else
        {
          success: false,
          error: "Token refresh failed: #{response.body}"
        }
      end
    rescue => e
      Rails.logger.error "Instagram token refresh error: #{e.message}"
      {
        success: false,
        error: e.message
      }
    end
  end

  private

  def refresh_token_request
    RestClient.post(
      'https://graph.instagram.com/refresh_access_token',
      {
        grant_type: 'ig_refresh_token',
        access_token: @account.access_token
      }
    )
  end

  def calculate_expiry(expires_in)
    return nil unless expires_in
    
    Time.current + expires_in.to_i.seconds
  end
end

# frozen_string_literal: true

class Instagram::Authentication::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
  def initialize(user, auth_data)
    @user = user
    @auth_data = auth_data
  end

  def call
    return nil unless valid_auth_data?

    # Extract Instagram account information from auth data
    instagram_user_id = extract_instagram_user_id
    return nil unless instagram_user_id

    # Find or create Instagram account
    instagram_account = find_or_create_account(instagram_user_id)
    
    if instagram_account
      # Update account with latest data
      update_account_data(instagram_account)
      
      # Sync initial data
      schedule_initial_sync(instagram_account)
    end

    instagram_account
  end

  private

  def valid_auth_data?
    @auth_data.present? && 
    @auth_data.provider == 'instagram_graph' &&
    @auth_data.credentials.present? &&
    @auth_data.credentials.token.present?
  end

  def extract_instagram_user_id
    # For Instagram Graph API, the user ID is in the auth data
    @auth_data.uid || @auth_data.extra&.raw_info&.dig('id')
  end

  def find_or_create_account(instagram_user_id)
    @user.instagram_accounts.find_or_initialize_by(instagram_user_id: instagram_user_id) do |account|
      account.username = extract_username
      account.account_type = extract_account_type
      account.is_active = true
    end
  end

  def update_account_data(account)
    account.assign_attributes(
      access_token: @auth_data.credentials.token,
      refresh_token: @auth_data.credentials.refresh_token,
      token_expires_at: calculate_token_expiry,
      username: extract_username,
      account_type: extract_account_type,
      profile_picture_url: extract_profile_picture,
      bio: extract_bio,
      website: extract_website,
      email: extract_email,
      is_active: true
    )

    account.save!
    account
  end

  def calculate_token_expiry
    expires_in = @auth_data.credentials.expires_in
    return nil unless expires_in

    Time.current + expires_in.seconds
  end

  def extract_username
    @auth_data.info&.nickname || 
    @auth_data.extra&.raw_info&.dig('username') ||
    "instagram_user_#{extract_instagram_user_id}"
  end

  def extract_account_type
    # Determine account type from auth data
    account_type = @auth_data.extra&.raw_info&.dig('account_type')
    
    case account_type&.upcase
    when 'BUSINESS'
      'BUSINESS'
    when 'CREATOR'
      'CREATOR'
    else
      'PERSONAL'
    end
  end

  def extract_profile_picture
    @auth_data.info&.image ||
    @auth_data.extra&.raw_info&.dig('profile_picture_url')
  end

  def extract_bio
    @auth_data.info&.description ||
    @auth_data.extra&.raw_info&.dig('biography')
  end

  def extract_website
    @auth_data.info&.urls&.dig('Website') ||
    @auth_data.extra&.raw_info&.dig('website')
  end

  def extract_email
    @auth_data.info&.email
  end

  def schedule_initial_sync(account)
    # Schedule background jobs to sync account data
    Instagram::DataFetching::AccountSyncJob.perform_later(account.id)
    Instagram::DataFetching::MediaSyncJob.perform_later(account.id)
  end
end

# frozen_string_literal: true

class FormsController < ApplicationController

  before_action :authenticate_user!
  before_action :get_form, only: [:edit, :update, :destroy]
  before_action :find_connected_account, only: %i[index save_default_mapping]

  def index
    @form_type = params[:form_type] || params.dig(:form, :form_type) || EXISTING
    @filter_by_params = {filter_by: '', filter_by_value: ''}

    if @form_type == EXISTING
      @forms = current_user.forms.includes(:page).where(saved_by_user: true).order(updated_at: :desc)
      set_filtered_forms
    elsif @form_type == NEW
      @sync_in_progress = current_user.fetch_forms_job_id.present?
      @forms = current_user.forms.includes(:page).where(saved_by_user: false).order(created_at: :desc)
      set_filtered_forms
    end

    @forms = @forms.page(params[:page]).per(10)
  end

  def create
    result = Form.create(create_params)

    if result.persisted?
      flash[:success] = 'Form added Succesfully!'
    else
      flash[:danger] = result.errors.full_messages.join(', ')
    end
    redirect_to forms_path
  end

  def edit
    lead_attributes_data = Kylas::GetLeadAttributes.new(current_user).call
    contact_attributes_data = Kylas::GetContactAttributes.new(current_user).call
    @fb_form_attributes = FetchFormAttributes.new(@form).call

    @kylas_lead_form_attributes = lead_attributes_data[:attributes] || []
    @kylas_contact_form_attributes = contact_attributes_data[:attributes] || []

    lead_mapped_fields = @form.mapped_lead_fields
    @kylas_lead_field_names = lead_mapped_fields.pluck(:kylas_field_name)
    @fb_lead_field_names = lead_mapped_fields.pluck(:fb_field_name)
    @lead_mapped_fields = lead_mapped_fields.page(params[:page]).per(10)

    contact_mapped_fields = @form.mapped_contact_fields
    @kylas_contact_field_names = contact_mapped_fields.pluck(:kylas_field_name)
    @fb_contact_field_names = contact_mapped_fields.pluck(:fb_field_name)
    @contact_mapped_fields = contact_mapped_fields.page(params[:page]).per(10)
    @lead_utm_fields = @form.lead_utm_fields
    @contact_utm_fields = @form.contact_utm_fields
    set_lead_utm_fields(lead_attributes_data: lead_attributes_data)
    set_contact_utm_fields(contact_attributes_data: contact_attributes_data)
    @selected_product = if @form.product_id.present?
        Kylas::GetProducts.new(current_user).by_ids([@form.product_id])[:products].map { |product| [product['id'], product['name']] }
      else
        []
      end
    @users     = Kylas::GetUsers.new(current_user).call[:users] || []
    @form_name = params[:form_name] || 'lead_form'
  end

  def set_lead_utm_fields(lead_attributes_data:)
    @lead_campaigns = lead_attributes_data[:campaigns] || []
    @lead_sources = lead_attributes_data[:sources] || []
    @lead_campaign_display_name = lead_attributes_data[:campaign_display_name] || 'Campaign'
    @lead_source_display_name = lead_attributes_data[:source_display_name] || 'Source'
    @lead_product_display_name = lead_attributes_data[:product_display_name] || 'Product & Services'
    @lead_owner_display_name = lead_attributes_data[:owner_display_name] || 'Owner'
    @lead_sub_source_display_name = lead_attributes_data[:sub_source_display_name] || 'Sub Source'
    @lead_utm_source_display_name = lead_attributes_data[:utm_source_display_name] || 'UTM Source'
    @lead_utm_campaign_display_name = lead_attributes_data[:utm_campaign_display_name] || 'UTM Campaign'
    @lead_utm_medium_display_name = lead_attributes_data[:utm_medium_display_name] || 'UTM Medium'
    @lead_utm_content_display_name = lead_attributes_data[:utm_content_display_name] || 'UTM Content'
    @lead_utm_term_display_name = lead_attributes_data[:utm_term_display_name] || 'UTM Term'
  end

  def set_contact_utm_fields(contact_attributes_data:)
    @contact_campaigns = contact_attributes_data[:campaigns] || []
    @contact_sources = contact_attributes_data[:sources] || []
    @contact_campaign_display_name = contact_attributes_data[:campaign_display_name] || 'Campaign'
    @contact_source_display_name = contact_attributes_data[:source_display_name] || 'Source'
    @contact_owner_display_name = contact_attributes_data[:owner_display_name] || 'Owner'
    @contact_sub_source_display_name = contact_attributes_data[:sub_source_display_name] || 'Sub Source'
    @contact_utm_source_display_name = contact_attributes_data[:utm_source_display_name] || 'UTM Source'
    @contact_utm_campaign_display_name = contact_attributes_data[:utm_campaign_display_name] || 'UTM Campaign'
    @contact_utm_medium_display_name = contact_attributes_data[:utm_medium_display_name] || 'UTM Medium'
    @contact_utm_content_display_name = contact_attributes_data[:utm_content_display_name] || 'UTM Content'
    @contact_utm_term_display_name = contact_attributes_data[:utm_term_display_name] || 'UTM Term'
  end

  def update
    form_name = params[:utm_field][:form_name]
    if form_name.include?(LEAD)
      form_updated = @form.lead_utm_fields.update(update_params)
      result = @form.update(create_lead_check: params[:create_lead_check].presence || false, product_id: params[:product_id]) && form_updated
    elsif form_name.include?(CONTACT)
      form_updated = @form.contact_utm_fields.update(update_params)
      result = @form.update(create_contact_check: params[:create_contact_check].presence || false) && form_updated
    end

    if result
      flash[:success] = 'Form updated Succesfully!'
    else
      flash[:danger] = 'Failed to update form!'
    end

    redirect_to edit_form_path(@form, form_name: form_name)
  end

  def destroy
    if @form.destroy
      flash[:success] = 'Form deleted Succesfully!'
    else
      flash[:danger] = 'Failed to delete form!'
    end

    redirect_to forms_path
  end

  def save_default_mapping
    @connected_account.update(default_mapping: params['default_mapping'])
  end

  def products_lookup
    products_response = Kylas::GetProducts.new(current_user).lookup(params.permit(:q)[:q])
    render json: {
      results: products_response[:products].map do |product|
                { id: product['id'], text: product['name'] }
              end
    }
  end

  def save_by_user
    @form = Form.find(params[:id])
    if @form.update(saved_by_user: true)
      flash[:success] = t('form.successfully_saved')
      redirect_to forms_path(form_type: NEW)
    else
      flash[:danger] = t('form.failed_to_save')
      redirect_to forms_path(form_type: NEW)
    end
  end

  def sync
    unless @sync_in_progress
      current_user.update_column(:fetch_forms_job_id, FetchAndSaveFormsJob.perform_later(current_user).job_id)
      flash[:success] = t('form.sync_in_progress')
    end

    redirect_to forms_path(form_type: NEW)
  end

  def sync_complete
    render json: { sync_completed: current_user.fetch_forms_job_id.nil? }
  end

  private

  def find_connected_account
    @connected_account = ConnectedAccount.find_by(user_id: current_user.id)
    if @connected_account.nil?
      flash[:danger] = I18n.t('connected_account.not_found')
      redirect_to help_path
    end
  end

  def set_filtered_forms
    return if params[:form].blank? || filter_params[:filter_by].blank? || filter_params[:filter_by_value].blank?

    @filter_by_params = filter_params.to_h

    return if @forms.nil?
    @forms = if @filter_by_params[:filter_by].eql?('page_name')
               @forms.select{ |f| f.page.name.downcase.include?(@filter_by_params[:filter_by_value].downcase.strip) }
             else
               @forms.select{ |f| f.name.downcase.include?(@filter_by_params[:filter_by_value].downcase.strip) }
             end
    @forms = Kaminari.paginate_array(@forms)
  end

  def get_form
    @form = current_user.forms.find_by(id: params[:id])
  end

  def create_params
    params.require(:form).permit(:name, :source_id, :page_id, :user_id, :campaign_id, :kylas_source_id)
  end

  def update_params
    params.require(:utm_field).permit(:campaign_id, :kylas_source_id, :owner_id, :sub_source, :utm_source, :utm_campaign, :utm_medium, :utm_content, :utm_term)
  end

  def filter_params
    params.require(:form).permit(:filter_by, :filter_by_value)
  end
end

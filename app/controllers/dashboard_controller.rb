class DashboardController < ApplicationController
  before_action :authenticate_user!

  def index
    if current_user.connected_account&.is_active
      # Show dashboard with both Facebook and Instagram options
      @facebook_forms_count = current_user.forms.count
      @instagram_accounts_count = current_user.instagram_accounts.count
      @instagram_leads_count = current_user.instagram_leads.count

      # If user has Instagram accounts, show integrated dashboard
      if @instagram_accounts_count > 0
        render :integrated_dashboard
      else
        redirect_to forms_path
      end
    else
      redirect_to auth_index_path
    end
  end

  def help
  end
end

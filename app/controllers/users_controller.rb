# frozen_string_literal: true

class UsersController < ApplicationController
  before_action :authenticate_user!

  def edit
    render :edit
  end

  def update_api_key
    result = current_user.update(user_params)

    if result
      flash[:success] = 'API Key Updated Succesfully!'
    else
      flash[:danger] = 'Failed to update API Key!'
    end

    redirect_to edit_user_path(current_user)
  end

  private

  def user_params
    params.require(:user).permit(:kylas_api_key)
  end
end

class AuthController < ApplicationController

  before_action :authenticate_user!, except: [:webhook]

  def index
    @connected = current_user.connected_account&.is_active
  end

  def webhook
    challenge =  Koala::Facebook::RealtimeUpdates.meet_challenge(params, FB_WEBHOOK_CHALLENGE)
    render plain: params['hub.challenge']
  end

  def callback
    connected_account = current_user.connected_account || current_user.build_connected_account
    connected_account.assign_attributes(is_active: true, email_id: request.env['omniauth.auth'].extra.raw_info.email, access_token: request.env['omniauth.auth'].credentials.token, refresh_token: nil)
    if connected_account.save
      Page.fetch_and_save(connected_account)
      flash[:success] = 'Account connected successfully'
    else
      flash[:danger] = 'Issue with connecting account, Please try again!'
    end
  end
end

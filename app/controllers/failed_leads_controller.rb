class FailedLeadsController < ApplicationController

  def index
    @failed_leads = get_filtered_failed_leads
    @total_failed_leads = @failed_leads.where(status: 'fail').size
    @failed_leads = @failed_leads.page(params[:page]).per(10)
  end

  def retry
    lead = FailedLead.find_by(id: params[:id])
    params = { 'page_id' => lead.form.page_id, 'form_id' => lead.form.source_id, 'leadgen_id' => lead.lead_gen_id}
    success = ProcessLeadGen.new([params], lead).call
    if success
      flash[:success] = "Lead synced successfuly!"
    else
      flash[:danger] = "We are facing issue while syncing this lead!"
    end
    redirect_to failed_leads_path
  end

  def bulk_retry
    if current_user.bulk_job_id.present?
      flash[:danger] = t('failed_leads.another_bulk_job_in_progress')
      redirect_back(fallback_location: failed_leads_path) and return
    end
    
    @failed_leads = get_filtered_failed_leads

    if params[:log_ids].blank?
      flash[:danger] = t('failed_leads.no_selected_logs')
      redirect_back(fallback_location: failed_leads_path) and return
    end

    if params[:log_ids] == 'ALL'
      logs_to_retry = @failed_leads.where(status: FAIL)
    else
      log_ids = params[:log_ids]&.split(',')&.map(&:to_i) || []
      
      logs_to_retry = @failed_leads.where(id: log_ids, status: FAIL)
    end
    
    if logs_to_retry.empty?
      flash[:danger] = t('failed_leads.no_failed_logs')
      redirect_back(fallback_location: failed_leads_path) and return
    end

    log_ids = logs_to_retry.pluck(:id)
    
    bulk_job = BulkJob.create!(
      user_id: current_user.id,
      status: IN_PROGRESS,
      total_count: logs_to_retry.count(:all),
    )
    sidekiq_bulk_job = BulkResyncFailedLeadJob.perform_later(log_ids, current_user.id, bulk_job.id)
    current_user.update_column(:bulk_job_id, sidekiq_bulk_job.job_id)
    bulk_job.update!(job_id: sidekiq_bulk_job.job_id)

    flash[:success] = t("failed_leads.bulk_resync_success", retry_count: logs_to_retry.count(:all))
    redirect_back(fallback_location: failed_leads_path)
  end

  private

  def get_filtered_failed_leads
    @query_params = params 
    @search       = @query_params[:search]
    @failed_leads = FailedLead.joins(:form)
                            .where('form.user_id' => current_user.id)
                            .select('form.name, failed_leads.id, failed_leads.lead_gen_id, failed_leads.error, failed_leads.status, failed_leads.kylas_lead_id, failed_leads.kylas_contact_id, failed_leads.created_at')
                            .order('failed_leads.created_at DESC')

    if @search.present?
      @failed_leads = @failed_leads.where('failed_leads.lead_gen_id::text = ?',@search)
    end

    if @query_params[:entity_id].present?
      @failed_leads = @failed_leads.where(
        'failed_leads.kylas_lead_id::text = ? OR failed_leads.kylas_contact_id::text = ?',
        @query_params[:entity_id],
        @query_params[:entity_id]
      )
    end

    if @query_params[:sync_status].present?
      @failed_leads = @failed_leads.where(status: @query_params[:sync_status])
    end

    if @query_params[:start_date].present? && @query_params[:end_date].present?
        start_date= @query_params[:start_date]
        end_date= @query_params[:end_date]
        @failed_leads = @failed_leads.where('failed_leads.created_at >= ? AND failed_leads.created_at <= ?', start_date, end_date)
    end
    @failed_leads
  end
end

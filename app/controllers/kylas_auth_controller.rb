class KylasAuthController < ApplicationController
  before_action :authenticate_user!

  def index
    response = Kylas::ExchangeCode.new(current_user, params[:code]).call
    if response[:success]
      Rails.logger.info "Old Access Token of #{current_user.id} - #{current_user.kylas_access_token}"
      Rails.logger.info "Old Refresh Token of #{current_user.id} - #{current_user.kylas_refresh_token}"
      current_user.update(kylas_access_token: response[:access_token],
                          kylas_refresh_token: response[:refresh_token],
                          kylas_access_token_expires_at: Time.at(response[:expires_in]))

      Rails.logger.info "New Access Token of #{current_user.id} - #{current_user.reload.kylas_access_token}"
      Rails.logger.info "New Refresh Token of #{current_user.id} - #{current_user.kylas_refresh_token}"
      flash[:success] = "Application installed successfully"
      redirect_to auth_index_path
    else
      flash[:danger] = "We are facing problem while installing app! Please try again"
      redirect_to root_path
    end
  end
end

class ApplicationController < ActionController::Base

  before_action :store_location, :validate_iframe_user
  after_action :allow_iframe

  def validate_iframe_user
    session[:tenantId] = params[:tenantId] if params[:tenantId]
    session[:userId] = params[:userId] if params[:userId]

    return if EXCEPT_CONTROLLER.include?(params[:controller])

    return true if session[:tenantId].blank? && session[:userId].blank?

    return true if current_user.blank?

    if current_user.kylas_user_id.blank? || current_user.kylas_tenant_id.blank?
      if current_user.kylas_api_key?
        response = Kylas::GetUserDetails.new(api_key: current_user.kylas_api_key, user: current_user).call
      else
        response = Kylas::GetUserDetails.new(token: current_user.get_access_token, user: current_user).call
      end
      check_kylas_user(kylas_user_id: response[:id].to_i, kylas_tenant_id: response[:tenant_id].to_i)
    else
      check_kylas_user(kylas_user_id: current_user.kylas_user_id, kylas_tenant_id: current_user.kylas_tenant_id)
    end
  end

  def store_location
    if session[:previous_url].blank? || session[:previous_url] == '/'
      session[:previous_url] = request.fullpath if request.fullpath.include? 'code'
    end

    return session[:previous_url]
  end

  def after_sign_in_path_for(resource)

    session[:tenantId] = params[:tenantId] || session[:tenantId]
    session[:userId] = params[:userId] || session[:userId]
    user= current_user 
    if user.present? && user.country.blank?
      response = Kylas::TenantsDetails.new(user: current_user).fetch
      if response[:success]
        user.update(country: response[:data].dig('country'))
      else
        Rails.logger.warn("returned nil or empty country code for user #{user.id}")
      end
    end

    if user.kylas_webhook_id.blank? && user.kylas_api_key.present?
        webhook_resp = Kylas::Webhook.new(current_user).create
        user.update(kylas_webhook_id: webhook_resp.dig(:data,'id')) if webhook_resp[:success]
    end
       
    if session[:previous_url]
      url = session[:previous_url]
      session[:previous_url] = nil
      url
    elsif current_user.kylas_api_key.blank? && current_user.kylas_refresh_token.blank?
      edit_user_path(current_user)
    elsif current_user.connected_account&.is_active
      forms_path
    else
      auth_index_path
    end
  end

  def after_sign_out_path_for(resource)
    session[:previous_url] = nil
    super
  end

  def append_info_to_payload(payload)
    super
    payload[:level] =
      case payload[:status]
      when 200
        'INFO'
      when 302
        'WARN'
      else
        'ERROR'
      end
    payload[:host] = request.host
    payload[:remote_ip] = request.remote_ip
    payload[:ip] = request.ip
    payload[:user_id] = current_user&.id
  end

  private

  def allow_iframe
    response.headers['X-Frame-Options'] = 'ALLOW-FROM https://app-qa.sling-dev.com/ , ALLOW-FROM https://kylas.io, ALLOW-FROM http://127.0.0.1, ALLOW-FROM https://app-stage.sling-dev.com/, ALLOW-FROM https://crm.sell.do/'
  end

  def check_kylas_user(kylas_user_id:, kylas_tenant_id:)
    if((session[:userId] && kylas_user_id != session[:userId].to_i) || (session[:tenantId] && kylas_tenant_id != session[:tenantId].to_i))
      if current_user.kylas_user_id.blank?
        flash.clear
        flash.keep[:danger] = t('application_reinstall_message')
      else
        flash.clear
        flash.keep[:danger] = t('credentials_mismatch_message')
      end
      sign_out(current_user)
    end
  end
end

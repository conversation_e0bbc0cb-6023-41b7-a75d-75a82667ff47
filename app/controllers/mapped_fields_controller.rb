class MappedFieldsController < ApplicationController
  before_action :get_mapped_field, only: :destroy
  before_action :get_form

  def create
    is_kylas_attr_custom = get_custom_flag_for_kylas_attribute

    result = @form.mapped_fields.create(create_params.merge({ is_custom_kylas_attribute: is_kylas_attr_custom }))

    if result.persisted?
      flash[:success] = 'Mapped field added Succesfully!'
    else
      flash[:danger] = result.errors.full_messages.join(', ')
    end

    form_name = params[:form_name]
    redirect_to edit_form_path(@form, form_name: form_name)
  end

  def destroy
    if @mapped_field.destroy
      flash[:success] = 'Mapped field deleted Succesfully!'
    else
      flash[:danger] = 'Failed to delete mapped field!'
    end

    form_name = params[:form_name]
    redirect_to edit_form_path(@form, form_name: form_name)
  end

  private

  def get_form
    @form = Form.find_by(id: params[:form_id])
  end

  def get_mapped_field
    @mapped_field = MappedField.find_by(id: params[:id])
  end

  def create_params
    params.require(:mapped_field).permit(:kylas_field_name, :fb_field_name, :entity_type, :kylas_field_type)
  end

  def get_custom_flag_for_kylas_attribute
    entity_type = params[:mapped_field][:entity_type]
    if entity_type == LEAD
      attributes_data = Kylas::GetLeadAttributes.new(current_user).call[:attributes]
    elsif entity_type == CONTACT
      attributes_data = Kylas::GetContactAttributes.new(current_user).call[:attributes]
    end

    custom_attr = attributes_data.select{ |attr| attr[:key].eql?(create_params[:kylas_field_name]) }.first

    custom_attr[:is_custom]
  end
end

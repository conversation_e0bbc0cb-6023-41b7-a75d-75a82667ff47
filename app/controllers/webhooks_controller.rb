# frozen_string_literal: true

class WebhooksController < ApplicationController
  protect_from_forgery with: :null_session
  skip_before_action :verify_authenticity_token

  def handler
    current_user = User.find_by(kylas_api_key: request.headers['Api-Key'])
    unless current_user.present?
      head :unauthorized
      return
    end

    ProcessKylasWebhook.perform_later(request.headers['Api-Key'], params.permit!)
    head :ok
  end

end

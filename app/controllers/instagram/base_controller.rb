# frozen_string_literal: true

class Instagram::BaseController < ApplicationController
  before_action :authenticate_user!
  before_action :check_instagram_integration_enabled

  protected

  def current_instagram_account
    @current_instagram_account ||= current_user.instagram_accounts.active.first
  end

  def require_instagram_account
    unless current_instagram_account
      flash[:danger] = 'Please connect an Instagram account first.'
      redirect_to instagram_accounts_path
    end
  end

  def check_instagram_integration_enabled
    # Add any feature flags or environment checks here
    true
  end

  def instagram_integration_breadcrumb
    [
      { name: 'Dashboard', path: root_path },
      { name: 'Instagram Integration', path: instagram_accounts_path }
    ]
  end
end

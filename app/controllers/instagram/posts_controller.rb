# frozen_string_literal: true

class Instagram::PostsController < Instagram::BaseController
  before_action :require_instagram_account
  before_action :set_instagram_post, only: [:show, :sync_comments, :toggle_monitoring]

  def index
    @instagram_posts = current_instagram_account.instagram_posts
                                                .includes(:instagram_comments, :instagram_leads)
                                                .order(published_at: :desc)
                                                .page(params[:page])
                                                .per(20)
    
    # Apply filters
    @instagram_posts = apply_filters(@instagram_posts)
    
    @stats = calculate_posts_stats
    @breadcrumbs = instagram_integration_breadcrumb + [
      { name: 'Posts', path: instagram_posts_path }
    ]
  end

  def show
    @comments = @instagram_post.instagram_comments
                              .includes(:instagram_lead)
                              .order(commented_at: :desc)
                              .page(params[:page])
                              .per(20)
    
    @post_stats = calculate_post_stats
    @breadcrumbs = instagram_integration_breadcrumb + [
      { name: 'Posts', path: instagram_posts_path },
      { name: "Post #{@instagram_post.media_id[0..8]}...", path: instagram_post_path(@instagram_post) }
    ]
  end

  def sync_comments
    Instagram::CommentSyncJob.perform_later(@instagram_post.id)
    flash[:success] = 'Comment sync started for this post.'
    redirect_to instagram_post_path(@instagram_post)
  end

  def toggle_monitoring
    @instagram_post.update!(is_monitored: !@instagram_post.is_monitored)
    status = @instagram_post.is_monitored? ? 'enabled' : 'disabled'
    flash[:success] = "Lead monitoring #{status} for this post."
    redirect_to instagram_post_path(@instagram_post)
  end

  private

  def set_instagram_post
    @instagram_post = current_instagram_account.instagram_posts.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    flash[:danger] = 'Instagram post not found.'
    redirect_to instagram_posts_path
  end

  def apply_filters(posts)
    # Filter by media type
    if params[:media_type].present?
      posts = posts.where(media_type: params[:media_type])
    end
    
    # Filter by monitoring status
    if params[:monitored].present?
      posts = posts.where(is_monitored: params[:monitored] == 'true')
    end
    
    # Filter by engagement level
    case params[:engagement]
    when 'high'
      posts = posts.where('like_count + comment_count + share_count > ?', 100)
    when 'medium'
      posts = posts.where('like_count + comment_count + share_count BETWEEN ? AND ?', 20, 100)
    when 'low'
      posts = posts.where('like_count + comment_count + share_count < ?', 20)
    end
    
    # Filter by date range
    if params[:date_from].present?
      posts = posts.where('published_at >= ?', Date.parse(params[:date_from]))
    end
    
    if params[:date_to].present?
      posts = posts.where('published_at <= ?', Date.parse(params[:date_to]))
    end
    
    posts
  end

  def calculate_posts_stats
    posts = current_instagram_account.instagram_posts
    
    {
      total_posts: posts.count,
      monitored_posts: posts.monitored.count,
      posts_with_comments: posts.where('comment_count > 0').count,
      posts_with_leads: posts.joins(:instagram_leads).distinct.count,
      total_engagement: posts.sum('like_count + comment_count + share_count + save_count'),
      avg_engagement: posts.average('like_count + comment_count + share_count + save_count').to_f.round(2)
    }
  end

  def calculate_post_stats
    {
      total_comments: @instagram_post.comment_count,
      stored_comments: @instagram_post.instagram_comments.count,
      lead_candidates: @instagram_post.instagram_comments.lead_candidates.count,
      generated_leads: @instagram_post.instagram_leads.count,
      engagement_rate: @instagram_post.engagement_rate,
      lead_conversion_rate: @instagram_post.lead_conversion_rate
    }
  end
end

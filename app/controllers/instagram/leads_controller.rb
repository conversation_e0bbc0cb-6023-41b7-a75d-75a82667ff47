# frozen_string_literal: true

class Instagram::LeadsController < Instagram::BaseController
  before_action :require_instagram_account
  before_action :set_instagram_lead, only: [:show, :retry_processing, :update_qualification]

  def index
    @instagram_leads = current_instagram_account.instagram_leads
                                               .includes(:instagram_comment, :instagram_conversation, :form)
                                               .order(created_at: :desc)
                                               .page(params[:page])
                                               .per(20)
    
    # Apply filters
    @instagram_leads = apply_filters(@instagram_leads)
    
    @stats = calculate_leads_stats
    @breadcrumbs = instagram_integration_breadcrumb + [
      { name: 'Leads', path: instagram_leads_path }
    ]
  end

  def show
    @source_object = @instagram_lead.source_object
    @breadcrumbs = instagram_integration_breadcrumb + [
      { name: 'Leads', path: instagram_leads_path },
      { name: "@#{@instagram_lead.lead_username}", path: instagram_lead_path(@instagram_lead) }
    ]
  end

  def retry_processing
    if @instagram_lead.failed?
      @instagram_lead.retry_processing!
      flash[:success] = 'Lead processing retry initiated.'
    else
      flash[:info] = 'Lead is not in failed state.'
    end
    
    redirect_to instagram_lead_path(@instagram_lead)
  end

  def update_qualification
    qualification_status = params[:qualification_status]
    
    if InstagramLead.qualification_statuses.keys.include?(qualification_status)
      @instagram_lead.update!(qualification_status: qualification_status)
      flash[:success] = "Lead qualification updated to #{qualification_status.humanize}."
    else
      flash[:danger] = 'Invalid qualification status.'
    end
    
    redirect_to instagram_lead_path(@instagram_lead)
  end

  def bulk_retry
    failed_leads = current_instagram_account.instagram_leads.failed
    
    if failed_leads.any?
      failed_leads.find_each(&:retry_processing!)
      flash[:success] = "#{failed_leads.count} failed leads queued for retry."
    else
      flash[:info] = 'No failed leads to retry.'
    end
    
    redirect_to instagram_leads_path
  end

  def bulk_qualify
    lead_ids = params[:lead_ids] || []
    qualification_status = params[:qualification_status]
    
    if lead_ids.any? && InstagramLead.qualification_statuses.keys.include?(qualification_status)
      leads = current_instagram_account.instagram_leads.where(id: lead_ids)
      leads.update_all(qualification_status: qualification_status)
      
      flash[:success] = "#{leads.count} leads updated to #{qualification_status.humanize}."
    else
      flash[:danger] = 'Invalid selection or qualification status.'
    end
    
    redirect_to instagram_leads_path
  end

  private

  def set_instagram_lead
    @instagram_lead = current_instagram_account.instagram_leads.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    flash[:danger] = 'Instagram lead not found.'
    redirect_to instagram_leads_path
  end

  def apply_filters(leads)
    # Filter by source type
    if params[:source_type].present?
      leads = leads.where(source_type: params[:source_type])
    end
    
    # Filter by qualification status
    if params[:qualification_status].present?
      leads = leads.where(qualification_status: params[:qualification_status])
    end
    
    # Filter by processing status
    if params[:status].present?
      leads = leads.where(status: params[:status])
    end
    
    # Filter by lead score
    case params[:score]
    when 'high'
      leads = leads.high_score
    when 'medium'
      leads = leads.where('lead_score BETWEEN ? AND ?', 30, 70)
    when 'low'
      leads = leads.where('lead_score < ?', 30)
    end
    
    # Filter by date range
    if params[:date_from].present?
      leads = leads.where('created_at >= ?', Date.parse(params[:date_from]))
    end
    
    if params[:date_to].present?
      leads = leads.where('created_at <= ?', Date.parse(params[:date_to]))
    end
    
    leads
  end

  def calculate_leads_stats
    leads = current_instagram_account.instagram_leads
    
    {
      total_leads: leads.count,
      pending_leads: leads.pending.count,
      successful_leads: leads.successful.count,
      failed_leads: leads.failed.count,
      qualified_leads: leads.qualified.count,
      comment_leads: leads.where(source_type: 'comment').count,
      message_leads: leads.where(source_type: 'message').count,
      avg_lead_score: leads.average(:lead_score).to_f.round(2),
      conversion_rate: calculate_conversion_rate(leads)
    }
  end

  def calculate_conversion_rate(leads)
    return 0 if leads.count.zero?
    
    successful_count = leads.successful.count
    (successful_count.to_f / leads.count * 100).round(2)
  end
end

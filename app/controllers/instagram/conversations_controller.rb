# frozen_string_literal: true

class Instagram::ConversationsController < Instagram::BaseController
  before_action :require_instagram_account
  before_action :set_instagram_conversation, only: [:show, :sync_messages, :mark_as_lead]

  def index
    @instagram_conversations = current_instagram_account.instagram_conversations
                                                       .includes(:instagram_messages, :instagram_lead)
                                                       .order(last_message_time: :desc)
                                                       .page(params[:page])
                                                       .per(20)
    
    # Apply filters
    @instagram_conversations = apply_filters(@instagram_conversations)
    
    @stats = calculate_conversations_stats
    @breadcrumbs = instagram_integration_breadcrumb + [
      { name: 'Conversations', path: instagram_conversations_path }
    ]
  end

  def show
    @messages = @instagram_conversation.instagram_messages
                                      .order(sent_at: :asc)
                                      .page(params[:page])
                                      .per(50)
    
    @conversation_stats = calculate_conversation_stats
    @breadcrumbs = instagram_integration_breadcrumb + [
      { name: 'Conversations', path: instagram_conversations_path },
      { name: "@#{@instagram_conversation.participant_username}", path: instagram_conversation_path(@instagram_conversation) }
    ]
  end

  def sync_messages
    Instagram::MessageSyncJob.perform_later(@instagram_conversation.id)
    flash[:success] = 'Message sync started for this conversation.'
    redirect_to instagram_conversation_path(@instagram_conversation)
  end

  def mark_as_lead
    if @instagram_conversation.is_lead_candidate?
      flash[:info] = 'This conversation is already marked as a lead candidate.'
    else
      @instagram_conversation.update!(
        is_lead_candidate: true,
        lead_score: [INSTAGRAM_LEAD_QUALIFICATION_THRESHOLD, @instagram_conversation.lead_score].max
      )
      
      # Process as lead
      Instagram::ConversationAnalysisJob.perform_later(@instagram_conversation.id)
      
      flash[:success] = 'Conversation marked as lead candidate and queued for processing.'
    end
    
    redirect_to instagram_conversation_path(@instagram_conversation)
  end

  private

  def set_instagram_conversation
    @instagram_conversation = current_instagram_account.instagram_conversations.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    flash[:danger] = 'Instagram conversation not found.'
    redirect_to instagram_conversations_path
  end

  def apply_filters(conversations)
    # Filter by lead status
    case params[:lead_status]
    when 'candidates'
      conversations = conversations.lead_candidates
    when 'processed'
      conversations = conversations.where(is_processed: true)
    when 'unprocessed'
      conversations = conversations.unprocessed
    end
    
    # Filter by conversation status
    if params[:status].present?
      conversations = conversations.where(status: params[:status])
    end
    
    # Filter by lead score
    case params[:score]
    when 'high'
      conversations = conversations.high_score
    when 'medium'
      conversations = conversations.where('lead_score BETWEEN ? AND ?', 30, 70)
    when 'low'
      conversations = conversations.where('lead_score < ?', 30)
    end
    
    # Filter by activity
    case params[:activity]
    when 'recent'
      conversations = conversations.recent
    when 'active'
      conversations = conversations.active
    end
    
    conversations
  end

  def calculate_conversations_stats
    conversations = current_instagram_account.instagram_conversations
    
    {
      total_conversations: conversations.count,
      active_conversations: conversations.active.count,
      lead_candidates: conversations.lead_candidates.count,
      processed_conversations: conversations.where(is_processed: true).count,
      high_score_conversations: conversations.high_score.count,
      recent_conversations: conversations.recent.count
    }
  end

  def calculate_conversation_stats
    {
      total_messages: @instagram_conversation.message_count,
      customer_messages: @instagram_conversation.instagram_messages.from_customer.count,
      business_messages: @instagram_conversation.instagram_messages.from_business.count,
      messages_with_attachments: @instagram_conversation.instagram_messages.with_attachments.count,
      response_time: @instagram_conversation.response_time_minutes,
      lead_score: @instagram_conversation.lead_score,
      has_contact_info: @instagram_conversation.has_contact_info?
    }
  end
end

# frozen_string_literal: true

class Instagram::Accounts<PERSON>ontroller < Instagram::BaseController
  before_action :set_instagram_account, only: [:show, :edit, :update, :destroy, :sync_data, :toggle_status]

  def index
    @instagram_accounts = current_user.instagram_accounts.includes(:instagram_posts, :instagram_conversations, :instagram_leads)
    @connected_account = current_user.connected_account
    @breadcrumbs = instagram_integration_breadcrumb
  end

  def show
    @recent_posts = @instagram_account.instagram_posts.recent.limit(10)
    @recent_conversations = @instagram_account.instagram_conversations.recent.limit(10)
    @recent_leads = @instagram_account.instagram_leads.recent.limit(10)
    @stats = calculate_account_stats
    @breadcrumbs = instagram_integration_breadcrumb + [
      { name: @instagram_account.display_name, path: instagram_account_path(@instagram_account) }
    ]
  end

  def new
    # Redirect to Instagram OAuth
    redirect_to '/auth/instagram_graph', allow_other_host: true
  end

  def create
    # This will be called by the OAuth callback
    auth_data = request.env['omniauth.auth']
    
    begin
      @instagram_account = Instagram::Authentication::OauthHandler.new(current_user, auth_data).call
      
      if @instagram_account.persisted?
        flash[:success] = "Instagram account @#{@instagram_account.username} connected successfully!"
        redirect_to instagram_account_path(@instagram_account)
      else
        flash[:danger] = 'Failed to connect Instagram account. Please try again.'
        redirect_to instagram_accounts_path
      end
    rescue => e
      Rails.logger.error "Instagram OAuth error: #{e.message}"
      flash[:danger] = 'An error occurred while connecting your Instagram account.'
      redirect_to instagram_accounts_path
    end
  end

  def edit
    @field_mappings = @instagram_account.instagram_field_mappings.includes(:user)
    @available_kylas_fields = fetch_available_kylas_fields
    @breadcrumbs = instagram_integration_breadcrumb + [
      { name: @instagram_account.display_name, path: instagram_account_path(@instagram_account) },
      { name: 'Settings', path: edit_instagram_account_path(@instagram_account) }
    ]
  end

  def update
    if @instagram_account.update(account_params)
      flash[:success] = 'Instagram account settings updated successfully!'
      redirect_to instagram_account_path(@instagram_account)
    else
      flash[:danger] = 'Failed to update account settings.'
      render :edit
    end
  end

  def destroy
    username = @instagram_account.username
    
    if @instagram_account.destroy
      flash[:success] = "Instagram account @#{username} disconnected successfully."
    else
      flash[:danger] = 'Failed to disconnect Instagram account.'
    end
    
    redirect_to instagram_accounts_path
  end

  def sync_data
    Instagram::DataFetching::AccountSyncJob.perform_later(@instagram_account.id)
    flash[:success] = 'Account data sync started. This may take a few minutes.'
    redirect_to instagram_account_path(@instagram_account)
  end

  def toggle_status
    @instagram_account.update!(is_active: !@instagram_account.is_active)
    status = @instagram_account.is_active? ? 'activated' : 'deactivated'
    flash[:success] = "Instagram account #{status} successfully."
    redirect_to instagram_accounts_path
  end

  def callback
    # Handle Instagram OAuth callback
    auth_data = request.env['omniauth.auth']
    
    if auth_data.present?
      create
    else
      flash[:danger] = 'Instagram authentication failed. Please try again.'
      redirect_to instagram_accounts_path
    end
  end

  private

  def set_instagram_account
    @instagram_account = current_user.instagram_accounts.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    flash[:danger] = 'Instagram account not found.'
    redirect_to instagram_accounts_path
  end

  def account_params
    params.require(:instagram_account).permit(:is_active)
  end

  def calculate_account_stats
    {
      total_posts: @instagram_account.instagram_posts.count,
      monitored_posts: @instagram_account.instagram_posts.monitored.count,
      total_conversations: @instagram_account.instagram_conversations.count,
      active_conversations: @instagram_account.instagram_conversations.active.count,
      total_leads: @instagram_account.instagram_leads.count,
      qualified_leads: @instagram_account.instagram_leads.qualified.count,
      successful_leads: @instagram_account.instagram_leads.successful.count,
      lead_conversion_rate: calculate_conversion_rate
    }
  end

  def calculate_conversion_rate
    total_interactions = @instagram_account.instagram_comments.count + @instagram_account.instagram_conversations.count
    return 0 if total_interactions.zero?
    
    leads_count = @instagram_account.instagram_leads.count
    (leads_count.to_f / total_interactions * 100).round(2)
  end

  def fetch_available_kylas_fields
    # This would fetch available fields from Kylas API
    # For now, return a basic set
    {
      lead_fields: [
        { name: 'lastName', label: 'Last Name', type: 'text' },
        { name: 'emails', label: 'Email', type: 'email' },
        { name: 'phoneNumbers', label: 'Phone', type: 'phone' },
        { name: 'description', label: 'Description', type: 'textarea' },
        { name: 'companyName', label: 'Company', type: 'text' }
      ],
      contact_fields: [
        { name: 'lastName', label: 'Last Name', type: 'text' },
        { name: 'emails', label: 'Email', type: 'email' },
        { name: 'phoneNumbers', label: 'Phone', type: 'phone' },
        { name: 'companyName', label: 'Company', type: 'text' }
      ]
    }
  end
end

# frozen_string_literal: true

class Instagram::WebhooksController < ApplicationController
  protect_from_forgery with: :null_session
  skip_before_action :verify_authenticity_token
  skip_before_action :authenticate_user!

  def verify
    # Instagram webhook verification
    challenge = params['hub.challenge']
    verify_token = params['hub.verify_token']
    
    if verify_token == INSTAGRAM_WEBHOOK_CHALLENGE
      render plain: challenge
    else
      render plain: 'Verification failed', status: :forbidden
    end
  end

  def handler
    # Handle Instagram webhook events
    begin
      webhook_data = JSON.parse(request.body.read)
      
      # Process each entry in the webhook
      webhook_data['entry']&.each do |entry|
        process_webhook_entry(entry)
      end
      
      render json: { status: 'success' }, status: :ok
    rescue JSON::ParserError => e
      Rails.logger.error "Instagram webhook JSON parse error: #{e.message}"
      render json: { error: 'Invalid JSON' }, status: :bad_request
    rescue => e
      Rails.logger.error "Instagram webhook processing error: #{e.message}"
      render json: { error: 'Processing failed' }, status: :internal_server_error
    end
  end

  private

  def process_webhook_entry(entry)
    instagram_user_id = entry['id']
    
    # Find the Instagram account
    instagram_account = InstagramAccount.find_by(instagram_user_id: instagram_user_id)
    
    unless instagram_account
      Rails.logger.warn "Instagram webhook received for unknown account: #{instagram_user_id}"
      return
    end

    # Process each change in the entry
    entry['changes']&.each do |change|
      process_webhook_change(instagram_account, change)
    end
  end

  def process_webhook_change(instagram_account, change)
    field = change['field']
    value = change['value']
    
    # Create webhook event record
    webhook_event = instagram_account.instagram_webhook_events.create!(
      event_type: field,
      event_id: value['id'],
      object_id: value['object_id'],
      event_data: value
    )
    
    # Process the webhook event asynchronously
    Instagram::WebhookHandlers::WebhookProcessorJob.perform_later(webhook_event.id)
    
    Rails.logger.info "Instagram webhook event created: #{webhook_event.id} for account: #{instagram_account.username}"
  end
end

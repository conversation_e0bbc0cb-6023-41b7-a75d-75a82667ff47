# frozen_string_literal: true

class Instagram::FieldMappingsController < Instagram::BaseController
  before_action :require_instagram_account
  before_action :set_field_mapping, only: [:update, :destroy]

  def create
    @field_mapper = Instagram::KylasIntegration::FieldMapper.new(current_instagram_account)
    
    result = @field_mapper.create_mapping(field_mapping_params)
    
    if result[:success]
      flash[:success] = 'Field mapping created successfully.'
      render json: { success: true, mapping: result[:mapping] }
    else
      flash[:danger] = "Failed to create field mapping: #{result[:errors].join(', ')}"
      render json: { success: false, errors: result[:errors] }, status: :unprocessable_entity
    end
  end

  def update
    @field_mapper = Instagram::KylasIntegration::FieldMapper.new(current_instagram_account)
    
    result = @field_mapper.update_mapping(@field_mapping.id, field_mapping_params)
    
    if result[:success]
      flash[:success] = 'Field mapping updated successfully.'
      render json: { success: true, mapping: result[:mapping] }
    else
      flash[:danger] = "Failed to update field mapping: #{result[:errors].join(', ')}"
      render json: { success: false, errors: result[:errors] }, status: :unprocessable_entity
    end
  end

  def destroy
    @field_mapper = Instagram::KylasIntegration::FieldMapper.new(current_instagram_account)
    
    result = @field_mapper.delete_mapping(@field_mapping.id)
    
    if result[:success]
      flash[:success] = 'Field mapping deleted successfully.'
      render json: { success: true }
    else
      flash[:danger] = "Failed to delete field mapping: #{result[:errors].join(', ')}"
      render json: { success: false, errors: result[:errors] }, status: :unprocessable_entity
    end
  end

  private

  def set_field_mapping
    @field_mapping = current_instagram_account.instagram_field_mappings.find(params[:id])
  rescue ActiveRecord::RecordNotFound
    render json: { success: false, error: 'Field mapping not found' }, status: :not_found
  end

  def field_mapping_params
    params.require(:instagram_field_mapping).permit(
      :instagram_field_name,
      :kylas_field_name,
      :entity_type,
      :source_type,
      :is_custom_kylas_attribute,
      :kylas_field_type,
      :extraction_pattern,
      :is_active
    )
  end
end

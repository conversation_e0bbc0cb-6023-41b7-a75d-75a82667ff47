%header.header-fixed
  %nav.navbar.navbar-expand.bg-white.pb-0.pt-0
    .header-top
      %nav.navbar.navbar-expand-lg.navbar-light.pb-0.pl-0
        %ul.navbar-nav.mr-auto
          - if current_user.kylas_api_key.present? || current_user.kylas_refresh_token.present?
            %li.nav-item{ class: ( "active" if params[:controller] == 'auth' ) }
              #setup-dashboardSection.left-navbar__side-nav__item-section
                = link_to "Facebook Settings", auth_index_path, class: "nav-link pl-0"
            
          - if current_user.connected_account&.is_active
            %li.nav-item{ class: ( "active" if params[:controller] == 'forms' ) }
              #accountSection.left-navbar__side-nav__item-section
                = link_to "Forms", forms_path, class: "nav-link"

            %li.nav-item{ class: ( "active" if params[:controller] == 'failed_leads' ) }
              #accountSection.left-navbar__side-nav__item-section
                = link_to "Sync logs", failed_leads_path, class: "nav-link pl-0"

            %li.nav-item{ class: ( "active" if params[:controller] == 'bulk_jobs' ) }
              #accountSection.left-navbar__side-nav__item-section
                = link_to "Resync logs", bulk_jobs_path, class: "nav-link pl-0"

          / Instagram Integration Menu
          %li.nav-item{ class: ( "active" if params[:controller]&.start_with?('instagram/') ) }
            #instagramSection.left-navbar__side-nav__item-section
              = link_to "Instagram Integration", instagram_accounts_path, class: "nav-link pl-0"

          - unless current_user.kylas_refresh_token
            %li.nav-item{ class: ( "active" if params[:controller] == 'users' ) }
              #data-managementSection.left-navbar__side-nav__item-section 
                = link_to "API Keys", edit_user_path(current_user), class: "nav-link pl-0"

          - if current_user.connected_account&.is_active
            %li.nav-item{ class: ( "active" if params[:controller] == 'dashboard' ) }
              #help-managementSection.left-navbar__side-nav__item-section
                = link_to "Help", help_path(current_user), class: "nav-link pl-0"

      .header-top-right#profile{"aria-disabled" => "true"}
        .dropdown.dropdown-user.ml-2
          %button.btn.dropdown-toggle.p-0{"aria-expanded" => "false", "aria-haspopup" => "true", "data-toggle" => "dropdown", :type => "button"} #{current_user.name ? current_user.name[0] : current_user.email[0]}
          .user-profile-menu.dropdown-menu.dropdown-menu-right{"aria-labelledby" => "userDropdown"}
            = link_to "Logout", destroy_user_session_path, :method => :delete, class: 'dropdown-item cursor-pointer'

%main.main-content.min-height-0.position-relative.main-parent-wrapper.left-nav-open
  .dashboard-layout
    .dashboard-layout__bucket
      = yield

:javascript
  $(function(){
    if(window.top != window.self)
      $('#profile').hide();
  });

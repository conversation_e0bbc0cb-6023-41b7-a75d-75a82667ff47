-# @import "~bootstrap/scss/bootstrap";

!!!
%html
  %head
    %meta{:content => "text/html; charset=UTF-8", "http-equiv" => "Content-Type"}/
    %title #{APP_NAME} Fblead
    %meta{ :name => "robots", :content => "noindex" }/
    %meta{:content => "width=device-width,initial-scale=1", :name => "viewport"}/

    = csrf_meta_tags
    = csp_meta_tag
    = stylesheet_link_tag 'application', media: 'all'
    = javascript_include_tag 'application'
    %link{href: "https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css", rel: "stylesheet"}
    = javascript_include_tag "https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"
    %script{src: "https://cdn.jsdelivr.net/npm/moment@2.29.1/moment.min.js"}
  %body
    - if user_signed_in?
      #app.d-flex.flex-column.h-100
        %span.redux-toastr{"aria-live" => "assertive"}
          %span
            .top-left
            .top-right
            .top-center
            .bottom-left
            .bottom-right
            .bottom-center
        .d-flex.flex-column.h-100
          = render 'layouts/navbar'
      / ==================== Bulk Retry Confirmation Modal ====================
      .modal.fade#retryconfirmation{ tabindex: "-1", data: {backdrop: "true"}, aria: { labelledby: "retryModalLabel", hidden: "true" } }
        .modal-dialog
          .modal-content
            .modal-body
              #retry-confirm-message{ style: "font-size: 16px; font-family: 'Rubik', sans-serif;" }
            = form_with url: bulk_retry_failed_leads_path, method: :post, local: true do |f|

              = hidden_field_tag :log_ids, '', id: 'bulk-retry-log-ids'
              = hidden_field_tag :entity_id, "#{params[:entity_id]}"
              = hidden_field_tag :sync_status, "#{params[:sync_status]}"
              = hidden_field_tag :start_date, "#{params[:start_date]}"
              = hidden_field_tag :end_date, "#{params[:end_date]}"
              = hidden_field_tag :search, "#{params[:search]}"
  
              .modal-footer
                %button.btn.btn-outline-primary{ type: "button", data: { dismiss: "modal" } } No
                = f.submit 'Yes', class: 'btn btn-primary'

    - else
      = yield

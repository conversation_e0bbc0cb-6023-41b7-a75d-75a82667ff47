%link{href: "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css", rel: "stylesheet"}

.row.mb-3.align-items-center
  .col-md
    %h5= t('failed_leads.sync_logs')
  .col-md-6.text-end
    = form_with url: failed_leads_path, method: 'get', local: true, id: 'searchForm' do |form|
      .d-flex.align-items-center.justify-content-end{ style: 'height: 40px;' }
        = form.hidden_field :time_zone, value: Time.zone.name, id: 'time_zone_main', class: 'time_zone'

        .d-flex{ style: 'height: 100%;' }
          = form.text_field :search,
            value: params[:search],
            placeholder: t('failed_leads.search_placeholder'),
            class: 'form-control search-bar-field',
            id: 'searchInput',
            style: 'max-width: 350px; height: 100%; padding: 10px; border: 1px solid #ced4da; border-right: none; border-radius: 0.375rem 0 0 0.375rem;',
            autocomplete: 'off',
            data: { bs_toggle: 'tooltip', bs_placement: 'top', bs_title: t('failed_leads.search_placeholder') }

          %button.btn.btn-outline-secondary#searchBtn{
            type: 'submit',
            style: 'height: 40px; width: 50px; border: 1px solid #ced4da; border-left: none; border-radius: 0 0.375rem 0.375rem 0; display: flex; align-items: center; justify-content: center; padding: 0;'
          }
            %i.fa.fa-search

        %button.btn.btn-outline-secondary#filterBtn{
          style: 'width: 40px; height: 40px; margin-left: 8px; border: 1px solid #ced4da; border-radius: 0.375rem; display: flex; align-items: center; justify-content: center; padding: 0; position: relative;',
          type: 'button',
          data: { bs_toggle: "modal", bs_target: "#filterModal" }
        }
          %i.fa.fa-filter
          - if params[:entity_id].present? || params[:sync_status].present? || (params[:start_date].present? && params[:end_date].present?)
            %span.position-absolute.bg-warning.rounded-circle.border.border-light{
              style: "width: 10px; height: 10px; top: -2px; right: -2px; border-width: 2px; z-index: 2;"
            }

.modal.fade#filterModal{ tabindex: "-1", data: { bs_backdrop: "false" }, aria: { labelledby: "filterModalLabel" } }
  .modal-dialog{ style: "position: absolute; top: 10px; right: 10px; margin-top: 153px;width: 540px; max-width: 90vw;" }
    .modal-content{ style: "max-height: 90vh; overflow-y: auto; border-radius: 0;" }
      .modal-header
        %h5.modal-title#filterModalLabel= t('failed_leads.filter_button')
        %button.btn-close.modal-close-btn{ type: "button", aria: { label: "Close" }, style: "background: none; border: none; padding: 0;" }
          %i.fas.fa-times

      = form_with url: failed_leads_path, method: 'get', local: true do |form|
        .modal-body
          = form.hidden_field :time_zone, value: Time.zone.name, id: 'time_zone_modal', class: 'time_zone'
          .d-flex.align-items-center.mb-3
            = form.label :entity_id, t('failed_leads.entity_id_label'), class: "me-2 mb-0", style: "min-width: 100px;"
            = form.text_field :entity_id,
                value: params[:entity_id],
                class: "form-control",
                id: "entity_id",
                placeholder: t('failed_leads.entity_id_placeholder'),
                type: "number",
                min: "0",
                step: "1",
                oninput: "this.value = this.value.replace(/[^0-9]/g, '')",
                style: "max-width: 25rem; flex-shrink: 0;"
          .d-flex.align-items-center.mb-3
            = form.label :sync_status, t('failed_leads.sync_status_label'), class: "me-2 mb-0", style: "min-width: 100px;"
            = form.select :sync_status,
              options_for_select([[t('failed_leads.sync_status_placeholder'), nil], [t('failed_leads.success'), "success"], [t('failed_leads.fail'), "fail"]], params[:sync_status]),
              { include_blank: false },
              class: "form-select",
              id: "sync_status",
              style: "width: 25rem; flex-shrink: 0;height:2rem; border: 1px solid #ced4da; cursor: pointer;background-color: #fff;color: #{params[:sync_status].present? ? '#212529' : '#6c757d'};"
 
          .row
            .col
              .d-flex.align-items-center{ style: "gap: 2.1rem;" }
                %label.mb-0 Created At:
                = form.text_field :start_date, value: params[:start_date], class: "form-control date-input flex-fill", style: "width: 11.5rem;cursor: pointer;", type: "datetime-local", id: "start_date"
                = form.text_field :end_date, value: params[:end_date], class: "form-control date-input flex-fill", style: "width: 11.5rem;cursor: pointer;", type: "datetime-local", id: "end_date"

        .modal-footer{ style: "display: flex; justify-content: flex-end; gap: 1rem;" }
          %button.btn.btn-danger#clearBtn{ type: "button", style: "min-width: 100px;" }= t('failed_leads.clear_button')
          = form.submit t('failed_leads.apply_button'), class: "btn btn-primary", id: "submitBtn", style: "min-width: 100px;"

:javascript
  document.addEventListener('DOMContentLoaded', function () {
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const submitBtn = document.getElementById('submitBtn');
    const clearBtn = document.getElementById('clearBtn');
    const modal = document.getElementById('filterModal');
    const filterBtn = document.getElementById('filterBtn');
    const closeBtn = modal.querySelector('.modal-close-btn');

    const searchInput = document.getElementById('searchInput');
    const searchBtn = document.getElementById('searchBtn');
    const searchForm = document.getElementById('searchForm');

    const filterValues = {
      entity_id: "#{params[:entity_id]}",
      sync_status: "#{params[:sync_status]}",
      start_date: "#{params[:start_date]}",
      end_date: "#{params[:end_date]}"
    };

    searchBtn?.addEventListener('click', function () {
      Object.entries(filterValues).forEach(([key, value]) => {
        if (value) {
          const hiddenInput = document.createElement('input');
          hiddenInput.type = 'hidden';
          hiddenInput.name = key;
          hiddenInput.value = value;
          searchForm.appendChild(hiddenInput);
        }
      });
    });

    submitBtn?.addEventListener('click', function () {
      const filterForm = submitBtn.closest('form');
      if (searchInput && searchInput.value && filterForm) {
        let searchHidden = filterForm.querySelector('input[name="search"]');
        if (!searchHidden) {
          searchHidden = document.createElement('input');
          searchHidden.type = 'hidden';
          searchHidden.name = 'search';
          filterForm.appendChild(searchHidden);
        }
        searchHidden.value = searchInput.value;
      }
    });

    filterBtn?.addEventListener('click', function () {
      modal.style.display = 'block';
      modal.classList.add('show');
    });

    closeBtn?.addEventListener('click', function () {
      modal.style.display = 'none';
      modal.classList.remove('show');
    });

    const browserTz = Intl.DateTimeFormat().resolvedOptions().timeZone;
    document.querySelectorAll('.time_zone').forEach(input => {
      input.value = browserTz;
    });

    function validateForm() {
      var startDate = $('input#start_date.date-input').val();
      var endDate = $('input#end_date.date-input').val();
      
      if (startDate) {
          $('input#start_date.date-input').attr('min', startDate);
      } else {
          $('input#end_date.date-input').removeAttr('min');
      }
      
      if (endDate) {
          $('input#start_date.date-input').attr('max', endDate);
      } else {
          $('input#end_date.date-input').removeAttr('max');
      }
      
      if (startDate && endDate) {
          var start = new Date(startDate);
          var end = new Date(endDate);
          
          if (start > end) {
              $('#submitBtn').prop('disabled', true);
          } else {
              $('#submitBtn').prop('disabled', false);
          }
      } else {
          if ((startDate && !endDate) || (!startDate && endDate)) {
              $('#submitBtn').prop('disabled', true);
          } else {
              $('#submitBtn').prop('disabled', false);
          }
      }
    }

    document.querySelectorAll('input#start_date.date-input, input#end_date.date-input').forEach(input => {
        input.addEventListener('change', validateForm);
    });

    startDateInput?.addEventListener('change', validateForm);
    endDateInput?.addEventListener('change', validateForm);

    clearBtn?.addEventListener('click', function () {
      document.getElementById('entity_id').value = '';
      document.getElementById('sync_status').selectedIndex = 0;
      startDateInput.value = '';
      endDateInput.value = '';
      validateForm();
    });
  });
  $(document).ready(function () {
    
    let allSelected = false;
    let selectedIds = new Set();
    const totalFailedCount = #{@total_failed_leads || 0};

    const $selectAll = $("#select_all_logs");
    const $bulkActions = $("#bulk-actions-container");
    const $bulkInfoText = $("#bulk-info-text");
    const $selectAllHint = $("#select-all-hint");
    const $unselectAllHint = $("#unselect-all-hint");

    function updateUI() {
      const $checkboxes = $(".log-checkbox");
      const $checkedBoxes = $(".log-checkbox:checked");
      const currentChecked = $checkedBoxes.length;
      const totalOnPage = $checkboxes.length;
      
      if (currentChecked > 0 || allSelected) {
        $bulkActions.addClass("show");
        $("#top-divider").addClass("hide-divider");
        
        const count = allSelected ? totalFailedCount : currentChecked;
        const text = count + ' failed log' + (count > 1 ? 's' : '') + ' selected';
        $bulkInfoText.text(text);

        if (!allSelected && currentChecked > 0) {
          $selectAllHint.show();
        } else {
          $selectAllHint.hide();
        }

        if (allSelected) {
          $unselectAllHint.show();
        } else {
          $unselectAllHint.hide();
        }
      } else {
        $bulkActions.removeClass("show");
        $("#top-divider").removeClass("hide-divider");
        $selectAllHint.hide();
        $unselectAllHint.hide();
      }

      // Update select all checkbox state
      if (totalOnPage > 0) {
        $selectAll.prop("checked", currentChecked === totalOnPage);
        $selectAll.prop("indeterminate", currentChecked > 0 && currentChecked < totalOnPage);
      }
    }

    // Select all on current page
    $selectAll.on("change", function () {
      const isChecked = $(this).is(':checked');
            
      $(".log-checkbox").each(function () {
        $(this).prop('checked', isChecked);
        if (isChecked) {
          selectedIds.add($(this).val());
        } else {
          selectedIds.delete($(this).val());
        }
      });
      
      allSelected = false;
      updateUI();
    });
    $('#bulk-retry-btn').on('click', function(event) {
      const count = allSelected ? #{@total_failed_leads || 0} : selectedIds.size;
      if (count === 0) {
        alert('Please select at least one failed log to resync.');
        event.preventDefault();
        return false;
      }
      
      $('#retry-confirm-message').html('Are you sure you want to Resync ' + count + ' failed log' + (count > 1 ? 's' : '') + '?');

      const logIds = allSelected ? 'ALL' : Array.from(selectedIds).join(',');
      $("#bulk-retry-log-ids").val(logIds);
    });

    // Handle individual checkbox changes
    $(document).on("change", ".log-checkbox", function () {
      const isChecked = $(this).is(':checked');
      const value = $(this).val();
        
      if (isChecked) {
        selectedIds.add(value);
      } else {
        selectedIds.delete(value);
        allSelected = false;
      }
      updateUI();
    });

    // Select all across all pages
    $(document).on("click", "#select-all-logs", function (e) {
      e.preventDefault();
      allSelected = true;
      selectedIds.clear(); 
      $(".log-checkbox").prop("checked", true);
      updateUI();
    });

    // Unselect all
    $(document).on("click", "#unselect-all-logs", function (e) {
      e.preventDefault();
      allSelected = false;
      selectedIds.clear();
      $(".log-checkbox").prop("checked", false);
      updateUI();
    });

    // Initialize UI - ensure bulk actions are hidden initially
    setTimeout(function() {
      $bulkActions.removeClass("show");
      updateUI();
    }, 100);
    $('.created-at').each(function() {
      $(this).text(`${moment($(this).text()).local().format('lll')}`);
    });
  });

= render "devise/shared/flash"

.row.mb-2
  .col-12.d-flex.justify-content-end
    %strong= "#{@failed_leads.total_count} Results"

#bulk-actions-container.bulk-actions-container.p-2.bg-white
  .bulk-info
    %span#bulk-info-text.text-muted.me-3
    %span#select-all-hint.ms-2{ style: "display: none;" }
      = link_to t('failed_leads.select_all', count: @total_failed_leads), '#', id: 'select-all-logs', class: 'text-primary text-decoration-none'
    %span#unselect-all-hint.ms-2{ style: "display: none;" }
      = link_to t('failed_leads.unselect_all'), '#', id: 'unselect-all-logs', class: 'text-primary text-decoration-none'
  %div.mr-1
    %button#bulk-retry-btn.btn.btn-outline-primary{ type: "button", "data-toggle" => "modal", "data-target" => "#retryconfirmation", "data-placement" => "top" }
      %i.fa-solid.fa-arrows-rotate.me-1
      = t('failed_leads.resync')

/ ---------------------- TABLE ----------------------
%table.table.table-bordered
  %thead
    %tr
      %th
        = check_box_tag 'select_all_logs', '', false, id: 'select_all_logs'
      %th= t('failed_leads.form_name')
      %th= t('failed_leads.leadgen_id')
      %th= t('failed_leads.entity_type')
      %th= t('failed_leads.entity_id')
      %th= t('failed_leads.sync_status')
      %th= t('failed_leads.sync_error')
      %th= t('failed_leads.created_at')
      %th= t('failed_leads.actions')

  %tbody
    - @failed_leads.each do |f|
      %tr
        %td
          - if f.status == 'fail'
            %input{ type: "checkbox", class: "log-checkbox", value: f.id }
        %td= f.name
        %td= f.lead_gen_id
        %td
          - if f.kylas_lead_id.present?
            = t('failed_leads.lead')
          - elsif f.kylas_contact_id.present?
            = t('failed_leads.contact')
          - else
            = t('failed_leads.none')
        %td= f.kylas_lead_id.presence || f.kylas_contact_id.presence || t('failed_leads.none')
        %td
          - if f.status_success?
            = t('failed_leads.success')
          - elsif f.status_fail?
            = t('failed_leads.fail')
        %td= f.error
        %td.created-at= f.created_at.iso8601
        %td
          - if f.status_fail?
            = link_to t('failed_leads.sync_now'), retry_failed_lead_path(f.id), class: 'btn btn-sm w-100 btn-primary'

.fixed-bottom.border.bottom-bar.bg-white.p-2
  .mt-2.d-flex.justify-content-end
    = paginate @failed_leads

- content_for :title, "Instagram Integration"

.container-fluid
  .row
    .col-12
      .d-flex.justify-content-between.align-items-center.mb-4
        %h1.h3.mb-0 Instagram Integration
        = link_to "Connect Instagram Account", new_instagram_account_path, class: "btn btn-primary"

  - if @instagram_accounts.any?
    .row
      .col-12
        .card
          .card-header
            %h5.card-title Connected Instagram Accounts
          .card-body
            .table-responsive
              %table.table.table-striped
                %thead
                  %tr
                    %th Account
                    %th Type
                    %th Status
                    %th Posts
                    %th Conversations
                    %th Leads
                    %th Actions
                %tbody
                  - @instagram_accounts.each do |account|
                    %tr
                      %td
                        .d-flex.align-items-center
                          - if account.profile_picture_url.present?
                            %img.rounded-circle.me-2{src: account.profile_picture_url, width: "32", height: "32"}
                          .ms-2
                            %strong= account.display_name
                            %br
                            %small.text-muted= "#{account.follower_count} followers"
                      %td
                        %span.badge{class: account.account_type == 'BUSINESS' ? 'bg-primary' : 'bg-secondary'}
                          = account.account_type.titleize
                      %td
                        - if account.is_active?
                          %span.badge.bg-success Active
                        - else
                          %span.badge.bg-danger Inactive
                      %td= account.instagram_posts.count
                      %td= account.instagram_conversations.count
                      %td= account.instagram_leads.count
                      %td
                        .btn-group{role: "group"}
                          = link_to "View", instagram_account_path(account), class: "btn btn-sm btn-outline-primary"
                          = link_to "Settings", edit_instagram_account_path(account), class: "btn btn-sm btn-outline-secondary"
                          = link_to "Sync", sync_data_instagram_account_path(account), method: :post, class: "btn btn-sm btn-outline-info"

    .row.mt-4
      .col-md-3
        .card.text-center
          .card-body
            %h5.card-title Posts
            %h2.text-primary= @instagram_accounts.sum { |a| a.instagram_posts.count }
            = link_to "View All", instagram_posts_path, class: "btn btn-sm btn-primary"
      .col-md-3
        .card.text-center
          .card-body
            %h5.card-title Conversations
            %h2.text-info= @instagram_accounts.sum { |a| a.instagram_conversations.count }
            = link_to "View All", instagram_conversations_path, class: "btn btn-sm btn-info"
      .col-md-3
        .card.text-center
          .card-body
            %h5.card-title Leads
            %h2.text-success= @instagram_accounts.sum { |a| a.instagram_leads.count }
            = link_to "View All", instagram_leads_path, class: "btn btn-sm btn-success"
      .col-md-3
        .card.text-center
          .card-body
            %h5.card-title Analytics
            %h2.text-warning
              %i.fas.fa-chart-line
            = link_to "View Reports", instagram_analytics_path, class: "btn btn-sm btn-warning"

  - else
    .row
      .col-12
        .card.text-center
          .card-body
            %i.fab.fa-instagram.fa-4x.text-muted.mb-3
            %h4 No Instagram Accounts Connected
            %p.text-muted Connect your Instagram business account to start capturing leads from comments and direct messages.
            
            .row.mt-4
              .col-md-6.offset-md-3
                .card
                  .card-body
                    %h5.card-title Instagram Lead Generation Features
                    %ul.list-unstyled.text-start
                      %li
                        %i.fas.fa-check.text-success.me-2
                        Capture leads from post comments
                      %li
                        %i.fas.fa-check.text-success.me-2
                        Process direct messages for lead qualification
                      %li
                        %i.fas.fa-check.text-success.me-2
                        Automatic lead scoring and qualification
                      %li
                        %i.fas.fa-check.text-success.me-2
                        Real-time webhook notifications
                      %li
                        %i.fas.fa-check.text-success.me-2
                        Integration with Kylas CRM
                      %li
                        %i.fas.fa-check.text-success.me-2
                        Comprehensive analytics and reporting
            
            = link_to "Connect Instagram Account", new_instagram_account_path, class: "btn btn-primary btn-lg mt-3"

  .row.mt-4
    .col-12
      .card
        .card-header
          %h5.card-title Getting Started with Instagram Integration
        .card-body
          .row
            .col-md-4
              .text-center
                %i.fas.fa-link.fa-2x.text-primary.mb-3
                %h6 1. Connect Account
                %p.small Connect your Instagram Business or Creator account to enable lead capture.
            .col-md-4
              .text-center
                %i.fas.fa-cogs.fa-2x.text-info.mb-3
                %h6 2. Configure Settings
                %p.small Set up field mappings and lead qualification criteria.
            .col-md-4
              .text-center
                %i.fas.fa-rocket.fa-2x.text-success.mb-3
                %h6 3. Start Capturing Leads
                %p.small Automatically capture and process leads from Instagram interactions.

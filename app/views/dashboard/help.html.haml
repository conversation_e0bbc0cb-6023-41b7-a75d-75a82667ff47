= render "devise/shared/flash"
%h4 Facebook Settings Step
%ol
  %li.mb-2 Connect your Facebook account
  %li.mb-2
    Lead access should be given to user who is connecting the account in business manager, as well as CRM should be added (include screenshot)
    %div.row
      = image_tag('facebook_settings.png', class: 'help-images')

  %li.mb-2 Lead access on business managers should be enabled and CRM app has to be added
  %li.mb-2
    Check the permission from here
    %a{ href: "https://business.facebook.com/latest/instant_forms/crm_setup" } https://business.facebook.com/latest/instant_forms/crm_setup
  %li.mb-2 Go to Forms section of #{APP_NAME} Facebook lead generation app. You should be able to see all the Facebook pages and corresponding forms available with the pages that you have selected
  %li.mb-2 Choose Source and Campaign (Not mandatory) and Click ‘Add’
  %li.mb-2 Your Leads will start syncing immediately


%h4 How to check the Leads that failed to sync:
%ol
  %li.mb-2 Go to #{APP_NAME} Facebook Lead gen app
  %li.mb-2
    Go to ‘Failed Leads’ section. You will be able to see all the Leads that failed to sync from facebook to #{APP_NAME}
    %div.row
      = image_tag('failed_leads.png', class: 'help-images')

  %li.mb-2 Click on ‘Retry’ in order to sync manually


%h4 How to test Facebook lead gen webhook:
%ol
  %li.mb-2
    Navigate to
    %a{ href: "https://developers.facebook.com/tools/lead-ads-testing" } https://developers.facebook.com/tools/lead-ads-testing

  %li.mb-2 Login with your Facebook account
  %li.mb-2 Choose the Facebook page and form that you want to test
  %li.mb-2 Click on ‘Create Lead’
  %li.mb-2
    Click on ‘Track status’ button to see if there are any errors
    %div.row
      = image_tag('facebook_leadgen.png', class: 'help-images')




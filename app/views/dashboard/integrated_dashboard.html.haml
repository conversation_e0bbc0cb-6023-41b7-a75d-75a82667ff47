- content_for :title, "Dashboard"

.container-fluid
  .row
    .col-12
      %h1.h3.mb-4 Welcome to #{APP_NAME} Lead Integration

  .row.mb-4
    .col-md-6
      .card.h-100
        .card-header.bg-primary.text-white
          %h5.card-title.mb-0
            %i.fab.fa-facebook.me-2
            Facebook Lead Ads
        .card-body
          .row
            .col-6
              %h3.text-primary= @facebook_forms_count
              %p.text-muted Forms Connected
            .col-6
              %h3.text-info= current_user.forms.joins(:failed_leads).distinct.count
              %p.text-muted Total Leads
          .mt-3
            = link_to "Manage Facebook Forms", forms_path, class: "btn btn-primary btn-sm me-2"
            = link_to "View Sync Logs", failed_leads_path, class: "btn btn-outline-primary btn-sm"

    .col-md-6
      .card.h-100
        .card-header.bg-info.text-white
          %h5.card-title.mb-0
            %i.fab.fa-instagram.me-2
            Instagram Integration
        .card-body
          .row
            .col-6
              %h3.text-info= @instagram_accounts_count
              %p.text-muted Accounts Connected
            .col-6
              %h3.text-success= @instagram_leads_count
              %p.text-muted Instagram Leads
          .mt-3
            = link_to "Manage Instagram", instagram_accounts_path, class: "btn btn-info btn-sm me-2"
            = link_to "View Instagram Leads", instagram_leads_path, class: "btn btn-outline-info btn-sm"

  .row.mb-4
    .col-12
      .card
        .card-header
          %h5.card-title Integration Overview
        .card-body
          .row.text-center
            .col-md-3
              .border-end
                %h4.text-primary
                  %i.fas.fa-forms
                %h5 Facebook Forms
                %p.text-muted Capture leads from Facebook Lead Ads with automated form processing
            .col-md-3
              .border-end
                %h4.text-info
                  %i.fab.fa-instagram
                %h5 Instagram Posts
                %p.text-muted Monitor comments on Instagram posts for lead opportunities
            .col-md-3
              .border-end
                %h4.text-success
                  %i.fas.fa-comments
                %h5 Direct Messages
                %p.text-muted Process Instagram DMs for qualified lead conversations
            .col-md-3
                %h4.text-warning
                  %i.fas.fa-chart-line
                %h5 Analytics
                %p.text-muted Track performance across both Facebook and Instagram channels

  .row
    .col-md-8
      .card
        .card-header
          %h5.card-title Recent Activity
        .card-body
          %p.text-muted Recent lead generation activity will appear here once you start capturing leads from both Facebook and Instagram.
          
          .text-center.mt-4
            = link_to "Set up Facebook Integration", auth_index_path, class: "btn btn-outline-primary me-2"
            = link_to "Connect Instagram Account", new_instagram_account_path, class: "btn btn-outline-info"

    .col-md-4
      .card
        .card-header
          %h5.card-title Quick Actions
        .card-body
          .d-grid.gap-2
            = link_to instagram_accounts_path, class: "btn btn-outline-primary" do
              %i.fab.fa-instagram.me-2
              Instagram Dashboard
            = link_to forms_path, class: "btn btn-outline-info" do
              %i.fab.fa-facebook.me-2
              Facebook Forms
            = link_to failed_leads_path, class: "btn btn-outline-secondary" do
              %i.fas.fa-list.me-2
              All Sync Logs
            = link_to help_path, class: "btn btn-outline-dark" do
              %i.fas.fa-question-circle.me-2
              Help & Support

<%= render "devise/shared/flash" %>
<%= stylesheet_link_tag "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css", rel: "stylesheet" %>

<div class="row">
  <div class="col-6">
    <h5 class="pt-2 mb-0">
      <%= t('bulk_job.bulk_job_logs') %>
    </h5>
  </div>
  <div class='col-6 d-flex justify-content-end'>
    <button id="refresh-btn" class="btn btn-sm btn-outline-primary mx-2">
      <i class="fa-solid fa-arrows-rotate"></i>
    </button>
  </div>
</div>

<hr>

<div class="table-responsive">
  <table class="table table-bordered">
    <thead>
      <tr>
        <th><%= t('bulk_job.job_id') %></th>
        <th><%= t('bulk_job.status') %></th>
        <th><%= t('bulk_job.total_count') %></th>
        <th><%= t('bulk_job.passed') %></th>
        <th><%= t('bulk_job.failed') %></th>
        <th><%= t('bulk_job.created_at') %></th>
        <th><%= t('bulk_job.created_by') %></th>
      </tr>
    </thead>
    <tbody>
      <% @bulk_jobs.each do |bulk_job| %>
        <tr>
          <td><%= bulk_job.id %></td>
          <td><%= bulk_job.status.capitalize.gsub('_', ' ') %></td>
          <td><%= "#{bulk_job.total_count} #{bulk_job.total_count <= 1 ? LEAD.titleize : LEADS}" %></td>
          <td style="color: rgb(82, 163, 81);"> <%= "#{bulk_job.success_count} #{bulk_job.success_count <= 1 ? LEAD.titleize : LEADS}" %> </td>
          <td class="text-danger"> <%= "#{bulk_job.failed_count} #{bulk_job.failed_count  <= 1 ? LEAD.titleize : LEADS}" %> </td>
          <td class="created-at"><%= bulk_job.created_at.iso8601 %></td>
          <td><%= bulk_job.user.name.titleize %></td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<div class="fixed-bottom border bottom-bar bg-white p-2">
  <div class='mt-2 d-flex justify-content-end'>
    <%= paginate @bulk_jobs %>
  </div>
</div>

<%= javascript_tag do %>
  $(document).ready(function() {
    $("#refresh-btn").click(function() {
      location.reload();
    });
    $('.created-at').each(function() {
      $(this).text(`${moment($(this).text()).local().format('lll')}`);
    });
  });
<% end %>

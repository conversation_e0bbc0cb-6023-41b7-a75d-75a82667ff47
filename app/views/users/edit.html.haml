- if Rails.env.staging?
  - @url = "https://app-qa.sling-dev.com/setup/integrations/api-keys/list"
- else
  - @url = "#{BASE_HOST}/setup/integrations/api-keys/list"
%p= "#{link_to 'Click here', @url} to get api key".html_safe
= form_for(current_user, url: update_api_key_user_path(current_user), method: :patch, html: { class: "form-horizontal"}) do |f|
  = render "devise/shared/flash"
  .form-group.row.api-key-form
    = f.label :kylas_api_key, "#{APP_NAME} API Key", { :class => 'form-label col-md-2 margin-2-rem required' }
    .text-field.col-md-5.m-0-5-rem
      #email.form-group
      .validate
        = f.text_field :kylas_api_key, autocomplete: "off", placeholder: "#{APP_NAME} API Key", class: 'form-control', required: true
    %i.fa.fa-eye
    .form-group.w-100.mb-0.col-md-1.key-submit-btn
      = f.submit "Submit", id: 'submitBtn', class: 'btn btn-sm w-100 mb-3 mt-auto btn-primary'

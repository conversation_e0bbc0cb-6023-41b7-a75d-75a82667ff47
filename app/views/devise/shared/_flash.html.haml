- flash.each do |name, msg|
  - case name.to_s
    - when 'success'
      .alert.alert-success
        %a.close.cursor-pointer-imp{ "data-dismiss" => "alert"}x
        = msg
    - when 'danger'
      .alert.alert-danger
        %a.close.cursor-pointer-imp{ "data-dismiss" => "alert"}x
        = msg
    - when 'alert'
      .alert.alert-danger
        %a.close.cursor-pointer-imp{ "data-dismiss" => "alert"}x
        = msg
    - when 'notice'
      .alert.alert-info
        %a.close.cursor-pointer-imp{ "data-dismiss" => "alert"}x
        = msg
- flash.clear

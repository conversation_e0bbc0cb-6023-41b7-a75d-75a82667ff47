.authentication-layout
  .div{class: background_class}
  .content-wrapper
    .main-content
      .authentication-form-wrapper
        %img.logo{:alt => "logo", :src => LOGO_URL}
        %h1 Sign in to your account
        %img.sign-in__placeholder{:alt => "sign in placeholder", :src => "sign-in.svg"}
        %p.sign-in__mobile-help-text For better user experience, sign in from a desktop/laptop browser.
        .mb-4
          %strong.signup_link
            %span Don't have an account?
            = link_to "Sign up here", new_registration_path(resource_name), class: 'link-primary'
        = render "devise/shared/flash"

        = form_for(resource, as: resource_name, url: session_path(resource_name)) do |f|
          .form-group
            = f.label 'Email ID', :class => 'form-label required'
            .text-field.col-undefined
              #email.form-group
                .validate
                  = f.email_field :email, autocomplete: "off", placeholder: "Email", id: 'input_email', class: 'form-control', required: true
                  %i.fas.fa-circle
                  %i.password-icon.fas.fa-eye-slash
          -if resource.errors.any? && resource.errors.messages[:email].any?
            %span.error
              Email
              = resource.errors.messages[:email][0]

          .form-group
            = f.label 'Password', :class => 'form-label required'
            .text-field.col-undefined
              #password.form-group
                .validate
                  = f.password_field :password, autocomplete: "current-password", placeholder: "Password", id: 'input_password', class: 'form-control form-control--password', required: true
                  %i.fas.fa-circle
                  %i.password-icon.fas.fa-eye
          -if resource.errors.any? && resource.errors.messages[:password].any?
            %span.error
              Password
              = resource.errors.messages[:password][0]

          .loggedin-forgot-password-link-wrapper
            .custom-control.custom-checkbox.custom-control-inline
              %input#customCheckInline1.custom-control-input{:name => "rememberMe", :type => "checkbox"}/
              %label.custom-control-label{:for => "customCheckInline1"} Keep me logged in
            = link_to "Forgot Password?", new_password_path(resource_name)
          .actions
          = f.submit "Sign in", id: 'loginBtn', class: 'btn btn-md w-100 mb-3 mt-auto btn-primary'
        .bottom-links
          %ul.support-links
            %li
              %a{:href => CONTACT_URL, :target => "_blank"} Contact Us
            %li.separator
            %li
              %a{:href => PRIVACY_URL, :target => "_blank"} Privacy Policy
            %li.separator
            %li
              %a{:href => SUPPORT_URL, :target => "_blank"} Support

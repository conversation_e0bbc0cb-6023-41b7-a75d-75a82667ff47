.authentication-layout
  .div{class: background_class}
  .content-wrapper
    .main-content
      .authentication-form-wrapper
        %h1 Sign up for free
        .mb-4
          %strong
            %span Having an existing account. 
            = link_to "Sign in here", new_session_path(resource_name), class: 'link-primary'
        = render "devise/shared/flash"

        = form_for(resource, as: resource_name, url: registration_path(resource_name)) do |f|
          .form-group
            = f.label 'Email ID', :class => 'form-label required'
            .text-field.col-undefined
              #email.form-group
                .validate
                  = f.email_field :email, autocomplete: "off", placeholder: "Email", id: 'input_email', class: 'form-control', required: true
                  %i.fas.fa-circle
                  %i.password-icon.fas.fa-eye-slash
          - if resource.errors.any? && resource.errors.messages[:email].any?
            %span.error
              Email
              = resource.errors.messages[:email][0]

          .form-group
            = f.label 'Name', :class => 'form-label required'
            .text-field.col-undefined
              #lastName.form-group
                .validate
                  = f.text_field :name, autocomplete: "off", placeholder: "Name", id: 'name', class: 'form-control', required: true
                  %i.fas.fa-circle
                  %i.password-icon.fas.fa-eye-slash
          -if resource.errors.any? && resource.errors.messages[:name].any?
            %span.error
              Name
              = resource.errors.messages[:name][0]

          .form-group
            = f.label 'Password', :class => 'form-label required'
            .text-field.col-undefined
              #password.form-group
                .validate
                  = f.password_field :password, autocomplete: "current-password", placeholder: "Password", id: 'input_password', class: 'form-control form-control--password', required: true
                  %i.fas.fa-circle
                  %i.password-icon.fas.fa-eye
          - if resource.errors.any? && resource.errors.messages[:password].any?
            %span.error
              Password
              = resource.errors.messages[:password][0]

          .form-group.w-100.mb-0
            = f.submit "Sign Up For Free", id: 'loginBtn', class: 'btn btn-md w-100 mb-3 mt-auto btn-primary'
          %p.f-13.m-0.acknowledgement
            By clicking on "Sign up for free" you agree to our 
            %a.link-primary{:href => TERMS_URL, :target => "_blank"}> terms
             and you acknowledge having read our 
            %a.link-primary{:href => PRIVACY_URL, :target => "_blank"}> privacy policy
             .

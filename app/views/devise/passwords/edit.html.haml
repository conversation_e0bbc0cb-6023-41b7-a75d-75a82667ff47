.authentication-layout
  .div{class: background_class}
  .content-wrapper
    .main-content
      .authentication-form-wrapper
        %img.logo{:alt => "logo", :src => LOGO_URL}
        %h2.h2.title Update your password
        %p.message.last-child Please set a password so that you can log in.
        = render "devise/shared/flash"

        = form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :put }) do |f|
          = f.hidden_field :reset_password_token
          .form-group
            = f.label 'New Password', :input_html => { :class => 'form-label' }
            .text-field.col-undefined
              #password.form-group.required
                .validate
                  = f.password_field :password, autocomplete: "current-password", placeholder: "Password", id: 'password', class: 'form-control form-control--password'
                  %i.fas.fa-circle
                  %i.password-icon.fas.fa-eye
          - if resource.errors.any? && resource.errors.messages[:password].any?
            %span.error
              Password
              = resource.errors.messages[:password][0].downcase

          .form-group
            = f.label 'Confirm Password', :input_html => { :class => 'form-label' }
            .text-field.col-undefined
              #password.form-group.required
                .validate
                  = f.password_field :password_confirmation, autocomplete: "current-password", placeholder: "Password", id: 'input_reTypePassword', class: 'form-control form-control--password'
                  %i.fas.fa-circle
                  %i.password-icon.fas.fa-eye
          - if resource.errors.any? && resource.errors.messages[:password_confirmation,].any?
            %span.error
              Confirm Password
              = resource.errors.messages[:password_confirmation][0].downcase

          .actions
            = f.submit "Reset Password", class: 'btn btn-md w-100 mb-3 mt-auto btn-primary'


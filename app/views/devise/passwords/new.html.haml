.authentication-layout
  .div{class: background_class}
  .content-wrapper
    .main-content
      .authentication-form-wrapper
        = link_to "Back To Login", new_session_path(resource_name), class: 'link-primary'
        %h1.mt-3 Forgot your password ?
        %p.mb-4 Don’t worry. We’ll email you a password reset link with instructions.
        = render "devise/shared/flash"

        = form_for(resource, as: resource_name, url: password_path(resource_name), html: { method: :post }) do |f|
          .form-group
            = f.label 'Email ID', :class => 'form-label required'
            .text-field.col-undefined
              #email.form-group
                .validate
                  = f.email_field :email, autocomplete: "off", placeholder: "Email", id: 'input_email', class: 'form-control', required: true
                  %i.fas.fa-circle
                  %i.password-icon.fas.fa-eye-slash
          -if resource.errors.any? && resource.errors.messages[:email].any?
            %span.error
              Email
              = resource.errors.messages[:email][0]
          .actions
          = f.submit "Request Reset Link", class: 'btn btn-md w-100 mb-3 mt-auto btn-primary'

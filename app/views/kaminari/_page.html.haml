-#  Link showing page number
-#  available local variables
-#    page:          a page object for "this" page
-#    url:           url to this page
-#    current_page:  a page object for the currently displayed page
-#    total_pages:   total number of pages
-#    per_page:      number of items to fetch per page
-#    remote:        data-remote
%span{class: "page#{' current' if page.current?}"}
  = link_to page, url, {remote: remote, rel: page.rel, class: page.current? ? 'btn btn-sm btn-success ml-1 mr-1' : 'btn btn-sm btn-primary ml-1 mr-1'}

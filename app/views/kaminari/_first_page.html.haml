-#  Link to the "First" page
-#  available local variables
-#    url:           url to the first page
-#    current_page:  a page object for the currently displayed page
-#    total_pages:   total number of pages
-#    per_page:      number of items to fetch per page
-#    remote:        data-remote
%span.first
  = link_to_unless current_page.first?, t('views.pagination.first').html_safe, url, remote: remote, class: 'btn btn-sm btn-primary ml-1 mr-1'

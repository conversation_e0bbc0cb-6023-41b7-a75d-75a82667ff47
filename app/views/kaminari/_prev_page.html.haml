-#  Link to the "Previous" page
-#  available local variables
-#    url:           url to the previous page
-#    current_page:  a page object for the currently displayed page
-#    total_pages:   total number of pages
-#    per_page:      number of items to fetch per page
-#    remote:        data-remote
%span.prev
  = link_to_unless current_page.first?, t('views.pagination.previous').html_safe, url, rel: 'prev', remote: remote, class: 'btn btn-sm btn-primary ml-1 mr-1'

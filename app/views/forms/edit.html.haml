= render "devise/shared/flash"
%link{:href => "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css", :rel => "stylesheet"}/

%h3
  %span
    = @form.page.name
    %span /
    = @form.name
%br
  .text-message This form will create Leads in #{APP_NAME}
%br

#fields-tab
  %ul.nav.nav-tabs
    %li.active.nav-link
      %a{"data-toggle" => "tab", :href => "#lead-panel"} Lead field mapping
    %li.nav-link
      %a{"data-toggle" => "tab", :href => "#contact-panel"} Contact field mapping
  .tab-content
    #lead-panel.tab-pane.active
      %br
      = hidden_field_tag 'selected_form_name', @form_name
      = form_for(@lead_utm_fields, html: { class: "form-horizontal" }, url: form_path) do |f|
        .form-group.filter-form.form-edit.form-panel-body
          = f.hidden_field 'form_name', value: 'lead_form'
          %br
          .div.row
            .row.form-check.create-lead-contact-row
              = check_box_tag 'create_lead_check', nil, @form.create_lead_check, :class => "form-check-input"
              %label.form-check-label{:for => "form_create_lead_check"} Create Lead
            %br
          .div.row
            .div.col-md-6.row
              .col-md-5
                = f.label @lead_campaign_display_name, :input_html => { :class => 'form-label col-md-3'}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                  = f.select :campaign_id, |
                    @lead_campaigns.collect {|c| [ c[:name], c[:id] ] }, |
                    { prompt: 'Select Campaign', required: true }, |
                    { class: 'form-control' }

            .div.col-md-6.row
              .col-md-2
                = f.label @lead_source_display_name, :input_html => { :class => 'form-label' }
              .text-field.m-0-5-rem.col-md-9
                #filter-by.form-group
                .validate
                  = f.select :kylas_source_id, |
                    @lead_sources.collect {|c| [ c[:name], c[:id] ] }, |
                    { prompt: 'Select Source', required: true }, |
                    { class: 'form-control' }

          .div.row
            .div#product-services.col-md-6.row
              .col-md-5
                = label_tag @lead_product_display_name, nil, {:class => 'form-label col-md-3' }
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                  = select_tag 'product_id', options_for_select([], @form.product_id), { class: 'form-control product_select', prompt: 'Select Product' }

            .div.col-md-6.row
              .col-md-2
                = f.label @lead_owner_display_name, :input_html => { :class => 'form-label' }
              .text-field.m-0-5-rem.col-md-9
                #filter-by.form-group
                .validate
                  = f.select :owner_id, |
                    @users.collect {|c| [ "#{c[:firstName]} #{c[:lastName]}",c[:id] ] }, |
                    { prompt: 'Select Owner', required: true }, |
                    { class: 'form-control' }

          .div.row
            .div.col-md-6.row
              .col-md-5
                = f.label @lead_sub_source_display_name, :input_html => { :class => 'form-label col-md-3'}
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'leads', field: @lead_sub_source_display_name)}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                = f.text_field :sub_source, autocomplete: "off", placeholder: @lead_sub_source_display_name, class: 'form-control'

            .div.col-md-6.row
              .col-md-2
                = f.label @lead_utm_source_display_name, :input_html => { :class => 'form-label' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'leads', field: @lead_utm_source_display_name)}
              .text-field.m-0-5-rem.col-md-9
                #filter-by.form-group
                .validate
                = f.text_field :utm_source, autocomplete: "off", placeholder: @lead_utm_source_display_name, class: 'form-control'

          .div.row
            .div.col-md-6.row
              .col-md-5
                = f.label @lead_utm_campaign_display_name, :input_html => { :class => 'form-label col-md-3' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'leads', field: @lead_utm_campaign_display_name)}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                = f.text_field :utm_campaign, autocomplete: "off", placeholder: @lead_utm_campaign_display_name, class: 'form-control'

            .div.col-md-6.row
              .col-md-2
                = f.label @lead_utm_medium_display_name, :input_html => { :class => 'form-label' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'leads', field: @lead_utm_medium_display_name)}
              .text-field.m-0-5-rem.col-md-9
                #filter-by.form-group
                .validate
                = f.text_field :utm_medium, autocomplete: "off", placeholder: @lead_utm_medium_display_name, class: 'form-control'

          .div.row
            .div.col-md-6.row
              .col-md-5
                = f.label @lead_utm_content_display_name, :input_html => { :class => 'form-label col-md-3' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'leads', field: @lead_utm_content_display_name)}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                = f.text_field :utm_content, autocomplete: "off", placeholder: @lead_utm_content_display_name, class: 'form-control'

            .div.col-md-6.row
              .col-md-2
                = f.label @lead_utm_term_display_name, :input_html => { :class => 'form-label' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'leads', field: @lead_utm_term_display_name)}
              .text-field.m-0-5-rem.col-md-9
                #filter-by.form-group
                .validate
                = f.text_field :utm_term, autocomplete: "off", placeholder: @lead_utm_term_display_name, class: 'form-control'

            .div.col-md-2.row
              .form-group.w-100.mb-0.key-submit-btn.col-md-6{ style: "margin-top: 1.2rem;" }
                %div.row
                  = f.submit "Update", id: 'submitBtn', class: 'btn btn-sm w-100 mb-3 mt-auto btn-primary', formaction: form_path(@form.id), method: :put
      %br
      = form_for(:mapped_field, url: form_mapped_fields_path(form_id: @form.id), method: :post, html: { class: "form-horizontal", style: "width: 80%"}) do |f|
        = hidden_field_tag 'form_name', 'lead_form'
        = f.hidden_field 'form_id', value: @form.id
        .form-group.filter-form.form-edit.d-flex.justify-content-xxl-start
          = f.label "#{APP_NAME} Field", :class => 'form-label required'
          .text-field.m-0-5-rem
            #filter-by.form-group
            .validate
              = f.select :kylas_field_name, |
                @kylas_lead_form_attributes.select { |attr| @kylas_lead_field_names.exclude?(attr[:key]) && attr[:active] }.collect {|c| [ c[:label], c[:key], { 'data-fieldType' => c[:field_type] }] }, |
                { prompt: "Select #{APP_NAME} Field" }, |
                { class: 'form-control lead-mapped-kylas-field', required: true }
          .text-right
            = f.label 'FB Field', :class => 'form-label required'
          .text-field.m-0-5-rem
            #filter-by.form-group
            .validate
              = f.select :fb_field_name, |
                @fb_form_attributes.select { |attr| @fb_lead_field_names.exclude?(attr[:key]) }.collect {|c| [ c[:label], c[:key] ] }, |
                { prompt: 'Select FB field' }, |
                { class: 'form-control', required: true }
          = f.hidden_field :entity_type, value: LEAD
          = f.hidden_field :kylas_field_type, value: nil, class: 'lead-kylas-field-type'
        .form-group.w-100.mb-0.key-submit-btn{ style: "margin-top: 1.2rem;"}
          %div.row.add-field-mapping
            = f.submit "Add Field Mapping", id: 'submitBtn', class: 'btn btn-sm w-40 mb-3 mt-auto btn-success mr-1'
      %table.table.table-bordered
        %thead
          %tr
            %th #{APP_NAME} Fields
            %th Facebook Fields
            %th Actions
        %tbody
          - @lead_mapped_fields.each do |m|
            %tr
              %td{ style: 'width: 40%'}
                =  get_kylas_lead_field_label(m.kylas_field_name)
              %td{ style: 'width: 40%'}
                =  get_fb_field_label(m.fb_field_name, m.kylas_field_name)
              %td
                %div.row
                  - if m.id.present?
                    = link_to "Remove Field Mapping", form_mapped_field_path(id: m.id, form_id: @form.id, form_name: 'lead_form'), data: { href: form_mapped_field_path(id: m.id, form_id: @form.id), method: :delete, confirm: 'Are you sure?' }, class: 'col-md-6 ml-4 mr-1 btn btn-sm w-100 mb-3 mt-auto btn-danger'
                  - else
                    %p.ml-3 #{APP_NAME} and FB standard mappings can't be removed
          = paginate @lead_mapped_fields
    #contact-panel.tab-pane
      %br
      = form_for(@contact_utm_fields, html: { class: 'form-horizontal' }, url: form_path) do |f|
        .form-group.filter-form.form-edit.form-panel-body
          = f.hidden_field 'form_name', value: 'contact_form'
          %br
          .div.row
            .row.form-check.create-lead-contact-row
              = check_box_tag 'create_contact_check', nil, @form.create_contact_check, :class => 'form-check-input'
              %label.form-check-label{:for => "form_create_contact_check"} Create Contact

          .div.row
            .div.col-md-6.row
              .col-md-5
                = f.label @contact_campaign_display_name, :input_html => { :class => 'form-label col-md-3'}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                  = f.select :campaign_id, |
                    @contact_campaigns.collect {|c| [ c[:name], c[:id] ] }, |
                    { prompt: 'Select Campaign', required: true }, |
                    { class: 'form-control' }

            .div.col-md-6.row
              .col-md-2
                = f.label @contact_source_display_name, :input_html => { :class => 'form-label' }
              .text-field.m-0-5-rem.col-md-9
                #filter-by.form-group
                .validate
                  = f.select :kylas_source_id, |
                    @contact_sources.collect {|c| [ c[:name], c[:id] ] }, |
                    { prompt: 'Select Source', required: true }, |
                    { class: 'form-control' }

          .div.row
            .div.col-md-6.row
              .col-md-5
                = f.label @contact_owner_display_name, :input_html => { :class => 'form-label' }
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                  = f.select :owner_id, |
                    @users.collect {|c| [ "#{c[:firstName]} #{c[:lastName]}",c[:id] ] }, |
                    { prompt: 'Select Owner', required: true }, |
                    { class: 'form-control' }

          .div.row
            .div.col-md-6.row
              .col-md-5
                = f.label @contact_sub_source_display_name, :input_html => { :class => 'form-label col-md-6'}
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'contacts', field: @contact_sub_source_display_name)}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                = f.text_field :sub_source, autocomplete: "off", placeholder: @contact_sub_source_display_name, class: 'form-control'

            .div.col-md-6.row
              .col-md-5
                = f.label @contact_utm_source_display_name, :input_html => { :class => 'form-label' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'contacts', field: @contact_utm_source_display_name)}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                = f.text_field :utm_source, autocomplete: "off", placeholder: @contact_utm_source_display_name, class: 'form-control'

          .div.row
            .div.col-md-6.row
              .col-md-5
                = f.label @contact_utm_campaign_display_name, :input_html => { :class => 'form-label col-md-6' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'contacts', field: @contact_utm_campaign_display_name)}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                = f.text_field :utm_campaign, autocomplete: "off", placeholder: @contact_utm_campaign_display_name, class: 'form-control'

            .div.col-md-6.row
              .col-md-5
                = f.label @contact_utm_medium_display_name, :input_html => { :class => 'form-label' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'contacts', field: @contact_utm_medium_display_name)}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                = f.text_field :utm_medium, autocomplete: "off", placeholder: @contact_utm_medium_display_name, class: 'form-control'

          .div.row
            .div.col-md-6.row
              .col-md-5
                = f.label @contact_utm_content_display_name, :input_html => { :class => 'form-label col-md-6' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'contacts', field: @contact_utm_content_display_name)}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                = f.text_field :utm_content, autocomplete: "off", placeholder: @contact_utm_content_display_name, class: 'form-control'

            .div.col-md-6.row
              .col-md-5
                = f.label @contact_utm_term_display_name, :input_html => { :class => 'form-label' }
                %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('utm_field.message', entity: 'contacts', field: @contact_utm_term_display_name)}
              .text-field.m-0-5-rem.col-md-6
                #filter-by.form-group
                .validate
                = f.text_field :utm_term, autocomplete: "off", placeholder: @contact_utm_term_display_name, class: 'form-control'

            .div.col-md-2.row
              .form-group.w-100.mb-0.key-submit-btn.col-md-6{ style: "margin-top: 1.2rem;" }
                %div.row
                  = f.submit "Update", id: 'submitBtn', class: 'btn btn-sm w-100 mb-3 mt-auto btn-primary', formaction: form_path(@form.id), method: :put


      = form_for(:mapped_field, url: form_mapped_fields_path(form_id: @form.id), method: :post, html: { class: "form-horizontal", style: "width: 80%"}) do |f|
        = hidden_field_tag 'form_name', 'contact_form'
        = f.hidden_field 'form_id', value: @form.id
        .form-group.filter-form.form-edit.d-flex.justify-content-xxl-start
          = f.label "#{APP_NAME} Field", :input_html => { :class => 'form-label required'}
          .text-field.m-0-5-rem
            #filter-by.form-group
            .validate
              = f.select :kylas_field_name, |
                @kylas_contact_form_attributes.select { |attr| @kylas_contact_field_names.exclude?(attr[:key]) && attr[:active] }.collect {|c| [ c[:label], c[:key], { 'data-fieldType' => c[:field_type] } ] }, |
                { prompt: "Select #{APP_NAME} Field" }, |
                { class: 'form-control contact-mapped-kylas-field', required: true }
          = f.label 'FB Field', :input_html => { :class => 'form-label required' }
          .text-field.m-0-5-rem
            #filter-by.form-group
            .validate
              = f.select :fb_field_name, |
                @fb_form_attributes.select { |attr| @fb_contact_field_names.exclude?(attr[:key]) }.collect {|c| [ c[:label], c[:key] ] }, |
                { prompt: 'Select FB field' }, |
                { class: 'form-control', required: true }
          = f.hidden_field :entity_type, value: CONTACT
          = f.hidden_field :kylas_field_type, value: nil, class: 'contact-kylas-field-type'
        .form-group.w-100.mb-0.key-submit-btn{ style: "margin-top: 1.2rem;"}
          %div
            = f.submit "Add Field Mapping", id: 'submitBtn', class: 'btn btn-sm w-40 mb-3 mt-auto btn-success mr-1'
      %table.table.table-bordered
        %thead
          %tr
            %th #{APP_NAME} Fields
            %th Facebook Fields
            %th Actions
        %tbody
          - @contact_mapped_fields.each do |m|
            %tr
              %td{ style: 'width: 40%'}
                =  get_kylas_contact_field_label(m.kylas_field_name)
              %td{ style: 'width: 40%'}
                =  get_fb_field_label(m.fb_field_name, m.kylas_field_name)
              %td
                %div.row
                  - if m.id.present?
                    = link_to "Remove Field Mapping", form_mapped_field_path(id: m.id, form_id: @form.id, form_name: 'contact_form'), data: { href: form_mapped_field_path(id: m.id, form_id: @form.id), method: :delete, confirm: 'Are you sure?' }, class: 'col-md-6 ml-4 mr-1 btn btn-sm w-100 mb-3 mt-auto btn-danger'
                  - else
                    %p.ml-3 #{APP_NAME} and FB standard mappings can't be removed
      = paginate @contact_mapped_fields

:javascript
  $(document).ready(function() {
    $('.lead-mapped-kylas-field').change(function(){
      $('.lead-kylas-field-type').val(this.options[this.selectedIndex].dataset.fieldtype);
    });

    $('.contact-mapped-kylas-field').change(function(){
      $('.contact-kylas-field-type').val(this.options[this.selectedIndex].dataset.fieldtype )
    });

    $(".product_select").select2({
      allowClear: true,
      placeholder: "Select Product",
      ajax: {
        delay: 250,
        url: function (params) {
          return `/forms/products-lookup?q=${params.term || ""}`;
        },
        processResults: function (data) {
          return {
            results: data['results']
          };
        }
      }
    });
  })

  var selectedProduct = #{raw @selected_product.flatten}
  if(selectedProduct.length != 0){
    $(".product_select").append(
      `<option value="${selectedProduct[0]}" selected>${selectedProduct[1]}</option>`
    ).trigger("change");
  }

  $(function(){
    if ($("#selected_form_name").val() == 'lead_form'){
      $($("#fields-tab .nav-link a")[0]).click();
      $($("#fields-tab .nav-link")[0]).addClass('active');
      $($("#fields-tab .nav-link")[1]).removeClass('active');
    }else{
      $($("#fields-tab .nav-link a")[1]).click();
      $($("#fields-tab .nav-link")[1]).addClass('active');
      $($("#fields-tab .nav-link")[0]).removeClass('active');
    }

    $("#fields-tab .nav-link").on("click", function(){
      $('#fields-tab .nav-link').removeClass('active');
      $(this).addClass('active');
    })

    function showMessageForCheckboxes(){
      if($('#form_create_lead_check').is(":checked") == true && $('#form_create_contact_check').is(":checked") == true){
        $('.text-message').text("This form will create both Leads and Contacts in #{APP_NAME}");
      }
      else if($('#form_create_lead_check').is(":checked") == true){
        $('.text-message').text("This form will create Leads in #{APP_NAME}");
      }
      else if($('#form_create_contact_check').is(":checked") == true){
        $('.text-message').text("This form will create Contacts in #{APP_NAME}");
      }
      else{
        $('.text-message').text('');
      }
    }

    showMessageForCheckboxes();

    $('.form-check-input').change(function() {
      showMessageForCheckboxes();
    });

  });

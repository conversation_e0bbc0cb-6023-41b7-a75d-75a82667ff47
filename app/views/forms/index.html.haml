= render "devise/shared/flash"
%link{:href => "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.1/css/all.min.css", :rel => "stylesheet"}/

.row.tab-wrapper
  .col-auto
    %ul.nav.custom-toggle-tabs
      %li.nav-item
        = link_to t('form.existing_forms'), forms_path(form_type: EXISTING), class: "nav-link custom-tab #{'active' if @form_type == EXISTING}"
      %li.nav-item
        = link_to t('form.new_forms'), forms_path(form_type: NEW), class: "nav-link custom-tab #{'active' if @form_type == NEW}"

%hr.mb-0

- if @form_type == EXISTING
  / --- Existing Forms Tab Content ---
  #existing-forms.tab-pane.fade{class: (@form_type == EXISTING ? 'show active' : ''), role: "tabpanel"}
    / --- Filter Forms Section ---
    .filter-forms-section.mb-2
      .card.border-1.border-grey.bg-white
        .card-body.p-4
          %h5.card-title.mb-3= t('form.filter_forms')
          = form_for(:form, url: forms_path, method: :get, html: { class: "form-horizontal"}) do |f|
            = f.hidden_field :form_type, value: EXISTING
            .form-group.row.filter-form.form-edit
              = f.label 'Filter By', { :class => 'form-label required'}
              .text-field.col-md-2.m-0-5-rem
                #filter-by.form-group
                .validate
                  = f.select :filter_by, [['Page Name', 'page_name'], ['Form Name', 'form_name']],
                              { prompt: 'Select Filter By', selected: @filter_by_params[:filter_by] || 'Select Filter By' },
                              { class: 'form-control', required: true }

              = f.label 'Value', { :class => 'form-label required' }
              .text-field.col-md-2.m-0-5-rem
                #filter-by.form-group
                .validate
                  = f.text_field :filter_by_value, autocomplete: "off", placeholder: "Enter Value", class: 'form-control', value: @filter_by_params[:filter_by_value],  required: true

              .form-group.w-100.mb-0.col-md-3.key-submit-btn{ style: "margin-top: 1.2rem;"}
                %div.row
                  = f.submit "Search", id: 'submitBtn', class: 'btn btn-sm w-100 mb-3 mt-auto btn-primary col-md-3 mr-1'
                  = link_to "Reset", forms_path(form_type: EXISTING ), class: 'col-md-3 ml-1 mr-1 btn btn-sm w-100 mb-3 mt-auto btn-outline-secondary'
    .form.information-section.mb-2
      .card.border-1.border-grey.bg-white
        .card-body.p-4
          .row
            .col
              = I18n.t('form.information')
   
    %table.table.table-bordered
      %thead
        %tr
          %th Page Name
          %th Form Name
          %th Actions
      %tbody
        - if @forms.present?
          - @forms.each do |f|
            = form_for(f) do |fm|
              %tr
              
                = fm.hidden_field 'source_id', value: f.source_id
                = fm.hidden_field 'page_id', value: f.page_id
                = fm.hidden_field 'user_id', value: f.user_id
              - if f.saved_by_user?
                %td{ style: 'width: 40%'}
                  = f.page.name
                %td{ style: 'width: 40%'}
                  = f.name
                  = fm.hidden_field 'name', value: f.name
                %td
                  %div.row
                    = link_to "Edit", edit_form_path(f, form_type: EXISTING), class: 'col-md-4 ml-4 mr-1 btn btn-sm w-100 mb-3 mt-auto btn-primary'
                    = link_to "Remove", form_path(f.id, form_type: EXISTING), method: :delete, data: { confirm: 'Are you sure?' }, class: 'col-md-4 ml-1 mr-1 btn btn-sm w-100 mb-3 mt-auto btn-danger'
    
    .pagination-container.fixed-bottom.bg-white.py-3.border-top
      = paginate @forms, params: { form_type: EXISTING, filter_by: @filter_by_params[:filter_by], filter_by_value: @filter_by_params[:filter_by_value] }
- elsif @form_type == NEW
  / --- New Form Tab Content ---
  #new-form.tab-pane.fade{class: (@form_type == NEW ? 'show active' : ''), role: "tabpanel"}

    / --- Fetch Facebook Forms Section ---
    .Fetch-forms-section.mb-2
      .card.border-1.border-grey.bg-white
        .card-body.p-4
          %h5.card-title.mb-4
            = t('form.title')
          %p.card-text.text-muted.mb-1
            = t('form.description')  
          .d-flex.justify-content-end.mt-n5
            - if @sync_in_progress
              %button.btn.fetch-btn.px-4{ type: "button", disabled: true }
                %i.fas.fa-download.me-2
                %span.spinner-border.spinner-border-sm.me-2{ role: "status", "aria-hidden": "true" }
                = t('form.fetching_forms')
              :javascript
                document.addEventListener("DOMContentLoaded", function () {
                  let syncCheckInterval = setInterval(() => {
                    fetch("/forms/sync-complete")
                      .then(response => response.json())
                      .then(data => {
                        if (data.sync_completed) {
                          clearInterval(syncCheckInterval);
                          location.reload();
                        }
                      });
                  }, 3000);
                });
            - else
              = link_to sync_forms_path, class: 'btn fetch-btn px-4', method: :post do
                %i.fas.fa-download.me-2
                = t('form.fetch_forms')

    / --- Filter Forms Section ---
    .filter-forms-section.mb-2
      .card.border-1.border-grey.bg-white
        .card-body.p-4
          %h5.card-title.mb-3= t('form.filter_forms')
          = form_for(:form, url: forms_path, method: :get, html: { class: "form-horizontal"}) do |f|
            = f.hidden_field :form_type, value: NEW
            .form-group.row.filter-form.form-edit
              = f.label 'Filter By', { :class => 'form-label required'}
              .text-field.col-md-2.m-0-5-rem
                #filter-by.form-group
                .validate
                  = f.select :filter_by, [['Page Name', 'page_name'], ['Form Name', 'form_name']],
                              { prompt: 'Select Filter By', selected: @filter_by_params[:filter_by] || 'Select Filter By' },
                              { class: 'form-control', required: true }

              = f.label 'Value', { :class => 'form-label required' }
              .text-field.col-md-2.m-0-5-rem
                #filter-by.form-group
                .validate
                  = f.text_field :filter_by_value, autocomplete: "off", placeholder: "Enter Value", class: 'form-control', value: @filter_by_params[:filter_by_value],  required: true

              .form-group.w-100.mb-0.col-md-3.key-submit-btn{ style: "margin-top: 1.2rem;"}
                %div.row
                  = f.hidden_field :form_type, value: NEW
                  = f.submit "Search", id: 'submitBtn', class: 'btn btn-sm w-100 mb-3 mt-auto btn-primary col-md-3 mr-1'
                  = link_to "Reset", forms_path(form_type: NEW), class: 'col-md-3 ml-1 mr-1 btn btn-sm w-100 mb-3 mt-auto btn-outline-secondary'
    .deafault-mapping-section.mb-2
      .card.border-1.border-grey.bg-white
        .card-body.p-4
          .row
            .col
              = I18n.t('form.information')
            .col-md-2
              .row.form-check
                = form_for(@connected_account, url: '') do |f|
                  = f.check_box :default_mapping, class: "form-check-input"
                  %label.form-check-label
                    = I18n.t('default_mapping.title')
                  %i.fa-solid.fa-circle-info.fa-lg{"data-placement": "right", "data-toggle": "tooltip", title: I18n.t('default_mapping.tooltip_text')}

    %table.table.table-bordered
      %thead
        %tr
          %th Page Name
          %th Form Name
          %th Actions
      %tbody
        - if @forms.present?
          - @forms.each do |f|
            = form_for(f) do |fm|
              %tr
                = fm.hidden_field 'source_id', value: f.source_id
                = fm.hidden_field 'page_id', value: f.page_id
                = fm.hidden_field 'user_id', value: f.user_id
              - unless f.saved_by_user?
                %td{ style: 'width: 40%'}
                  = f.page.name
                %td{ style: 'width: 40%'}
                  = f.name
                  = fm.hidden_field 'name', value: f.name
                %td
                  %div.row
                    = link_to "Add", save_by_user_form_path(f, form_type: NEW), method: :patch, class: 'col-md-4 ml-4 mr-1 btn btn-sm w-100 mb-3 mt-auto btn-success'

    .pagination-container.fixed-bottom.bg-white.py-3.border-top
      = paginate @forms, params: { form_type: NEW, filter_by: @filter_by_params[:filter_by], filter_by_value: @filter_by_params[:filter_by_value] }

:javascript
  $('#connected_account_default_mapping').click(function(){
    $.ajax({
      url: "/save-default-mapping",
      type: "patch",
      data: { default_mapping: this.checked }
    });
  });

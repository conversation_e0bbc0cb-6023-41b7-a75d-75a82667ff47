<html>
  <head>
    <title>Client-side Flow Example</title>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.2.2/jquery.min.js" type="text/javascript"></script>
    <script type="text/javascript">
      window.fbAsyncInit = function() {
        FB.init({
          appId: <%= Rails.application.credentials.facebook[:app_id]%>,
          version: 'v4.0',
          cookie: true // IMPORTANT must enable cookies to allow the server to access the session
        });
        console.log("fb init");
      };
(function(d, s, id){
  var js, fjs = d.getElementsByTagName(s)[0];
  if (d.getElementById(id)) {return;}
  js = d.createElement(s); js.id = id;
  js.src = "//connect.facebook.net/en_US/sdk.js";
  fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'facebook-jssdk'));
    </script>
  </head>

  <body>
    <%= render "devise/shared/flash" %>
    <% if @connected %>

      <div id="fb-root"></div>
      <p id="fb-connect">
      <a href="#" class = 'btn btn-primary'>ReConnect to Facebook!</a>
      </p>
      <p id="results" />
    <% else %>
      <div id="fb-root"></div>
      <p id="fb-connect">
      <a href="#" class = 'btn btn-primary'>Connect to Facebook!</a>
      </p>
      <p id="results" />
    <% end %>

    <script type="text/javascript">
      $('#fb-connect').click(function(e) {
        e.preventDefault();
        FB.login(function(response) {
          console.log(response.authResponse.accessToken);
          if (response.authResponse) {
            document.cookie = 'fbsr_'+ <%= Rails.application.credentials.facebook[:app_id] %> + '='+response.authResponse.signedRequest;
            $('#fb-connect').html('Connected! Hitting OmniAuth callback');
            // since we have cookies enabled, this request will allow omniauth to parse
            // out the auth code from the signed request in the fbsr_XXX cookie
            $.getJSON('/auth/facebook/callback',{ signed_request: response.authResponse.signedRequest }, function(json) {
              location.reload(true)
            });
          }
        },  {
          scope: 'email,pages_show_list,pages_read_engagement,pages_manage_metadata,pages_manage_ads,leads_retrieval,ads_management,business_management',
          return_scopes: true
        });; // if you want custom scopes, pass them as an extra, final argument to FB.login
      });
    </script>
  </body>
</html>

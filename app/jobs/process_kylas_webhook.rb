#  frozen_string_literal: true
class ProcessKylasWebhook < ApplicationJob
  sidekiq_options retry: 3
  queue_as :default
  
  def perform(webhook_api_key, parameters)
    user = User.find_by(kylas_api_key: webhook_api_key)
    if user.present?
      if parameters['event'] == 'ACCOUNT_UPDATED' && user.country != parameters.dig('entity', 'country','code')
        user.update!(country: parameters.dig('entity', 'country','code'))
      end
    end
  end
end

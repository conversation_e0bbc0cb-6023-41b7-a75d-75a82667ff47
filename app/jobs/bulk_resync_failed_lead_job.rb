# frozen_string_literal: true

class BulkResyncFailed<PERSON>eadJob < ApplicationJob
  queue_as :default
  sidekiq_options retry: 3
  after_perform :clear_bulk_job_id

  def perform(log_ids, user_id, bulk_job_id)
    logs = FailedLead.where(id: log_ids).order(created_at: :asc)
    bulk_job = BulkJob.find_by(id: bulk_job_id)

    return unless bulk_job.present?

    Rails.logger.info "BulkResyncFailedLeadJob | Processing #{logs.count} logs for Lead"
    success_count = 0
    failed_count = 0
    
    logs.find_each do |log|
      begin
        Rails.logger.info "BulkResyncFailedLeadJob | Retrying webhook for log #{log.id}"
          
        params = { 'page_id' => log.form.page_id, 'form_id' => log.form.source_id, 'leadgen_id' => log.lead_gen_id}
        success = ProcessLeadGen.new([params], log).call

        if success
          success_count += 1
        else
          failed_count += 1
        end

      rescue => e
        Rails.logger.error "BulkResyncFailedLeadJob | Error processing webhook for log #{log.id}: #{e.message}"

        failed_count += 1
      end
    end
    Rails.logger.info "BulkResyncFailedLeadJob | Updating bulk job with success count: #{success_count}, failed count: #{failed_count} and status: #{COMPLETED}. "
    bulk_job.update(success_count: success_count, failed_count: failed_count, status: COMPLETED)
  end

  private

  def clear_bulk_job_id
    user = User.find_by(id: self.arguments[1])
    if user
      user.update!(bulk_job_id: nil)
      Rails.logger.info "BulkResyncFailedLeadJob | User #{user.id} bulk_job_id set to nil after job completion."
    end
  rescue => e
    Rails.logger.info "BulkResyncFailedLeadJob | Failed to clear bulk_job_id for user #{user.id}: #{e.message}"
  end
end

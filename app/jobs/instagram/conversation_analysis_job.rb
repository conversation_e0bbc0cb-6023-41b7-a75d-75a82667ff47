# frozen_string_literal: true

class Instagram::ConversationAnalysisJob < ApplicationJob
  queue_as :instagram
  sidekiq_options retry: 3

  def perform(conversation_id)
    @conversation = InstagramConversation.find(conversation_id)
    
    Rails.logger.info "Analyzing Instagram conversation: #{@conversation.id}"
    
    result = Instagram::LeadProcessing::MessageProcessor.new(@conversation).call
    
    if result[:success]
      Rails.logger.info "Instagram conversation analysis completed: #{@conversation.id} (Lead: #{result[:is_lead]})"
    else
      Rails.logger.error "Instagram conversation analysis failed: #{result[:error]}"
    end
  end
end

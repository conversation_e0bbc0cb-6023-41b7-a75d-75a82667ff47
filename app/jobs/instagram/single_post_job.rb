# frozen_string_literal: true

class Instagram::SinglePostJob < ApplicationJob
  queue_as :instagram
  sidekiq_options retry: 3

  def perform(instagram_account_id, media_id)
    @instagram_account = InstagramAccount.find(instagram_account_id)
    @media_id = media_id
    
    Rails.logger.info "Fetching single Instagram post: #{@media_id} for account: #{@instagram_account.username}"
    
    fetch_and_create_post
  end

  private

  def fetch_and_create_post
    access_token = @instagram_account.get_valid_access_token
    return unless access_token

    url = "https://graph.instagram.com/#{@media_id}"
    params = {
      fields: 'id,media_type,media_url,thumbnail_url,permalink,caption,timestamp,like_count,comments_count,shares_count,saves_count',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    
    if data
      create_post_from_data(data)
    end
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Failed to fetch Instagram post #{@media_id}: #{e.response.body}"
  rescue => e
    Rails.logger.error "Error fetching Instagram post #{@media_id}: #{e.message}"
  end

  def create_post_from_data(data)
    post = @instagram_account.instagram_posts.find_or_create_by(media_id: data['id']) do |p|
      p.media_type = data['media_type']
      p.caption = data['caption']
      p.permalink = data['permalink']
      p.media_url = data['media_url']
      p.thumbnail_url = data['thumbnail_url']
      p.published_at = Time.parse(data['timestamp']) if data['timestamp']
      p.like_count = data['like_count'] || 0
      p.comment_count = data['comments_count'] || 0
      p.share_count = data['shares_count'] || 0
      p.save_count = data['saves_count'] || 0
      p.is_monitored = true # Auto-monitor posts that come through webhooks
    end

    Rails.logger.info "Created/updated Instagram post: #{post.media_id}"
    
    # Schedule comment sync if there are comments
    if post.comment_count > 0
      Instagram::CommentSyncJob.perform_later(post.id)
    end
  end
end

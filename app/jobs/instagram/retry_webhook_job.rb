# frozen_string_literal: true

class Instagram::RetryWebhookJob < ApplicationJob
  queue_as :instagram_webhooks
  sidekiq_options retry: 2

  def perform(webhook_event_id)
    @webhook_event = InstagramWebhookEvent.find(webhook_event_id)
    
    Rails.logger.info "Retrying Instagram webhook event: #{@webhook_event.id} (Attempt #{@webhook_event.retry_count + 1})"
    
    @webhook_event.retry!
  end
end

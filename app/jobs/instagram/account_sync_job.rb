# frozen_string_literal: true

class Instagram::AccountSyncJob < ApplicationJob
  queue_as :instagram
  sidekiq_options retry: 3

  def perform(instagram_account_id)
    @instagram_account = InstagramAccount.find(instagram_account_id)
    
    Rails.logger.info "Starting Instagram account sync for: #{@instagram_account.username}"
    
    begin
      sync_account_data
      sync_recent_media
      
      Rails.logger.info "Completed Instagram account sync for: #{@instagram_account.username}"
    rescue => e
      Rails.logger.error "Instagram account sync failed for #{@instagram_account.username}: #{e.message}"
      raise e
    end
  end

  private

  def sync_account_data
    Instagram::DataFetching::AccountData.new(@instagram_account).call
  end

  def sync_recent_media
    Instagram::DataFetching::MediaData.new(@instagram_account).call
  end
end

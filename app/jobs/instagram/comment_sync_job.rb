# frozen_string_literal: true

class Instagram::CommentSyncJob < ApplicationJob
  queue_as :instagram
  sidekiq_options retry: 3

  def perform(instagram_post_id)
    @instagram_post = InstagramPost.find(instagram_post_id)
    
    Rails.logger.info "Starting Instagram comment sync for post: #{@instagram_post.media_id}"
    
    result = Instagram::DataFetching::CommentData.new(@instagram_post).call
    
    if result[:success]
      Rails.logger.info "Instagram comment sync completed for post: #{@instagram_post.media_id} (#{result[:count]} comments)"
    else
      Rails.logger.error "Instagram comment sync failed for post #{@instagram_post.media_id}: #{result[:error]}"
    end
  end
end

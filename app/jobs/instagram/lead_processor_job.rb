# frozen_string_literal: true

class Instagram::LeadProcessorJob < ApplicationJob
  queue_as :instagram
  sidekiq_options retry: 5

  def perform(instagram_lead_id)
    @instagram_lead = InstagramLead.find(instagram_lead_id)
    
    Rails.logger.info "Processing Instagram lead: #{@instagram_lead.id}"
    
    @instagram_lead.process_to_kylas!
    
    Rails.logger.info "Instagram lead processing completed: #{@instagram_lead.id} (Status: #{@instagram_lead.status})"
  end
end

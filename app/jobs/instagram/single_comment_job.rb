# frozen_string_literal: true

class Instagram::SingleCommentJob < ApplicationJob
  queue_as :instagram
  sidekiq_options retry: 3

  def perform(comment_id)
    @comment = InstagramComment.find(comment_id)
    @account = @comment.instagram_post.instagram_account
    
    Rails.logger.info "Fetching full data for Instagram comment: #{@comment.comment_id}"
    
    fetch_and_update_comment
  end

  private

  def fetch_and_update_comment
    access_token = @account.get_valid_access_token
    return unless access_token

    url = "https://graph.instagram.com/#{@comment.comment_id}"
    params = {
      fields: 'id,text,username,timestamp,like_count,replies_count,hidden,user',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    
    if data
      update_comment_from_data(data)
    end
  rescue RestClient::ExceptionWithResponse => e
    Rails.logger.error "Failed to fetch Instagram comment #{@comment.comment_id}: #{e.response.body}"
  rescue => e
    Rails.logger.error "Error fetching Instagram comment #{@comment.comment_id}: #{e.message}"
  end

  def update_comment_from_data(data)
    @comment.update!(
      like_count: data['like_count'] || 0,
      reply_count: data['replies_count'] || 0,
      is_hidden: data['hidden'] || false,
      user_profile_data: @comment.user_profile_data.merge(data['user'] || {})
    )

    Rails.logger.info "Updated Instagram comment: #{@comment.comment_id}"
    
    # Fetch replies if any
    if @comment.reply_count > 0
      fetch_comment_replies
    end
  end

  def fetch_comment_replies
    access_token = @account.get_valid_access_token
    return unless access_token

    url = "https://graph.instagram.com/#{@comment.comment_id}/replies"
    params = {
      fields: 'id,text,username,timestamp,like_count,user',
      access_token: access_token
    }

    response = RestClient.get(url, params: params)
    data = JSON.parse(response.body) if response.code == 200
    
    if data && data['data']
      data['data'].each do |reply_data|
        create_comment_reply(reply_data)
      end
    end
  rescue => e
    Rails.logger.error "Failed to fetch comment replies: #{e.message}"
  end

  def create_comment_reply(reply_data)
    post = @comment.instagram_post
    
    reply = post.instagram_comments.find_or_create_by(comment_id: reply_data['id']) do |r|
      r.text = reply_data['text']
      r.username = reply_data['username']
      r.user_id = reply_data.dig('user', 'id')
      r.commented_at = Time.parse(reply_data['timestamp']) if reply_data['timestamp']
      r.like_count = reply_data['like_count'] || 0
      r.parent_comment_id = @comment.comment_id
      r.user_profile_data = reply_data['user'] || {}
    end

    # Schedule analysis for the reply
    if reply.persisted? && reply.text.present?
      Instagram::CommentAnalysisJob.perform_later(reply.id)
    end
  end
end

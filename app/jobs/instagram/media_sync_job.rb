# frozen_string_literal: true

class Instagram::MediaSyncJob < ApplicationJob
  queue_as :instagram
  sidekiq_options retry: 3

  def perform(instagram_account_id)
    @instagram_account = InstagramAccount.find(instagram_account_id)
    
    Rails.logger.info "Starting Instagram media sync for: #{@instagram_account.username}"
    
    result = Instagram::DataFetching::MediaData.new(@instagram_account).call
    
    if result[:success]
      Rails.logger.info "Instagram media sync completed for: #{@instagram_account.username} (#{result[:count]} posts)"
    else
      Rails.logger.error "Instagram media sync failed for #{@instagram_account.username}: #{result[:error]}"
    end
  end
end

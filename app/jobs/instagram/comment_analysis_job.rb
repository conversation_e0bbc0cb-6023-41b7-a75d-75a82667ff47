# frozen_string_literal: true

class Instagram::CommentAnalysisJob < ApplicationJob
  queue_as :instagram
  sidekiq_options retry: 3

  def perform(comment_id)
    @comment = InstagramComment.find(comment_id)
    
    Rails.logger.info "Analyzing Instagram comment: #{@comment.id}"
    
    result = Instagram::LeadProcessing::CommentProcessor.new(@comment).call
    
    if result[:success]
      Rails.logger.info "Instagram comment analysis completed: #{@comment.id} (Lead: #{result[:is_lead]})"
    else
      Rails.logger.error "Instagram comment analysis failed: #{result[:error]}"
    end
  end
end

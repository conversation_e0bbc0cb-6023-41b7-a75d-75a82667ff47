# frozen_string_literal: true

class EntityGenerationReportJob < ApplicationJob
  def perform(user)
    failed_entities = FailedLead.joins(:form).where("forms.user_id": user.id, status: 'fail').where("failed_leads.created_at > ?", (DateTime.now - 1.hour))

    if failed_entities.exists?
      success_entities = FailedLead.joins(:form).where("forms.user_id": user.id, status: 'success').where("failed_leads.created_at > ?", (DateTime.now - 1.hour)).count
      total_entities = failed_entities.count + success_entities
      EntityGenerationReportMailer.entity_summary(user, total_entities, failed_entities.count, success_entities).deliver
    end
  end
end

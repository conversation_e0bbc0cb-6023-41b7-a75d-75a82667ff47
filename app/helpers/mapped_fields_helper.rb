module MappedField<PERSON><PERSON><PERSON><PERSON>

  def get_kylas_lead_field_label(kylas_field_name)
    obj = @kylas_lead_form_attributes.select { |obj| obj[:key].eql?(kylas_field_name)}

    obj.present? ? obj.first[:label] : kylas_field_name
  end

  def get_kylas_contact_field_label(kylas_field_name)
    obj = @kylas_contact_form_attributes.select { |obj| obj[:key].eql?(kylas_field_name)}

    obj.present? ? obj.first[:label] : kylas_field_name
  end

  def get_fb_field_label(fb_field_name, kylas_field_name)

    obj = @fb_form_attributes.select { |obj| obj[:key].eql?(fb_field_name) }

    obj.present? ? obj.first[:label] : fb_field_name
  end
end

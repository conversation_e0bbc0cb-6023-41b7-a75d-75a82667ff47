# frozen_string_literal: true

class FacebookAd < ApplicationRecord
  # Associations
  belongs_to :facebook_ad_set
  has_one :facebook_campaign, through: :facebook_ad_set
  has_one :user, through: :facebook_campaign

  # Validations
  validates :ad_id, presence: true, uniqueness: true
  validates :name, presence: true

  # Scopes
  scope :active, -> { where(status: 'ACTIVE') }
  scope :paused, -> { where(status: 'PAUSED') }
  scope :recent, -> { where('created_at > ?', 30.days.ago) }
  scope :with_performance, -> { where('impressions > 0') }
  scope :high_performing, -> { where('ctr > 0.01 AND leads > 0') }

  # Enums
  enum status: {
    'ACTIVE' => 'ACTIVE',
    'PAUSED' => 'PAUSED',
    'DELETED' => 'DELETED'
  }

  def is_active?
    status == 'ACTIVE' && effective_status == 'ACTIVE'
  end

  def creative_type
    return 'video' if video_url.present?
    return 'image' if image_url.present?
    'text'
  end

  def creative_summary
    summary = []
    summary << "Headline: #{headline.truncate(50)}" if headline.present?
    summary << "Body: #{body.truncate(100)}" if body.present?
    summary << "CTA: #{call_to_action_type.humanize}" if call_to_action_type.present?
    summary.join(' | ')
  end

  def performance_metrics
    {
      impressions: impressions,
      clicks: clicks,
      leads: leads,
      spend: spend,
      cpm: cpm,
      cpc: cpc,
      cpl: cpl,
      ctr: ctr,
      frequency: frequency,
      reach: reach,
      conversion_rate: calculate_conversion_rate,
      relevance_score: calculate_relevance_score
    }
  end

  def calculate_conversion_rate
    return 0 if clicks.zero?
    
    (leads.to_f / clicks * 100).round(4)
  end

  def calculate_relevance_score
    # Facebook's relevance score equivalent based on CTR and engagement
    return 0 if impressions.zero?
    
    base_score = ctr * 1000 # Convert CTR to a base score
    engagement_bonus = (clicks + leads) / impressions.to_f * 100
    
    score = (base_score + engagement_bonus).round(2)
    [score, 10].min # Cap at 10 (Facebook's old relevance score scale)
  end

  def creative_performance_score
    score = 0
    
    # CTR scoring (0-40 points)
    score += [ctr * 4000, 40].min
    
    # Conversion rate scoring (0-30 points)
    conv_rate = calculate_conversion_rate
    score += [conv_rate * 3, 30].min
    
    # Frequency scoring (0-20 points) - lower frequency is better
    if frequency > 0
      frequency_score = frequency < 2 ? 20 : [40 / frequency, 5].max
      score += frequency_score
    end
    
    # Relevance scoring (0-10 points)
    score += calculate_relevance_score
    
    score.round(2)
  end

  def creative_fatigue_level
    return 'unknown' if frequency.zero?
    
    case frequency
    when 0..1.5
      'fresh'
    when 1.5..2.5
      'optimal'
    when 2.5..4
      'moderate_fatigue'
    when 4..6
      'high_fatigue'
    else
      'severe_fatigue'
    end
  end

  def sync_insights!
    Facebook::Marketing::AdInsights.new(self).call
  end

  def pause_ad!
    Facebook::Marketing::AdManagement.new(self).pause
  end

  def resume_ad!
    Facebook::Marketing::AdManagement.new(self).resume
  end

  def duplicate_ad!(new_name = nil)
    Facebook::Marketing::AdManagement.new(self).duplicate(new_name)
  end

  def update_creative!(creative_params)
    Facebook::Marketing::AdManagement.new(self).update_creative(creative_params)
  end

  def days_running
    return 0 unless created_time.present?
    
    ((Time.current - created_time) / 1.day).ceil
  end

  def average_daily_performance
    return {} if days_running.zero?
    
    {
      impressions: (impressions.to_f / days_running).round,
      clicks: (clicks.to_f / days_running).round,
      leads: (leads.to_f / days_running).round(2),
      spend: (spend / days_running).round(2)
    }
  end

  def optimization_recommendations
    recommendations = []
    
    # Performance-based recommendations
    case creative_performance_score
    when 0..25
      recommendations << 'Poor performance - consider pausing or updating creative'
    when 25..50
      recommendations << 'Below average performance - test new creative elements'
    when 50..75
      recommendations << 'Good performance - consider scaling or testing variations'
    else
      recommendations << 'Excellent performance - scale budget and create similar ads'
    end
    
    # Frequency-based recommendations
    case creative_fatigue_level
    when 'moderate_fatigue'
      recommendations << 'Creative showing fatigue - consider refreshing imagery or copy'
    when 'high_fatigue', 'severe_fatigue'
      recommendations << 'High creative fatigue - update creative immediately'
    end
    
    # CTR-based recommendations
    if ctr < 0.005
      recommendations << 'Low CTR - test more compelling headlines or visuals'
    end
    
    # Conversion-based recommendations
    conv_rate = calculate_conversion_rate
    if conv_rate < 1 && clicks > 100
      recommendations << 'Low conversion rate - review landing page or form'
    end
    
    recommendations
  end

  def a_b_test_suggestions
    suggestions = []
    
    # Headline variations
    if headline.present?
      suggestions << 'Test emotional vs. rational headlines'
      suggestions << 'Test question-based vs. statement headlines'
    end
    
    # Visual variations
    case creative_type
    when 'image'
      suggestions << 'Test lifestyle vs. product-focused images'
      suggestions << 'Test different color schemes or compositions'
    when 'video'
      suggestions << 'Test different video lengths or opening hooks'
    end
    
    # CTA variations
    if call_to_action_type.present?
      suggestions << 'Test different call-to-action buttons'
    end
    
    suggestions
  end

  def competitive_analysis_data
    # This would integrate with competitive intelligence tools
    # For now, return basic performance benchmarks
    {
      industry_avg_ctr: 0.012, # This should be dynamic based on industry
      industry_avg_cpl: 35,
      performance_vs_industry: {
        ctr: ctr > 0.012 ? 'above_average' : 'below_average',
        cpl: cpl < 35 ? 'above_average' : 'below_average'
      }
    }
  end
end

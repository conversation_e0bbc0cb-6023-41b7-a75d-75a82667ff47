# frozen_string_literal: true

class Form < ApplicationRecord
  has_many :failed_leads, dependent: :destroy
  has_many :mapped_fields, dependent: :destroy
  has_many :utm_fields, dependent: :destroy

  belongs_to :page
  belongs_to :user

  #after_create :create_default_mapped_and_utm_fields

  before_save :trigger_default_mapped_and_utm_fields, if: :should_create_default_fields?

  def mapped_lead_fields
    mapped_fields.where(entity_type: LEAD)
  end

  def mapped_contact_fields
    mapped_fields.where(entity_type: CONTACT)
  end

  def lead_utm_fields
    utm_fields.find_by(entity_type: LEAD)
  end

  def contact_utm_fields
    utm_fields.find_by(entity_type: CONTACT)
  end

  private

  def create_default_mapped_and_utm_fields
    connected_account = ConnectedAccount.find_by(user_id: user_id)
    return if connected_account.nil?

    if connected_account.default_mapping
      data_to_be_created = [
        { kylas_field_name: 'lastName', fb_field_name: 'full_name', is_standard: true },
        { kylas_field_name: 'emails', fb_field_name: 'email', is_standard: true },
        { kylas_field_name: 'phoneNumbers', fb_field_name: 'phone_number', is_standard: true }
      ]

      mapped_fields.create(data_to_be_created)
    end

    utm_fields.create!(entity_type: LEAD)
    utm_fields.create!(entity_type: CONTACT)
  end

  def should_create_default_fields?
    saved_by_user_changed? && saved_by_user_was == false && saved_by_user == true
  end

  def trigger_default_mapped_and_utm_fields
    create_default_mapped_and_utm_fields
  end
end

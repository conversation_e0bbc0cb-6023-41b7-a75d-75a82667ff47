# frozen_string_literal: true

class FacebookAdSet < ApplicationRecord
  # Associations
  belongs_to :facebook_campaign
  has_many :facebook_ads, dependent: :destroy
  has_one :user, through: :facebook_campaign

  # Validations
  validates :ad_set_id, presence: true, uniqueness: true
  validates :name, presence: true
  validates :optimization_goal, presence: true

  # Scopes
  scope :active, -> { where(status: 'ACTIVE') }
  scope :paused, -> { where(status: 'PAUSED') }
  scope :lead_generation, -> { where(optimization_goal: 'LEAD_GENERATION') }
  scope :recent, -> { where('created_at > ?', 30.days.ago) }
  scope :with_budget, -> { where.not(daily_budget: nil).or(where.not(lifetime_budget: nil)) }

  # Enums
  enum status: {
    'ACTIVE' => 'ACTIVE',
    'PAUSED' => 'PAUSED',
    'DELETED' => 'DELETED'
  }

  enum optimization_goal: {
    'LEAD_GENERATION' => 'LEAD_GENERATION',
    'LINK_CLICKS' => 'LINK_CLICKS',
    'IMPRESSIONS' => 'IMPRESSIONS',
    'REACH' => 'REACH',
    'LANDING_PAGE_VIEWS' => 'LANDING_PAGE_VIEWS'
  }

  enum billing_event: {
    'IMPRESSIONS' => 'IMPRESSIONS',
    'CLICKS' => 'CLICKS',
    'ACTIONS' => 'ACTIONS'
  }

  def is_active?
    status == 'ACTIVE' && effective_status == 'ACTIVE'
  end

  def is_running?
    is_active? && 
    (start_time.nil? || start_time <= Time.current) &&
    (end_time.nil? || end_time > Time.current)
  end

  def budget_type
    return 'daily' if daily_budget.present?
    return 'lifetime' if lifetime_budget.present?
    'campaign'
  end

  def current_budget
    daily_budget || lifetime_budget || 0
  end

  def budget_utilization
    return 0 if current_budget.zero?
    
    (spend / current_budget * 100).round(2)
  end

  def targeting_summary
    return 'No targeting data' if targeting_data.blank?
    
    summary = []
    
    # Age targeting
    if targeting_data['age_min'] || targeting_data['age_max']
      age_range = "#{targeting_data['age_min'] || 18}-#{targeting_data['age_max'] || 65}"
      summary << "Age: #{age_range}"
    end
    
    # Gender targeting
    if targeting_data['genders']
      genders = targeting_data['genders'].map { |g| g == 1 ? 'Male' : 'Female' }
      summary << "Gender: #{genders.join(', ')}"
    end
    
    # Location targeting
    if targeting_data['geo_locations']
      locations = targeting_data['geo_locations']['countries'] || []
      summary << "Countries: #{locations.join(', ')}" if locations.any?
    end
    
    # Interest targeting
    if targeting_data['interests']
      interests = targeting_data['interests'].map { |i| i['name'] }.compact
      summary << "Interests: #{interests.first(3).join(', ')}" if interests.any?
    end
    
    summary.join(' | ')
  end

  def performance_metrics
    {
      impressions: impressions,
      clicks: clicks,
      leads: leads,
      spend: spend,
      cpm: cpm,
      cpc: cpc,
      cpl: cpl,
      ctr: ctr,
      conversion_rate: calculate_conversion_rate
    }
  end

  def calculate_conversion_rate
    return 0 if clicks.zero?
    
    (leads.to_f / clicks * 100).round(4)
  end

  def audience_size_estimate
    targeting_data.dig('audience_size', 'estimate') || 'Unknown'
  end

  def sync_insights!
    Facebook::Marketing::AdSetInsights.new(self).call
  end

  def sync_ads!
    Facebook::Marketing::AdsData.new(self).call
  end

  def pause_ad_set!
    Facebook::Marketing::AdSetManagement.new(self).pause
  end

  def resume_ad_set!
    Facebook::Marketing::AdSetManagement.new(self).resume
  end

  def update_budget!(new_budget, budget_type = 'daily')
    Facebook::Marketing::AdSetManagement.new(self).update_budget(new_budget, budget_type)
  end

  def update_bid!(new_bid)
    Facebook::Marketing::AdSetManagement.new(self).update_bid(new_bid)
  end

  def days_running
    return 0 unless start_time.present?
    
    end_date = end_time.present? ? [end_time, Time.current].min : Time.current
    ((end_date - start_time) / 1.day).ceil
  end

  def average_daily_performance
    return {} if days_running.zero?
    
    {
      impressions: (impressions.to_f / days_running).round,
      clicks: (clicks.to_f / days_running).round,
      leads: (leads.to_f / days_running).round(2),
      spend: (spend / days_running).round(2)
    }
  end

  def optimization_score
    score = 0
    
    # CTR scoring (0-25 points)
    score += [ctr * 2500, 25].min
    
    # Conversion rate scoring (0-25 points)
    conv_rate = calculate_conversion_rate
    score += [conv_rate * 2.5, 25].min
    
    # Cost efficiency scoring (0-25 points)
    if cpl > 0
      # Lower CPL is better, score inversely
      score += [250 / cpl, 25].min
    end
    
    # Volume scoring (0-25 points)
    score += [leads / 4.0, 25].min
    
    score.round(2)
  end

  def performance_trend
    # This would ideally compare with historical data
    # For now, return based on current metrics
    return 'excellent' if optimization_score > 75
    return 'good' if optimization_score > 50
    return 'average' if optimization_score > 25
    'needs_improvement'
  end

  def targeting_recommendations
    recommendations = []
    
    # Based on performance metrics
    if ctr < 0.01
      recommendations << 'Consider narrowing audience targeting to improve relevance'
    end
    
    if cpl > 50
      recommendations << 'Audience may be too broad - try more specific targeting'
    end
    
    if impressions < 1000 && days_running > 3
      recommendations << 'Audience may be too narrow - consider expanding targeting'
    end
    
    recommendations
  end
end

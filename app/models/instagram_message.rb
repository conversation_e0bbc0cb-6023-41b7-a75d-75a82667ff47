# frozen_string_literal: true

class InstagramMessage < ApplicationRecord
  # Associations
  belongs_to :instagram_conversation
  has_one :instagram_account, through: :instagram_conversation

  # Validations
  validates :message_id, presence: true, uniqueness: true
  validates :sender_id, presence: true

  # Scopes
  scope :from_business, -> { where(is_from_business: true) }
  scope :from_customer, -> { where(is_from_business: false) }
  scope :with_attachments, -> { where.not(attachment_url: nil) }
  scope :with_contact_info, -> { where(contains_contact_info: true) }
  scope :recent, -> { where('sent_at > ?', 24.hours.ago) }

  # Enums
  enum message_type: {
    text: 'text',
    image: 'image',
    video: 'video',
    audio: 'audio',
    file: 'file'
  }

  # Callbacks
  after_create :extract_contact_info
  after_create :update_conversation_stats

  def has_attachment?
    attachment_url.present?
  end

  def extract_contact_information
    return {} unless text.present?
    
    info = {}
    
    # Extract email
    email_regex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
    emails = text.scan(email_regex)
    info['email'] = emails.first if emails.any?
    
    # Extract phone number
    phone_regex = /(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/
    phones = text.scan(phone_regex).flatten.reject(&:blank?)
    info['phone'] = phones.first if phones.any?
    
    # Extract website
    url_regex = /(https?:\/\/[^\s]+|www\.[^\s]+)/
    urls = text.scan(url_regex).flatten
    info['website'] = urls.first if urls.any?
    
    info
  end

  def contains_buying_intent?
    return false unless text.present?
    
    buying_keywords = [
      'buy', 'purchase', 'order', 'price', 'cost', 'quote',
      'how much', 'available', 'interested in buying'
    ]
    
    text.downcase.match?(Regexp.union(buying_keywords))
  end

  def contains_contact_request?
    return false unless text.present?
    
    contact_keywords = [
      'contact', 'call me', 'email me', 'reach out', 'get in touch',
      'phone number', 'email address', 'contact info'
    ]
    
    text.downcase.match?(Regexp.union(contact_keywords))
  end

  private

  def extract_contact_info
    return unless text.present?
    
    contact_info = extract_contact_information
    
    if contact_info.any?
      self.contains_contact_info = true
      self.extracted_data = contact_info
      save!
    end
  end

  def update_conversation_stats
    conversation = instagram_conversation
    conversation.update!(
      last_message_time: sent_at,
      message_count: conversation.instagram_messages.count,
      unread_count: is_from_business? ? 0 : conversation.unread_count + 1
    )
  end
end

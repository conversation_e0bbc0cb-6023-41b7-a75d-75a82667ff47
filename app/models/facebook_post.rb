# frozen_string_literal: true

class FacebookPost < ApplicationRecord
  # Associations
  belongs_to :page
  has_many :facebook_comments, dependent: :destroy
  has_many :facebook_leads, dependent: :destroy

  # Validations
  validates :post_id, presence: true, uniqueness: true
  validates :post_type, inclusion: { in: %w[status photo video link event] }

  # Scopes
  scope :monitored, -> { where(is_monitored: true) }
  scope :published, -> { where(is_published: true) }
  scope :recent, -> { where('published_at > ?', 30.days.ago) }
  scope :with_engagement, -> { where('likes_count + comments_count + shares_count > 0') }
  scope :high_engagement, -> { where('likes_count + comments_count + shares_count > ?', 100) }

  # Enums
  enum post_type: {
    status: 'status',
    photo: 'photo',
    video: 'video',
    link: 'link',
    event: 'event'
  }

  # Callbacks
  after_create :extract_hashtags_and_mentions
  after_update :check_for_new_comments, if: :saved_change_to_comments_count?

  def engagement_rate
    return 0 if reach.zero?
    
    total_engagement = likes_count + comments_count + shares_count + reactions_count
    (total_engagement.to_f / reach * 100).round(2)
  end

  def has_lead_potential?
    return false unless message.present?
    
    lead_keywords = [
      'contact', 'dm', 'message', 'inquiry', 'quote', 'price', 'info',
      'learn more', 'get started', 'sign up', 'register', 'book now',
      'call us', 'email us', 'visit us', 'website', 'link in bio'
    ]
    
    message.downcase.match?(Regexp.union(lead_keywords))
  end

  def extract_hashtags
    return [] unless message.present?
    
    message.scan(/#\w+/).map(&:downcase)
  end

  def extract_mentions
    return [] unless message.present?
    
    message.scan(/@\w+/).map { |mention| mention[1..-1] }
  end

  def sync_comments!
    Facebook::DataFetching::CommentData.new(self).call
  end

  def sync_insights!
    Facebook::DataFetching::PostInsights.new(self).call
  end

  def process_comments_for_leads!
    facebook_comments.where(is_processed: false).find_each do |comment|
      Facebook::LeadProcessing::CommentProcessor.perform_later(comment.id)
    end
  end

  def lead_conversion_rate
    return 0 if facebook_comments.count.zero?
    
    leads_count = facebook_leads.count
    (leads_count.to_f / facebook_comments.count * 100).round(2)
  end

  def total_engagement
    likes_count + comments_count + shares_count + reactions_count
  end

  def reactions_summary
    return {} if reactions_breakdown.blank?
    
    reactions_breakdown.transform_keys(&:humanize)
  end

  def performance_score
    # Calculate performance score based on engagement and reach
    return 0 if reach.zero?
    
    engagement_score = (total_engagement.to_f / reach * 1000).round(2)
    lead_score = facebook_leads.count * 10
    
    engagement_score + lead_score
  end

  private

  def extract_hashtags_and_mentions
    return unless message.present?
    
    hashtags = extract_hashtags
    mentions = extract_mentions
    
    # Store in insights_data for now
    self.insights_data = insights_data.merge(
      'hashtags' => hashtags,
      'mentions' => mentions
    )
    save! if changed?
  end

  def check_for_new_comments
    return unless is_monitored?
    
    # Schedule comment sync if comment count increased
    if comments_count > comments_count_was
      Facebook::DataFetching::CommentSyncJob.perform_later(id)
    end
  end
end

# frozen_string_literal: true

class FacebookComment < ApplicationRecord
  # Associations
  belongs_to :facebook_post
  has_one :facebook_lead, dependent: :destroy
  has_one :page, through: :facebook_post

  # Validations
  validates :comment_id, presence: true, uniqueness: true
  validates :message, presence: true
  validates :from_name, presence: true

  # Scopes
  scope :lead_candidates, -> { where(is_lead_candidate: true) }
  scope :unprocessed, -> { where(is_processed: false) }
  scope :high_score, -> { where('lead_score > ?', 50) }
  scope :recent, -> { where('commented_at > ?', 7.days.ago) }
  scope :replies, -> { where.not(parent_comment_id: nil) }
  scope :top_level, -> { where(parent_comment_id: nil) }

  # Callbacks
  after_create :analyze_for_lead_potential
  after_update :create_lead_if_qualified, if: :saved_change_to_is_lead_candidate?

  def is_reply?
    parent_comment_id.present?
  end

  def has_contact_info?
    extracted_contact_info.present? && 
    (extracted_contact_info['email'].present? || extracted_contact_info['phone'].present?)
  end

  def calculate_lead_score
    score = 0
    
    # Base score for having message
    score += 10 if message.present?
    
    # Contact information indicators
    score += 30 if contains_email?
    score += 25 if contains_phone?
    score += 15 if contains_website?
    
    # Intent keywords
    score += 20 if contains_buying_intent?
    score += 15 if contains_inquiry_keywords?
    score += 10 if contains_interest_keywords?
    
    # User profile indicators
    score += 15 if user_has_business_profile?
    score += 10 if user_has_complete_profile?
    
    # Engagement indicators
    score += 5 if like_count > 0
    score += 10 if comment_count > 0
    
    # Length and quality
    score += 5 if message.length > 50
    score += 10 if message.length > 100
    
    # Negative indicators
    score -= 20 if looks_like_spam?
    score -= 15 if contains_promotional_content?
    
    [score, 0].max # Ensure non-negative score
  end

  def extract_contact_information
    info = {}
    
    # Extract email
    email_regex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
    emails = message.scan(email_regex)
    info['email'] = emails.first if emails.any?
    
    # Extract phone number
    phone_regex = /(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/
    phones = message.scan(phone_regex).flatten.reject(&:blank?)
    info['phone'] = phones.first if phones.any?
    
    # Extract website/social handles
    url_regex = /(https?:\/\/[^\s]+|www\.[^\s]+)/
    urls = message.scan(url_regex).flatten
    info['website'] = urls.first if urls.any?
    
    # Extract social handles
    handle_regex = /@(\w+)/
    handles = message.scan(handle_regex).flatten
    info['social_handles'] = handles if handles.any?
    
    info
  end

  def process_for_lead!
    return if is_processed?
    
    # Calculate lead score
    self.lead_score = calculate_lead_score
    
    # Extract contact information
    self.extracted_contact_info = extract_contact_information
    
    # Determine if it's a lead candidate
    self.is_lead_candidate = lead_score >= 30 || has_contact_info?
    
    # Mark as processed
    self.is_processed = true
    
    save!
    
    # Create lead if qualified
    create_lead_if_qualified if is_lead_candidate?
  end

  def hide_comment!
    return unless can_hide?
    
    Facebook::PageManagement::HideComment.new(self).call
    update!(is_hidden: true)
  end

  def reply_to_comment!(message)
    Facebook::PageManagement::ReplyToComment.new(self, message).call
  end

  private

  def analyze_for_lead_potential
    Facebook::LeadProcessing::CommentAnalysisJob.perform_later(id)
  end

  def create_lead_if_qualified
    return unless is_lead_candidate?
    return if facebook_lead.present?
    
    Facebook::LeadProcessing::CommentToLeadJob.perform_later(id)
  end

  def contains_email?
    message.match?(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/)
  end

  def contains_phone?
    message.match?(/(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/)
  end

  def contains_website?
    message.match?(/(https?:\/\/[^\s]+|www\.[^\s]+)/)
  end

  def contains_buying_intent?
    buying_keywords = [
      'buy', 'purchase', 'order', 'price', 'cost', 'quote', 'estimate',
      'how much', 'available', 'in stock', 'delivery', 'shipping'
    ]
    message.downcase.match?(Regexp.union(buying_keywords))
  end

  def contains_inquiry_keywords?
    inquiry_keywords = [
      'interested', 'more info', 'details', 'learn more', 'tell me',
      'contact', 'reach out', 'get in touch', 'call me', 'email me'
    ]
    message.downcase.match?(Regexp.union(inquiry_keywords))
  end

  def contains_interest_keywords?
    interest_keywords = [
      'love this', 'amazing', 'perfect', 'exactly what', 'looking for',
      'need this', 'want this', 'where can i', 'how do i'
    ]
    message.downcase.match?(Regexp.union(interest_keywords))
  end

  def user_has_business_profile?
    user_profile_data.dig('category').present?
  end

  def user_has_complete_profile?
    profile = user_profile_data
    profile.present? && 
    profile['name'].present? && 
    profile['id'].present?
  end

  def looks_like_spam?
    spam_indicators = [
      message.length < 10,
      message.match?(/(.)\1{4,}/), # Repeated characters
      message.count('!') > 3,
      message.count('?') > 3,
      message.upcase == message && message.length > 20
    ]
    spam_indicators.count(true) >= 2
  end

  def contains_promotional_content?
    promo_keywords = [
      'follow me', 'check my page', 'dm for promotion', 'buy followers',
      'free followers', 'follow for follow', 'f4f', 'l4l'
    ]
    message.downcase.match?(Regexp.union(promo_keywords))
  end
end

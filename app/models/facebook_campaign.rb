# frozen_string_literal: true

class FacebookCampaign < ApplicationRecord
  # Associations
  belongs_to :user
  has_many :facebook_ad_sets, dependent: :destroy
  has_many :facebook_ads, through: :facebook_ad_sets

  # Validations
  validates :campaign_id, presence: true, uniqueness: true
  validates :name, presence: true
  validates :objective, presence: true

  # Scopes
  scope :active, -> { where(status: 'ACTIVE') }
  scope :paused, -> { where(status: 'PAUSED') }
  scope :lead_generation, -> { where(objective: 'LEAD_GENERATION') }
  scope :recent, -> { where('created_at > ?', 30.days.ago) }
  scope :with_budget, -> { where.not(daily_budget: nil).or(where.not(lifetime_budget: nil)) }

  # Enums
  enum status: {
    'ACTIVE' => 'ACTIVE',
    'PAUSED' => 'PAUSED',
    'DELETED' => 'DELETED'
  }

  enum objective: {
    'LEAD_GENERATION' => 'LEAD_GENERATION',
    'CONVERSIONS' => 'CONVERSIONS',
    'TRAFFIC' => 'TRAFFIC',
    'REACH' => 'REACH',
    'BRAND_AWARENESS' => 'BRAND_AWARENESS',
    'VIDEO_VIEWS' => 'VIDEO_VIEWS',
    'POST_ENGAGEMENT' => 'POST_ENGAGEMENT'
  }

  def is_active?
    status == 'ACTIVE' && effective_status == 'ACTIVE'
  end

  def is_running?
    is_active? && 
    (start_time.nil? || start_time <= Time.current) &&
    (stop_time.nil? || stop_time > Time.current)
  end

  def budget_type
    return 'daily' if daily_budget.present?
    return 'lifetime' if lifetime_budget.present?
    'none'
  end

  def current_budget
    daily_budget || lifetime_budget || 0
  end

  def budget_utilization
    return 0 if current_budget.zero?
    
    (spend / current_budget * 100).round(2)
  end

  def performance_score
    return 0 if impressions.zero?
    
    # Calculate performance based on CTR, CPL, and lead volume
    ctr_score = [ctr * 100, 10].min # Max 10 points for CTR
    cpl_score = cpl > 0 ? [100 / cpl, 20].min : 0 # Max 20 points for low CPL
    volume_score = [leads / 10.0, 20].min # Max 20 points for lead volume
    
    (ctr_score + cpl_score + volume_score).round(2)
  end

  def roi_estimate
    return 0 if spend.zero? || leads.zero?
    
    # Assuming average lead value (this could be configurable)
    average_lead_value = 100 # This should be configurable per user/campaign
    revenue_estimate = leads * average_lead_value
    
    ((revenue_estimate - spend) / spend * 100).round(2)
  end

  def sync_insights!
    Facebook::Marketing::CampaignInsights.new(self).call
  end

  def sync_ad_sets!
    Facebook::Marketing::AdSetsData.new(self).call
  end

  def pause_campaign!
    Facebook::Marketing::CampaignManagement.new(self).pause
  end

  def resume_campaign!
    Facebook::Marketing::CampaignManagement.new(self).resume
  end

  def update_budget!(new_budget, budget_type = 'daily')
    Facebook::Marketing::CampaignManagement.new(self).update_budget(new_budget, budget_type)
  end

  def days_running
    return 0 unless start_time.present?
    
    end_date = stop_time.present? ? [stop_time, Time.current].min : Time.current
    ((end_date - start_time) / 1.day).ceil
  end

  def average_daily_spend
    return 0 if days_running.zero?
    
    (spend / days_running).round(2)
  end

  def projected_monthly_spend
    return 0 if average_daily_spend.zero?
    
    (average_daily_spend * 30).round(2)
  end

  def lead_generation_efficiency
    return 'N/A' unless objective == 'LEAD_GENERATION'
    return 'No data' if leads.zero?
    
    case cpl
    when 0..10
      'Excellent'
    when 10..25
      'Good'
    when 25..50
      'Average'
    when 50..100
      'Below Average'
    else
      'Poor'
    end
  end

  def optimization_suggestions
    suggestions = []
    
    # CTR suggestions
    suggestions << 'Consider improving ad creative - CTR is below 1%' if ctr < 0.01
    
    # CPL suggestions
    suggestions << 'Cost per lead is high - consider refining targeting' if cpl > 50
    
    # Budget suggestions
    if budget_utilization > 90
      suggestions << 'Budget is nearly exhausted - consider increasing budget'
    elsif budget_utilization < 20 && days_running > 7
      suggestions << 'Low budget utilization - consider increasing bids or expanding targeting'
    end
    
    # Performance suggestions
    suggestions << 'Campaign is performing well - consider scaling budget' if performance_score > 40
    
    suggestions
  end
end

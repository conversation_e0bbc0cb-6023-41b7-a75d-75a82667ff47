require 'rest_client'
class Page < ApplicationRecord
  has_many :forms, dependent: :destroy
  belongs_to :user

  # Facebook API integrations
  has_many :facebook_posts, dependent: :destroy
  has_many :facebook_comments, through: :facebook_posts
  has_many :facebook_events, dependent: :destroy
  has_many :facebook_conversations, dependent: :destroy
  has_many :facebook_messages, through: :facebook_conversations
  has_many :facebook_leads, dependent: :destroy

  validates :source_id, presence: true

  def self.fetch_and_save(connected_account)
    @user_graph = Koala::Facebook::API.new(connected_account.refresh_token)
    pages = @user_graph.get_connections('me', 'accounts')
    user = connected_account.user
    ids = []
    loop do
      pages.each do |fb_page|
        next unless fb_page['access_token'].present?

        ids << fb_page["id"]
        page = user.pages.find_or_initialize_by(source_id: fb_page["id"])
        page.assign_attributes(name: fb_page["name"], access_token: fb_page["access_token"])
        page.save
        page.subscribe_to_leads
      end
      pages = pages.next_page
      break if pages.blank?
    end
    user.pages.where.not(source_id: ids).destroy_all
  end

  def subscribe_to_leads
    begin
      response = RestClient.post("https://graph.facebook.com/v19.0/#{self.source_id}/subscribed_apps?access_token=#{self.access_token}", {subscribed_fields: 'leadgen'}.to_json,  {content_type: :json})
    rescue Exception => e
      Rails.logger.info "Error while subscribing to page: #{e.to_s}"
    end
  end
end

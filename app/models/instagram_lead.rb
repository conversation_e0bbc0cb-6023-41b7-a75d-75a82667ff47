# frozen_string_literal: true

class InstagramLead < ApplicationRecord
  # Associations
  belongs_to :user
  belongs_to :instagram_account
  belongs_to :form, optional: true
  belongs_to :instagram_comment, optional: true
  belongs_to :instagram_conversation, optional: true

  # Validations
  validates :source_type, presence: true, inclusion: { in: %w[comment message mention story_reply] }
  validates :source_id, presence: true
  validates :lead_username, presence: true

  # Scopes
  scope :pending, -> { where(status: 0) }
  scope :successful, -> { where(status: 1) }
  scope :failed, -> { where(status: 2) }
  scope :qualified, -> { where(qualification_status: 'qualified') }
  scope :recent, -> { where('created_at > ?', 7.days.ago) }
  scope :high_score, -> { where('lead_score > ?', 70) }

  # Enums
  enum source_type: {
    comment: 'comment',
    message: 'message', 
    mention: 'mention',
    story_reply: 'story_reply'
  }

  enum qualification_status: {
    pending: 'pending',
    qualified: 'qualified',
    disqualified: 'disqualified',
    converted: 'converted'
  }

  enum status: {
    pending: 0,
    success: 1,
    failed: 2
  }

  # Callbacks
  after_create :process_lead_async

  def source_object
    case source_type
    when 'comment'
      instagram_comment
    when 'message'
      instagram_conversation
    else
      nil
    end
  end

  def display_source
    case source_type
    when 'comment'
      "Comment on post"
    when 'message'
      "Direct message"
    when 'mention'
      "Mention"
    when 'story_reply'
      "Story reply"
    end
  end

  def source_url
    case source_type
    when 'comment'
      instagram_comment&.instagram_post&.permalink
    when 'message'
      "https://instagram.com/direct/t/#{instagram_conversation&.conversation_id}"
    else
      nil
    end
  end

  def contact_email
    contact_info['email'] || processed_data['email']
  end

  def contact_phone
    contact_info['phone'] || processed_data['phone']
  end

  def contact_name
    processed_data['name'] || lead_username
  end

  def process_to_kylas!
    return if processed_at.present?
    
    begin
      # Prepare data for Kylas
      kylas_data = prepare_kylas_data
      
      # Create lead in Kylas
      if form&.create_lead_check
        result = Instagram::KylasIntegration::CreateLeadFromInstagram.new(self, kylas_data).call
        if result[:success]
          update!(
            kylas_lead_id: result[:id],
            status: :success,
            processed_at: Time.current
          )
        else
          update!(
            status: :failed,
            error_message: result[:error_message],
            processed_at: Time.current
          )
        end
      end
      
      # Create contact in Kylas
      if form&.create_contact_check
        result = Instagram::KylasIntegration::CreateContactFromInstagram.new(self, kylas_data).call
        if result[:success]
          update!(
            kylas_contact_id: result[:id],
            status: :success,
            processed_at: Time.current
          )
        else
          update!(
            status: :failed,
            error_message: result[:error_message],
            processed_at: Time.current
          )
        end
      end
      
    rescue => e
      update!(
        status: :failed,
        error_message: e.message,
        processed_at: Time.current
      )
      Rails.logger.error "Failed to process Instagram lead #{id}: #{e.message}"
    end
  end

  def retry_processing!
    return unless failed?
    
    update!(
      status: :pending,
      error_message: nil,
      processed_at: nil
    )
    
    process_lead_async
  end

  private

  def process_lead_async
    Instagram::LeadProcessing::LeadProcessorJob.perform_later(id)
  end

  def prepare_kylas_data
    data = {}
    
    # Basic information
    data['lastName'] = contact_name
    data['description'] = generate_description
    
    # Contact information
    if contact_email.present?
      data['emails'] = [{ type: 'OFFICE', primary: true, value: contact_email }]
    end
    
    if contact_phone.present?
      phone = Phonelib.parse(contact_phone, user.country)
      if phone.valid?
        data['phoneNumbers'] = [{
          type: 'MOBILE',
          code: phone.country,
          dialCode: phone.country_code,
          value: phone.raw_national
        }]
      end
    end
    
    # Source information
    data['source'] = form&.kylas_source_id
    data['campaign'] = form&.campaign_id
    data['ownerId'] = form&.owner_id
    data['subSource'] = "Instagram #{source_type.titleize}"
    
    # UTM parameters
    data['utmSource'] = 'instagram'
    data['utmMedium'] = 'social'
    data['utmCampaign'] = instagram_account.username
    data['utmContent'] = source_type
    
    # Custom fields
    data['customFieldValues'] = {
      'instagram_username' => lead_username,
      'instagram_source_type' => source_type,
      'instagram_lead_score' => lead_score,
      'instagram_account' => instagram_account.username
    }
    
    # Add mapped fields
    apply_field_mappings(data)
    
    data
  end

  def generate_description
    case source_type
    when 'comment'
      "Instagram lead from comment: #{raw_data['text']&.truncate(200)}"
    when 'message'
      "Instagram lead from direct message conversation"
    when 'mention'
      "Instagram lead from mention"
    when 'story_reply'
      "Instagram lead from story reply"
    end
  end

  def apply_field_mappings(data)
    mappings = instagram_account.instagram_field_mappings.where(is_active: true)
    
    mappings.each do |mapping|
      source_value = extract_field_value(mapping)
      next unless source_value.present?
      
      if mapping.is_custom_kylas_attribute?
        data['customFieldValues'] ||= {}
        data['customFieldValues'][mapping.kylas_field_name] = source_value
      else
        data[mapping.kylas_field_name] = source_value
      end
    end
  end

  def extract_field_value(mapping)
    case mapping.source_type
    when 'profile'
      extract_profile_field(mapping.instagram_field_name)
    when 'comment'
      extract_comment_field(mapping.instagram_field_name)
    when 'message'
      extract_message_field(mapping.instagram_field_name)
    end
  end

  def extract_profile_field(field_name)
    profile_data = raw_data['user_profile'] || {}
    
    case field_name
    when 'username'
      lead_username
    when 'bio'
      profile_data['bio']
    when 'follower_count'
      profile_data['follower_count']
    when 'bio_email'
      profile_data['bio']&.scan(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/)&.first
    when 'bio_phone'
      profile_data['bio']&.scan(/(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/)&.flatten&.first
    end
  end

  def extract_comment_field(field_name)
    return unless instagram_comment
    
    case field_name
    when 'comment_text'
      instagram_comment.text
    when 'comment_likes'
      instagram_comment.like_count
    end
  end

  def extract_message_field(field_name)
    return unless instagram_conversation
    
    case field_name
    when 'message_text'
      instagram_conversation.instagram_messages.customer_messages.pluck(:text).join(' ')
    when 'message_count'
      instagram_conversation.message_count
    end
  end
end

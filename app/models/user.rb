# frozen_string_literal: true

class User < ApplicationRecord
  # Encryption
  encrypts :kylas_api_key, deterministic: true
  encrypts :kylas_access_token, :kylas_refresh_token

  # Include default devise modules. Others available are:
  # :confirmable, :lockable, :timeoutable, :trackable and :omniauthable
  devise :database_authenticatable, :registerable,
    :recoverable, :rememberable, :validatable, :confirmable

  has_one :connected_account
  has_many :pages
  has_many :forms
  has_many :bulk_jobs

  # Instagram associations
  has_many :instagram_accounts, dependent: :destroy
  has_many :instagram_leads, dependent: :destroy
  has_many :instagram_field_mappings, dependent: :destroy

  validates :email, :name, presence: true
  validates :email, uniqueness: true

  before_validation :save_and_valdate_kylas_user_details

  def get_access_token
    return kylas_access_token if(kylas_access_token_expires_at.to_i > DateTime.now.to_i)
    res = Kylas::GetAccessToken.new(kylas_refresh_token).call
    return nil unless res[:success]

    Rails.logger.info "Old Access Token of #{id} - #{kylas_access_token}"
    Rails.logger.info "Old Refresh Token of #{id} - #{kylas_refresh_token}"
    self.update(kylas_access_token: res[:access_token], kylas_refresh_token: res[:refresh_token],
                         kylas_access_token_expires_at: (res[:expires_in].to_i))

    Rails.logger.info "New Access Token of #{id}  - #{reload.kylas_access_token}"
    Rails.logger.info "New Refresh Token of #{id}  - #{kylas_refresh_token}"

    res[:access_token]
  end

  private

  def save_and_valdate_kylas_user_details
    if self.kylas_refresh_token_changed?
      response = Kylas::GetUserDetails.new(token: self.kylas_access_token, user: self).call
    elsif self.kylas_api_key_changed?
      response = Kylas::GetUserDetails.new(api_key: self.kylas_api_key, user: self).call
    else
      return true
    end

    if self.kylas_user_id_was.blank? && self.kylas_tenant_id_was.blank?
      self.kylas_user_id = response[:id]
      self.kylas_tenant_id = response[:tenant_id]
    elsif self.kylas_user_id.present? && self.kylas_user_id != response[:id]
      self.errors[:base] << 'User details mismatch'
    elsif self.kylas_tenant_id.present? && self.kylas_tenant_id != response[:tenant_id]
      self.errors[:base] << 'Tenant details mismatch'
    end
  end
end

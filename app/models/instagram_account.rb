# frozen_string_literal: true

class InstagramAccount < ApplicationRecord
  # Encryption for sensitive data
  encrypts :access_token, deterministic: true
  encrypts :refresh_token

  # Associations
  belongs_to :user
  has_many :instagram_posts, dependent: :destroy
  has_many :instagram_conversations, dependent: :destroy
  has_many :instagram_leads, dependent: :destroy
  has_many :instagram_webhook_events, dependent: :destroy
  has_many :instagram_field_mappings, dependent: :destroy
  has_many :instagram_comments, through: :instagram_posts
  has_many :instagram_messages, through: :instagram_conversations

  # Validations
  validates :instagram_user_id, presence: true, uniqueness: { scope: :user_id }
  validates :username, presence: true
  validates :account_type, inclusion: { in: %w[BUSINESS CREATOR PERSONAL] }

  # Scopes
  scope :active, -> { where(is_active: true) }
  scope :business_accounts, -> { where(account_type: ['BUSINESS', 'CREATOR']) }

  # Enums
  enum account_type: {
    BUSINESS: 'BUSINESS',
    CREATOR: 'CREATOR', 
    PERSONAL: 'PERSONAL'
  }

  # Callbacks
  before_validation :set_defaults
  after_create :create_default_field_mappings

  def display_name
    "@#{username}"
  end

  def business_account?
    account_type.in?(['BUSINESS', 'CREATOR'])
  end

  def token_expired?
    token_expires_at.present? && token_expires_at < Time.current
  end

  def refresh_access_token!
    return false unless refresh_token.present?
    
    response = Instagram::Authentication::TokenRefresh.new(self).call
    if response[:success]
      update!(
        access_token: response[:access_token],
        token_expires_at: response[:expires_at]
      )
      true
    else
      false
    end
  end

  def get_valid_access_token
    return access_token unless token_expired?
    
    refresh_access_token! ? access_token : nil
  end

  def sync_account_data!
    Instagram::DataFetching::AccountData.new(self).call
  end

  def sync_recent_posts!
    Instagram::DataFetching::MediaData.new(self).call
  end

  def process_pending_leads!
    instagram_leads.where(status: 'pending').find_each do |lead|
      Instagram::LeadProcessing::LeadProcessor.perform_later(lead.id)
    end
  end

  private

  def set_defaults
    self.is_active = true if is_active.nil?
    self.follower_count ||= 0
    self.following_count ||= 0
    self.media_count ||= 0
  end

  def create_default_field_mappings
    return unless business_account?

    default_mappings = [
      {
        instagram_field_name: 'username',
        kylas_field_name: 'lastName',
        entity_type: 'lead',
        source_type: 'profile',
        is_standard: true
      },
      {
        instagram_field_name: 'bio_email',
        kylas_field_name: 'emails',
        entity_type: 'lead',
        source_type: 'profile',
        is_standard: true,
        extraction_pattern: '\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
      },
      {
        instagram_field_name: 'bio_phone',
        kylas_field_name: 'phoneNumbers',
        entity_type: 'lead',
        source_type: 'profile',
        is_standard: true,
        extraction_pattern: '(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
      },
      {
        instagram_field_name: 'comment_text',
        kylas_field_name: 'description',
        entity_type: 'lead',
        source_type: 'comment',
        is_standard: true
      },
      {
        instagram_field_name: 'message_text',
        kylas_field_name: 'description',
        entity_type: 'lead',
        source_type: 'message',
        is_standard: true
      }
    ]

    instagram_field_mappings.create!(default_mappings)
  end
end

# frozen_string_literal: true

class FacebookLead < ApplicationRecord
  # Associations
  belongs_to :user
  belongs_to :page
  belongs_to :form, optional: true
  belongs_to :facebook_post, optional: true
  belongs_to :facebook_comment, optional: true
  belongs_to :facebook_conversation, optional: true
  belongs_to :facebook_event, optional: true

  # Validations
  validates :source_type, presence: true, inclusion: { in: %w[leadgen_form post_comment page_message event_response] }
  validates :source_id, presence: true
  validates :lead_name, presence: true

  # Scopes
  scope :pending, -> { where(status: 0) }
  scope :successful, -> { where(status: 1) }
  scope :failed, -> { where(status: 2) }
  scope :qualified, -> { where(qualification_status: 'qualified') }
  scope :recent, -> { where('created_at > ?', 7.days.ago) }
  scope :high_score, -> { where('lead_score > ?', 70) }
  scope :from_forms, -> { where(source_type: 'leadgen_form') }
  scope :from_comments, -> { where(source_type: 'post_comment') }
  scope :from_messages, -> { where(source_type: 'page_message') }
  scope :from_events, -> { where(source_type: 'event_response') }

  # Enums
  enum source_type: {
    leadgen_form: 'leadgen_form',
    post_comment: 'post_comment',
    page_message: 'page_message',
    event_response: 'event_response'
  }

  enum qualification_status: {
    pending: 'pending',
    qualified: 'qualified',
    disqualified: 'disqualified',
    converted: 'converted'
  }

  enum status: {
    pending: 0,
    success: 1,
    failed: 2
  }

  # Callbacks
  after_create :process_lead_async

  def source_object
    case source_type
    when 'post_comment'
      facebook_comment
    when 'page_message'
      facebook_conversation
    when 'event_response'
      facebook_event
    when 'leadgen_form'
      form
    else
      nil
    end
  end

  def display_source
    case source_type
    when 'leadgen_form'
      "Lead Ad Form: #{form&.name}"
    when 'post_comment'
      "Comment on post"
    when 'page_message'
      "Page message"
    when 'event_response'
      "Event response"
    end
  end

  def source_url
    case source_type
    when 'post_comment'
      facebook_comment&.facebook_post&.link
    when 'page_message'
      "https://facebook.com/#{page.source_id}/inbox"
    when 'event_response'
      "https://facebook.com/events/#{facebook_event&.event_id}"
    when 'leadgen_form'
      form&.form_url
    else
      nil
    end
  end

  def contact_email
    contact_info['email'] || processed_data['email']
  end

  def contact_phone
    contact_info['phone'] || processed_data['phone']
  end

  def contact_name
    processed_data['name'] || lead_name
  end

  def process_to_kylas!
    return if processed_at.present?
    
    begin
      # Prepare data for Kylas
      kylas_data = prepare_kylas_data
      
      # Create lead in Kylas
      if form&.create_lead_check
        result = Facebook::KylasIntegration::CreateLeadFromFacebook.new(self, kylas_data).call
        if result[:success]
          update!(
            kylas_lead_id: result[:id],
            status: :success,
            processed_at: Time.current
          )
        else
          update!(
            status: :failed,
            error_message: result[:error_message],
            processed_at: Time.current
          )
        end
      end
      
      # Create contact in Kylas
      if form&.create_contact_check
        result = Facebook::KylasIntegration::CreateContactFromFacebook.new(self, kylas_data).call
        if result[:success]
          update!(
            kylas_contact_id: result[:id],
            status: :success,
            processed_at: Time.current
          )
        else
          update!(
            status: :failed,
            error_message: result[:error_message],
            processed_at: Time.current
          )
        end
      end
      
    rescue => e
      update!(
        status: :failed,
        error_message: e.message,
        processed_at: Time.current
      )
      Rails.logger.error "Failed to process Facebook lead #{id}: #{e.message}"
    end
  end

  def retry_processing!
    return unless failed?
    
    update!(
      status: :pending,
      error_message: nil,
      processed_at: nil
    )
    
    process_lead_async
  end

  private

  def process_lead_async
    Facebook::LeadProcessing::LeadProcessorJob.perform_later(id)
  end

  def prepare_kylas_data
    data = {}
    
    # Basic information
    data['lastName'] = contact_name
    data['description'] = generate_description
    
    # Contact information
    if contact_email.present?
      data['emails'] = [{ type: 'OFFICE', primary: true, value: contact_email }]
    end
    
    if contact_phone.present?
      phone = Phonelib.parse(contact_phone, user.country)
      if phone.valid?
        data['phoneNumbers'] = [{
          type: 'MOBILE',
          code: phone.country,
          dialCode: phone.country_code,
          value: phone.raw_national
        }]
      end
    end
    
    # Source information
    data['source'] = form&.kylas_source_id
    data['campaign'] = form&.campaign_id
    data['ownerId'] = form&.owner_id
    data['subSource'] = "Facebook #{source_type.humanize}"
    
    # UTM parameters
    data['utmSource'] = 'facebook'
    data['utmMedium'] = 'social'
    data['utmCampaign'] = page.name
    data['utmContent'] = source_type
    
    # Custom fields
    data['customFieldValues'] = {
      'facebook_page' => page.name,
      'facebook_source_type' => source_type,
      'facebook_lead_score' => lead_score,
      'facebook_page_id' => page.source_id
    }
    
    data
  end

  def generate_description
    case source_type
    when 'leadgen_form'
      "Facebook lead from form: #{form&.name}"
    when 'post_comment'
      "Facebook lead from comment: #{raw_data['comment_text']&.truncate(200)}"
    when 'page_message'
      "Facebook lead from page message conversation"
    when 'event_response'
      "Facebook lead from event: #{facebook_event&.name}"
    end
  end
end

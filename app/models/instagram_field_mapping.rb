# frozen_string_literal: true

class InstagramFieldMapping < ApplicationRecord
  # Associations
  belongs_to :user
  belongs_to :instagram_account

  # Validations
  validates :instagram_field_name, presence: true
  validates :kylas_field_name, presence: true
  validates :entity_type, inclusion: { in: %w[lead contact] }
  validates :source_type, inclusion: { in: %w[comment message profile] }
  validates :instagram_field_name, uniqueness: { 
    scope: [:user_id, :entity_type, :source_type],
    message: "mapping already exists for this field and source"
  }

  # Scopes
  scope :active, -> { where(is_active: true) }
  scope :for_leads, -> { where(entity_type: 'lead') }
  scope :for_contacts, -> { where(entity_type: 'contact') }
  scope :from_comments, -> { where(source_type: 'comment') }
  scope :from_messages, -> { where(source_type: 'message') }
  scope :from_profile, -> { where(source_type: 'profile') }
  scope :standard_fields, -> { where(is_standard: true) }
  scope :custom_fields, -> { where(is_custom_kylas_attribute: true) }

  # Enums
  enum entity_type: {
    lead: 'lead',
    contact: 'contact'
  }

  enum source_type: {
    comment: 'comment',
    message: 'message',
    profile: 'profile'
  }

  def display_name
    "#{instagram_field_name} → #{kylas_field_name} (#{source_type})"
  end

  def extract_value_from_data(data)
    case source_type
    when 'profile'
      extract_from_profile(data)
    when 'comment'
      extract_from_comment(data)
    when 'message'
      extract_from_message(data)
    end
  end

  def self.default_mappings_for_account(account)
    [
      {
        instagram_field_name: 'username',
        kylas_field_name: 'lastName',
        entity_type: 'lead',
        source_type: 'profile',
        is_standard: true
      },
      {
        instagram_field_name: 'bio_email',
        kylas_field_name: 'emails',
        entity_type: 'lead',
        source_type: 'profile',
        is_standard: true,
        extraction_pattern: '\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
      },
      {
        instagram_field_name: 'bio_phone',
        kylas_field_name: 'phoneNumbers',
        entity_type: 'lead',
        source_type: 'profile',
        is_standard: true,
        extraction_pattern: '(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
      },
      {
        instagram_field_name: 'comment_text',
        kylas_field_name: 'description',
        entity_type: 'lead',
        source_type: 'comment',
        is_standard: true
      },
      {
        instagram_field_name: 'message_text',
        kylas_field_name: 'description',
        entity_type: 'lead',
        source_type: 'message',
        is_standard: true
      }
    ]
  end

  private

  def extract_from_profile(data)
    profile = data['user_profile'] || data['profile'] || {}
    
    case instagram_field_name
    when 'username'
      data['username'] || profile['username']
    when 'bio'
      profile['bio']
    when 'follower_count'
      profile['follower_count']
    when 'bio_email'
      extract_with_pattern(profile['bio'], email_pattern)
    when 'bio_phone'
      extract_with_pattern(profile['bio'], phone_pattern)
    when 'website'
      profile['website']
    else
      profile[instagram_field_name]
    end
  end

  def extract_from_comment(data)
    comment = data['comment'] || data
    
    case instagram_field_name
    when 'comment_text'
      comment['text']
    when 'comment_likes'
      comment['like_count']
    when 'comment_email'
      extract_with_pattern(comment['text'], email_pattern)
    when 'comment_phone'
      extract_with_pattern(comment['text'], phone_pattern)
    else
      comment[instagram_field_name]
    end
  end

  def extract_from_message(data)
    messages = data['messages'] || [data['message']].compact
    all_text = messages.map { |m| m['text'] }.compact.join(' ')
    
    case instagram_field_name
    when 'message_text'
      all_text
    when 'message_count'
      messages.count
    when 'message_email'
      extract_with_pattern(all_text, email_pattern)
    when 'message_phone'
      extract_with_pattern(all_text, phone_pattern)
    else
      data[instagram_field_name]
    end
  end

  def extract_with_pattern(text, pattern)
    return nil unless text.present? && pattern.present?
    
    regex = Regexp.new(pattern)
    matches = text.scan(regex)
    matches.flatten.first
  end

  def email_pattern
    extraction_pattern || '\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
  end

  def phone_pattern
    extraction_pattern || '(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}'
  end
end

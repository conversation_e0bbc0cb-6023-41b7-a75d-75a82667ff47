# frozen_string_literal: true

class FacebookConversation < ApplicationRecord
  # Associations
  belongs_to :page
  has_many :facebook_messages, dependent: :destroy
  has_one :facebook_lead, dependent: :destroy

  # Validations
  validates :conversation_id, presence: true, uniqueness: true
  validates :participant_id, presence: true

  # Scopes
  scope :lead_candidates, -> { where(is_lead_candidate: true) }
  scope :unprocessed, -> { where(is_processed: false) }
  scope :active, -> { where(status: 'active') }
  scope :recent, -> { where('updated_time > ?', 7.days.ago) }
  scope :high_score, -> { where('lead_score > ?', 50) }
  scope :with_unread, -> { where('unread_count > 0') }

  # Enums
  enum status: {
    active: 'active',
    archived: 'archived',
    spam: 'spam'
  }

  # Callbacks
  after_update :analyze_for_lead_potential, if: :saved_change_to_message_count?

  def latest_message
    facebook_messages.order(created_time: :desc).first
  end

  def page_messages
    facebook_messages.where(is_from_page: true)
  end

  def customer_messages
    facebook_messages.where(is_from_page: false)
  end

  def has_contact_info?
    extracted_contact_info.present? && 
    (extracted_contact_info['email'].present? || extracted_contact_info['phone'].present?)
  end

  def response_time_minutes
    return nil unless page_messages.exists? && customer_messages.exists?
    
    last_customer_message = customer_messages.order(created_time: :desc).first
    first_page_response = page_messages.where('created_time > ?', last_customer_message.created_time).order(created_time: :asc).first
    
    return nil unless first_page_response
    
    ((first_page_response.created_time - last_customer_message.created_time) / 1.minute).round
  end

  def calculate_lead_score
    score = 0
    
    # Base score for having messages
    score += 10 if message_count > 0
    
    # Message volume indicators
    score += 5 * [message_count, 10].min # Up to 50 points for message count
    
    # Contact information
    score += 40 if has_contact_info?
    score += 20 if any_message_contains_contact_info?
    
    # Intent indicators
    score += 30 if contains_buying_intent?
    score += 20 if contains_inquiry_keywords?
    score += 15 if contains_business_keywords?
    
    # Participant profile
    score += 20 if participant_has_business_profile?
    score += 10 if participant_has_complete_profile?
    
    # Conversation quality
    score += 15 if conversation_length_indicates_interest?
    score += 10 if customer_initiated_conversation?
    score += 5 if recent_activity?
    
    # Negative indicators
    score -= 25 if looks_like_spam?
    score -= 15 if contains_promotional_requests?
    
    [score, 0].max # Ensure non-negative score
  end

  def extract_contact_information
    info = {}
    all_text = facebook_messages.pluck(:message).compact.join(' ')
    
    # Extract email
    email_regex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/
    emails = all_text.scan(email_regex)
    info['email'] = emails.first if emails.any?
    
    # Extract phone number
    phone_regex = /(\+\d{1,3}[-.\s]?)?\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/
    phones = all_text.scan(phone_regex).flatten.reject(&:blank?)
    info['phone'] = phones.first if phones.any?
    
    # Extract website
    url_regex = /(https?:\/\/[^\s]+|www\.[^\s]+)/
    urls = all_text.scan(url_regex).flatten
    info['website'] = urls.first if urls.any?
    
    # Extract company/business name from context
    business_indicators = all_text.scan(/(?:work at|company|business|from)\s+([A-Z][a-zA-Z\s&]+)/).flatten
    info['company'] = business_indicators.first if business_indicators.any?
    
    info
  end

  def process_for_lead!
    return if is_processed?
    
    # Calculate lead score
    self.lead_score = calculate_lead_score
    
    # Extract contact information
    self.extracted_contact_info = extract_contact_information
    
    # Update conversation context
    update_conversation_context
    
    # Determine if it's a lead candidate
    self.is_lead_candidate = lead_score >= 40 || has_contact_info?
    
    # Mark as processed
    self.is_processed = true
    
    save!
    
    # Create lead if qualified
    create_lead_if_qualified if is_lead_candidate?
  end

  def sync_messages!
    Facebook::DataFetching::MessageData.new(self).call
  end

  def send_message!(message_text)
    Facebook::PageManagement::SendMessage.new(self, message_text).call
  end

  def mark_as_read!
    Facebook::PageManagement::MarkAsRead.new(self).call
    update!(unread_count: 0)
  end

  private

  def analyze_for_lead_potential
    Facebook::LeadProcessing::ConversationAnalysisJob.perform_later(id)
  end

  def create_lead_if_qualified
    return unless is_lead_candidate?
    return if facebook_lead.present?
    
    Facebook::LeadProcessing::ConversationToLeadJob.perform_later(id)
  end

  def any_message_contains_contact_info?
    facebook_messages.where(contains_contact_info: true).exists?
  end

  def contains_buying_intent?
    all_text = facebook_messages.customer_messages.pluck(:message).compact.join(' ').downcase
    buying_keywords = [
      'buy', 'purchase', 'order', 'price', 'cost', 'quote', 'estimate',
      'how much', 'available', 'interested in buying', 'want to order'
    ]
    all_text.match?(Regexp.union(buying_keywords))
  end

  def contains_inquiry_keywords?
    all_text = facebook_messages.customer_messages.pluck(:message).compact.join(' ').downcase
    inquiry_keywords = [
      'more information', 'details', 'learn more', 'tell me about',
      'interested in', 'want to know', 'can you help', 'need help'
    ]
    all_text.match?(Regexp.union(inquiry_keywords))
  end

  def contains_business_keywords?
    all_text = facebook_messages.customer_messages.pluck(:message).compact.join(' ').downcase
    business_keywords = [
      'business', 'company', 'service', 'consultation', 'meeting',
      'appointment', 'schedule', 'work together', 'partnership'
    ]
    all_text.match?(Regexp.union(business_keywords))
  end

  def participant_has_business_profile?
    participant_profile_data.dig('category').present?
  end

  def participant_has_complete_profile?
    profile = participant_profile_data
    profile.present? && 
    profile['name'].present? && 
    profile['id'].present?
  end

  def conversation_length_indicates_interest?
    message_count >= 3 && customer_messages.count >= 2
  end

  def customer_initiated_conversation?
    first_message = facebook_messages.order(created_time: :asc).first
    first_message&.is_from_page == false
  end

  def recent_activity?
    updated_time && updated_time > 24.hours.ago
  end

  def looks_like_spam?
    return true if message_count == 1 && facebook_messages.first&.message&.length.to_i < 10
    
    spam_patterns = facebook_messages.customer_messages.where(
      "message ~* '(follow me|check my page|dm for promotion|free followers)'"
    )
    spam_patterns.count > 0
  end

  def contains_promotional_requests?
    promo_text = facebook_messages.customer_messages.pluck(:message).compact.join(' ').downcase
    promo_keywords = [
      'promote my', 'shoutout', 'collaboration', 'sponsor', 'partnership',
      'free product', 'exchange for post', 'influencer'
    ]
    promo_text.match?(Regexp.union(promo_keywords))
  end

  def update_conversation_context
    context = {
      total_messages: message_count,
      customer_messages: customer_messages.count,
      page_messages: page_messages.count,
      first_message_time: facebook_messages.minimum(:created_time),
      last_message_time: facebook_messages.maximum(:created_time),
      response_time_minutes: response_time_minutes,
      contains_media: facebook_messages.where.not(attachment_url: nil).exists?,
      conversation_topics: extract_conversation_topics
    }
    
    self.conversation_context = context
  end

  def extract_conversation_topics
    all_text = facebook_messages.pluck(:message).compact.join(' ').downcase
    
    # Simple keyword extraction for topics
    topics = []
    
    # Product/service related
    topics << 'product_inquiry' if all_text.match?(/product|item|service/)
    topics << 'pricing' if all_text.match?(/price|cost|how much|quote/)
    topics << 'availability' if all_text.match?(/available|in stock|when/)
    topics << 'support' if all_text.match?(/help|support|problem|issue/)
    topics << 'booking' if all_text.match?(/book|appointment|schedule|meeting/)
    
    topics
  end
end

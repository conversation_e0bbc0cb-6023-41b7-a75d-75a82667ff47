# frozen_string_literal: true

class InstagramWebhookEvent < ApplicationRecord
  # Associations
  belongs_to :instagram_account, optional: true

  # Validations
  validates :event_type, presence: true
  validates :event_data, presence: true

  # Scopes
  scope :unprocessed, -> { where(is_processed: false) }
  scope :failed, -> { where(processing_status: 'failed') }
  scope :recent, -> { where('created_at > ?', 24.hours.ago) }

  # Enums
  enum processing_status: {
    pending: 'pending',
    processing: 'processing',
    completed: 'completed',
    failed: 'failed'
  }

  # Callbacks
  after_create :process_webhook_async

  def process!
    return if is_processed?
    
    update!(processing_status: 'processing')
    
    begin
      case event_type
      when 'messages'
        process_message_event
      when 'comments'
        process_comment_event
      when 'mentions'
        process_mention_event
      when 'story_insights'
        process_story_event
      else
        Rails.logger.warn "Unknown Instagram webhook event type: #{event_type}"
      end
      
      update!(
        is_processed: true,
        processed_at: Time.current,
        processing_status: 'completed'
      )
      
    rescue => e
      self.retry_count += 1
      update!(
        processing_status: 'failed',
        error_message: e.message
      )
      
      # Retry up to 3 times
      if retry_count < 3
        Instagram::WebhookHandlers::RetryWebhookJob.perform_in(retry_count * 5.minutes, id)
      end
      
      Rails.logger.error "Failed to process Instagram webhook event #{id}: #{e.message}"
    end
  end

  def retry!
    return unless failed?
    
    update!(
      processing_status: 'pending',
      error_message: nil
    )
    
    process_webhook_async
  end

  private

  def process_webhook_async
    Instagram::WebhookHandlers::WebhookProcessorJob.perform_later(id)
  end

  def process_message_event
    Instagram::WebhookHandlers::MessageWebhook.new(self).process
  end

  def process_comment_event
    Instagram::WebhookHandlers::CommentWebhook.new(self).process
  end

  def process_mention_event
    Instagram::WebhookHandlers::MentionWebhook.new(self).process
  end

  def process_story_event
    Instagram::WebhookHandlers::StoryWebhook.new(self).process
  end
end

# frozen_string_literal: true

class InstagramPost < ApplicationRecord
  # Associations
  belongs_to :instagram_account
  has_many :instagram_comments, dependent: :destroy
  has_many :instagram_leads, through: :instagram_comments

  # Validations
  validates :media_id, presence: true, uniqueness: true
  validates :media_type, inclusion: { in: %w[IMAGE VIDEO CAROUSEL_ALBUM STORY] }

  # Scopes
  scope :monitored, -> { where(is_monitored: true) }
  scope :recent, -> { where('published_at > ?', 30.days.ago) }
  scope :with_comments, -> { where('comment_count > 0') }
  scope :high_engagement, -> { where('like_count + comment_count + share_count > ?', 100) }

  # Enums
  enum media_type: {
    IMAGE: 'IMAGE',
    VIDEO: 'VIDEO',
    CAROUSEL_ALBUM: 'CAROUSEL_ALBUM',
    STORY: 'STORY'
  }

  # Callbacks
  after_create :extract_hashtags_and_mentions
  after_update :check_for_new_comments, if: :saved_change_to_comment_count?

  def engagement_rate
    return 0 if reach.zero?
    
    total_engagement = like_count + comment_count + share_count + save_count
    (total_engagement.to_f / reach * 100).round(2)
  end

  def has_lead_potential?
    # Check if post contains lead generation indicators
    return false unless caption.present?
    
    lead_keywords = [
      'contact', 'dm', 'message', 'inquiry', 'quote', 'price', 'info',
      'learn more', 'get started', 'sign up', 'register', 'book now',
      'call us', 'email us', 'visit us', 'website', 'link in bio'
    ]
    
    caption.downcase.match?(Regexp.union(lead_keywords))
  end

  def extract_hashtags
    return [] unless caption.present?
    
    caption.scan(/#\w+/).map(&:downcase)
  end

  def extract_mentions
    return [] unless caption.present?
    
    caption.scan(/@\w+/).map { |mention| mention[1..-1] }
  end

  def sync_comments!
    Instagram::DataFetching::CommentData.new(self).call
  end

  def sync_insights!
    return unless instagram_account.business_account?
    
    Instagram::DataFetching::InsightsData.new(self).call
  end

  def process_comments_for_leads!
    instagram_comments.where(is_processed: false).find_each do |comment|
      Instagram::LeadProcessing::CommentProcessor.perform_later(comment.id)
    end
  end

  def lead_conversion_rate
    return 0 if instagram_comments.count.zero?
    
    leads_count = instagram_leads.count
    (leads_count.to_f / instagram_comments.count * 100).round(2)
  end

  private

  def extract_hashtags_and_mentions
    self.hashtags = extract_hashtags
    self.mentions = extract_mentions
    save! if changed?
  end

  def check_for_new_comments
    return unless is_monitored?
    
    # Schedule comment sync if comment count increased
    if comment_count > comment_count_was
      Instagram::DataFetching::CommentSyncJob.perform_later(id)
    end
  end
end

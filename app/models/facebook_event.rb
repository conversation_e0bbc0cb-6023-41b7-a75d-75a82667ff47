# frozen_string_literal: true

class FacebookEvent < ApplicationRecord
  # Associations
  belongs_to :page
  has_many :facebook_leads, dependent: :destroy

  # Validations
  validates :event_id, presence: true, uniqueness: true
  validates :name, presence: true
  validates :start_time, presence: true

  # Scopes
  scope :upcoming, -> { where('start_time > ?', Time.current) }
  scope :past, -> { where('start_time < ?', Time.current) }
  scope :active, -> { where(is_canceled: false) }
  scope :monitored, -> { where(is_monitored: true) }
  scope :public_events, -> { where(event_type: 'public') }
  scope :this_month, -> { where(start_time: Time.current.beginning_of_month..Time.current.end_of_month) }

  # Enums
  enum event_type: {
    private: 'private',
    public: 'public',
    group: 'group',
    community: 'community'
  }

  # Callbacks
  before_save :set_is_online_flag

  def is_upcoming?
    start_time > Time.current
  end

  def is_past?
    end_time.present? ? end_time < Time.current : start_time < Time.current
  end

  def is_happening_now?
    return false unless start_time <= Time.current
    return true unless end_time.present?
    
    end_time >= Time.current
  end

  def duration_in_hours
    return nil unless end_time.present?
    
    ((end_time - start_time) / 1.hour).round(2)
  end

  def total_responses
    attending_count + declined_count + maybe_count + interested_count
  end

  def response_rate
    return 0 if noreply_count.zero?
    
    (total_responses.to_f / (total_responses + noreply_count) * 100).round(2)
  end

  def attendance_rate
    return 0 if total_responses.zero?
    
    (attending_count.to_f / total_responses * 100).round(2)
  end

  def sync_attendees!
    Facebook::DataFetching::EventAttendees.new(self).call
  end

  def sync_insights!
    Facebook::DataFetching::EventInsights.new(self).call
  end

  def process_attendees_for_leads!
    # Process attending and interested users as potential leads
    Facebook::LeadProcessing::EventAttendeesProcessor.perform_later(id)
  end

  def formatted_start_time
    start_time.in_time_zone(timezone || 'UTC').strftime('%B %d, %Y at %I:%M %p %Z')
  end

  def formatted_end_time
    return nil unless end_time.present?
    
    end_time.in_time_zone(timezone || 'UTC').strftime('%B %d, %Y at %I:%M %p %Z')
  end

  def location_summary
    return 'Online Event' if is_online?
    return location if location.present?
    return place_data['name'] if place_data.present? && place_data['name'].present?
    
    'Location TBD'
  end

  def engagement_score
    score = 0
    
    # Base score for responses
    score += attending_count * 10
    score += interested_count * 5
    score += maybe_count * 3
    
    # Bonus for high engagement
    score += 50 if response_rate > 50
    score += 100 if attendance_rate > 70
    
    # Penalty for cancellation
    score -= 200 if is_canceled?
    
    [score, 0].max
  end

  def lead_potential_score
    score = 0
    
    # Event type scoring
    score += 30 if event_type == 'public'
    score += 20 if event_type == 'community'
    
    # Attendance scoring
    score += 20 if attending_count > 10
    score += 40 if attending_count > 50
    score += 60 if attending_count > 100
    
    # Interest scoring
    score += 15 if interested_count > 20
    score += 30 if interested_count > 50
    
    # Description analysis
    if description.present?
      business_keywords = ['business', 'networking', 'conference', 'workshop', 'seminar', 'training']
      score += 25 if description.downcase.match?(Regexp.union(business_keywords))
    end
    
    score
  end

  def should_monitor_for_leads?
    lead_potential_score >= 50 && is_upcoming? && !is_canceled?
  end

  private

  def set_is_online_flag
    self.is_online = location.blank? && place_data.blank? if is_online.nil?
  end
end

# frozen_string_literal: true

class ConnectedAccount < ApplicationRecord
  # Encryption
  encrypts :access_token, :refresh_token, :email_id, deterministic: true
  attr_accessor :encryption_skip_callback

  belongs_to :user

  before_create :fetch_long_living_access_token
  before_update :fetch_long_living_access_token, if: :access_token_changed?, unless: :encryption_skip_callback

  private

  def fetch_long_living_access_token
    self.refresh_token = Koala::Facebook::OAuth.new(
      Rails.application.credentials.facebook[:app_id],
      Rails.application.credentials.facebook[:app_secret]
    ).exchange_access_token(access_token)
  end
end

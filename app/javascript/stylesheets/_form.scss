.tab-content {
  padding : 5px 15px;
}

.create-lead-contact-row{
  padding-left: 2%;
  margin-left: 1%!important;

  .form-check-label{
    margin-top: 2px;
  }
}

.add-field-mapping{
  display: flex;
  flex-wrap: wrap;
  margin-right: -40px;
  margin-left: -10px;
}

.custom-toggle-tabs {
  list-style: none;
  padding-left: 0;
  margin: 0;
  display: flex;
  gap: 0.5rem;

  .nav-item {
    .custom-tab {
      border: 1px solid #c3d5eb;
      border-radius: 999px;
      padding: 6px 14px;
      color: #1877f2;
      background-color: #fff;
      font-size: 14px;
      font-weight: 500;
      text-align: center;
      transition: background-color 0.2s ease, color 0.2s ease;

      &:hover {
        background-color: #e7f1ff;
        text-decoration: none;
      }

      &.active {
        background-color: #e7f1ff;
        border-color: #b6d3f7;
        color: #1877f2;
      }
    }
  }
}

.btn.fetch-btn,
a.btn.fetch-btn,
button.btn.fetch-btn {
  background-color:#9b9696 !important;
  color: white !important;
  text-decoration: none !important;

  &:hover,
  &:hover:not(:disabled) {
    background-color: #0d6efd !important; 
    color: white !important;
    text-decoration: none !important;
  }

  &:disabled,
  &[disabled] {
    background-color: #9b9696 !important;
    color: white !important;
    opacity: 0.65 !important;
  }
}

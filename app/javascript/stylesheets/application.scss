@import '~bootstrap/scss/bootstrap';
@import "_authentication";
@import "_pre-login";
@import "_header";
@import "_mailer";
@import "_dashboard";
@import "_navbar";
@import "user-profile-menu";
@import "_form";
@import "bootstrap";
@import "~bootstrap/scss/bootstrap";

.cursor-pointer-imp {
  cursor: pointer !important;
}

.required:after {
  content: "*";
  color: red;
  margin-left: 2px;
}

#search-box {
  width: 350px;
}

.search-box-group{
  position: relative;
}

.search-bar-field {
  border: 1px solid #ccc;
  border-right: 0;
  border-top-left-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

.search-button {
  border: 1px solid #ccc;
  border-left: 0;
  border-top-right-radius: 0.25rem !important;
  border-bottom-right-radius: 0.25rem !important;
  height: 38px;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.search-button i {
  font-size: 0.75rem;
}

.search-box-group .search-bar-field::placeholder {
  font-size: 0.85rem;
}

.modal-open {
  padding: 0px !important;
}

#flyoutmodal .modal-dialog {
  display: flex !important;
  justify-content: flex-end !important;
  width: 100% !important;
  max-width: 100% !important;
  padding-inline: 1rem;
}

#flyoutmodal .modal-content {
  width: 621px !important;
  margin: 0 !important;
}

#flyoutmodal .modal-header {
  padding: 0.75rem;
}

#flyoutmodal .modal-body {
  height: 175px;
  padding: 0.75rem;
}

#flyoutmodal .date-input:first-child {
  margin-right: 14px;
}

.modal-backdrop {
  z-index: 1020 !important;
}

.modal {
  z-index: 1020 !important;
}

.bg-warning {
  background-color: #EE0000 !important;
}

.bulk-actions-container {
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  border: 1px solid #dee2e6;
  border-radius: 5px;
}

.bulk-info {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-left: 8px;
}

#bulk-actions-container {
  display: none !important;
}

#bulk-actions-container.show {
  display: flex !important;
  justify-content: space-between;
  align-items: center;
}

#retryconfirmation{
  z-index: 1040 !important;
}

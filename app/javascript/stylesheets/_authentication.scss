.authentication-layout {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  overflow-y: auto;
  margin-top: 7rem;

  .main-content {
    height: 100%;
  }

  .content-wrapper {
    width: 80vw;
    position: relative;

    @media(min-width: map-get($grid-breakpoints, 'xl')) {
      width: 30vw;
    }
  }

  .heading {
    margin: 2rem 0;
    font-weight: 500;
  }

  .authentication-form-wrapper {
    background-color: $white;
    padding: 3rem;
    img {
      display: none;
    }

    h1 {
      font-weight: 300;
    }

    .sign-in__mobile-help-text {
      display: none;
    }

    .bottom-links {
      display: none;
    }

    .recaptcha{
      iframe{
        height: 78px;
      }
    }
  }

  .acknowledgement {
    font-size: 0.8rem;
    color: $gray-600;
  }
}

.kylas-background {
  position: fixed;
  top: 0;
  left: 0;
  min-width: 100%;
  min-height: 100%;
  height: 100vh;
  width: 100vw;
  background-size: 100% 100%;
  background-image: url('https://app-qa.sling-dev.com/images/kylas-mountain.png');
}

.selldo-background {
  position: fixed;
  top: 0;
  left: 0;
  min-width: 100%;
  min-height: 100%;
  background-size: 100% 100%;
  background-image: url('https://selldo-marketplace.sgp1.digitaloceanspaces.com/selldo-background.jpg');
}

@media(max-width: map-get($grid-breakpoints, 'sm')) {
  .kylas-background {
    display: none;
  }

  .authentication-layout {
    padding: 2rem;

    .promotional-wrapper {
        display: none;
    }

    .content-wrapper {
      height: 100%;
      width: 100%;
    }

    .authentication-form-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0;
      width: 100%;

      img.logo {
        display: block;
        height: 2rem;
        margin-bottom: 2rem;
      }

      img.sign-in__placeholder {
        display: block;
        margin: 1rem auto;
      }

      .sign-in__mobile-help-text {
        display: block;
        width: 18.5rem;
        text-align: center;
        margin-bottom: 2.333rem;
      }

      .signup_link {
        display: flex;
        flex-direction: column;
        align-items: center;

        span {
          margin-bottom: 0.5rem;
        }
      }

      form {
        display: none;
      }

      .bottom-links {
        display: flex;
      }
    }
  }
}

.error{
  color: red
}

.form-group{
  margin-bottom: 0px;
}

$header-user-dd-bg: rgba(62, 130, 247, 0.5);
$main-nav-item-padding: 1.067rem;


header {
  z-index: 1000;
  box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.2);
}

.header {

  &-separator {
    border-right: $custom-border-width solid $gray-400;
    margin-left: 1.667rem;
    margin-right: 1.667rem;
    height: 2.5rem;
  }

  &-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    &__logo {
      height: 4rem;
      width: 13.125rem;
      display: flex;
      align-items: center;
      border-right: .1333rem solid $gray-400;

      .label-h1 {
        font-size: 1.333rem;
        margin-left: 0.5rem;
        margin-bottom: 0;
        cursor: pointer;
      }
    }

    .dropdown-menu {
      top: 3.5rem;
      left: unset;
      right: 0;
  
      &::before {
        content: "";
        position: absolute;
        bottom: 100%;
        right: 0.5rem;
        border-left: 0.5rem solid transparent; 
        border-right: 0.5rem solid transparent; 
        border-bottom: 0.5rem solid $white;
      }
    }
  }
}

.navbar {
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 0.85rem;
  border-bottom: .0667rem solid $border-color;
}

.input-group-prepend .btn, .input-group-append .btn {
  z-index: 1;
}


.searchbox-with-dd {
  max-width: 34.67rem;
}

.header-top-action-icons {
  margin: 0;
  padding: 0;

  .bg-primary-circle {
    cursor: pointer;
    font-size: .7rem;
    width: 1.6rem;
    height: 1.6rem;
    color: $white;

    &:hover {
      color: $white;
    }
  }

  .fa-calendar-alt, .fa-cog, .fa-bell, .fa-question-circle {
    font-size: 1.6rem;
    color: $input-border-color;
    cursor: pointer;

    &:hover {
      color: darken($input-border-color, 7.5%);
    }
  }

  .disabled {
    &.bg-primary-circle {
      opacity: .5;
      pointer-events: none;
    }

    .fa-calendar-alt, .fa-cog, .fa-bell, .fa-question-circle {
      color: $input-border-color;
      opacity: .5;
      pointer-events: none;

      &:hover {
        color: $input-border-color;
      }
    }

    &.bookmark-dd-wrapper {
      opacity: .5;

      .fa-star {
        pointer-events: none;
      }

      .dropdown-toggle {
        pointer-events: none;
      }
    }
  }

  .fa-calendar-alt {
    .dot-blue {
      top: -.33rem;
      right: -.53rem;
    }
  }


  .fa-bell {
    .dot-blue {
      top: -.17rem;
      right: 0.02rem;
    }
  }
}

@mixin bg-primary-circle(){
  display: flex;
  align-items: center;
  justify-content: center;
  @extend .btn-primary;
  @include border-radius(50%);
}

.header-top-right {
  display: flex;

  .dropdown-user {
    .dropdown-toggle {
      width: 1.867rem;
      height: 1.867rem;
      @include bg-primary-circle();
      background-color: $header-user-dd-bg;
      border: 0;
      color: $primary;

      &:hover {
        background-color: $primary;
        color: $white;
      }

      border: 0;
    }

    .dropdown-toggle::after {
      display:none;
    }

    .dropdown-menu {
      margin-top: .33rem;
    }
  }
}

.header-bottom {
  display: flex;
  justify-content: flex-start;
  width: 100%;
}

.header-bottom-left-link {
  display: flex;
  align-items: center;
  flex: 0 0 auto;
  font-size: 1.2rem;
  font-weight: $font-light;
  letter-spacing: 0.02133rem;
  margin-bottom: .6667rem;
}

.main-nav-wrapper {
  flex-grow: 1;
  padding-left: 1.333rem;

  ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
  }
}

.main-nav {
  display: flex;

  .pg-link {
    align-self: flex-end;
    line-height: 2;

    &.active {
      color: $primary;
    }

    &:hover {
      color: $primary;
    }
  }

  > .btn-group {
    margin-left: .333rem;
    margin-right: .333rem;
    flex-shrink: 0;

    .btn:not(.dropdown-toggle) {
      padding-left: $main-nav-item-padding;
      padding-right: .2667rem;
    }

    .dropdown-toggle {
      padding-right: $main-nav-item-padding;
      padding-left: .2667rem;
      align-self: flex-end;
      margin-bottom: .2rem;
    }

    border-bottom: .2rem solid transparent;

    &.active {
      border-bottom: .2rem solid $primary;
    }

    &:last-child {
      margin-right: 0;

      .dropdown-toggle {
        padding-right: 0;
      }
    }
  }

  .main-nav-home-link {
    padding-left: 1.067rem;
    padding-right: 1.067rem;
    border-bottom: .2rem solid transparent;
    display: block;

    &.active {
      border: 0;
      border-bottom: .2rem solid $primary;
      border-radius: 0;
    }
  }

  .dropdown-menu {
    top: 80%;
  }
}

.bookmark-dd-wrapper {
  display: flex;
  align-items: center;
  border: .1333rem solid $input-border-color;
  @include border-radius(.2667rem);

  .fa-star {
    font-size: .8rem;
    color: $input-border-color;
    cursor: pointer;
    border-right: .1333rem solid $input-border-color;

    &:hover {
      color: darken($input-border-color, 7.5%);
    }
  }

  .dropdown-toggle {
    padding-left: .125rem;
    padding-right: .125rem;
    padding-top: 0;
    padding-bottom: 0;
    color: $input-border-color;
    cursor: pointer;

    &:hover {
      color: darken($input-border-color, 7.5%);
    }
  }

  .dropdown-menu {
    top: 1.5rem;
    right: -.1333rem;
  }
}


.outline-box {
  border: .133rem solid $primary;
  @include border-radius(.133rem);
  width: 1.6rem;
  height: 1.6rem;
  display: flex;
  margin-right: .5rem;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .fa-arrow-left, .fa-arrow-right {
    font-size: .6rem;
    position: relative;
  }

  .fa-arrow-right {
    &::before {
      position: absolute;
      top: -.4667rem;
      right: -.4667rem;
    }
  }

  .fa-arrow-left {
    &::before {
      position: absolute;
      bottom: -.4667rem;
      left: -.4667rem;
    }
  }
}

.app-logo {
  height: 2rem;
  display: inline-block;
}

.label-h1 {
  font-weight: bold;
  color: #2e384d;
  letter-spacing: 0.018rem;
  line-height: 1;
}

.dropdown-toggle::after {
  display:none;
}

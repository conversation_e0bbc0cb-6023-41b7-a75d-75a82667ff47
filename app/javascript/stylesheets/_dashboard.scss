.dashboard-layout{
  width: 100%;
  display: flex;
  font-size: 0.813rem;
  font-weight: normal;
  font-family: "Rubik", sans-serif;
  &__bucket{
    height: 100%;
    overflow-y: auto;
    z-index: 1;
    overflow-x: hidden;
    width: 60%;
    display: flex;
    position: relative;
    flex: 1 1 100%;
    flex-direction: column;
    margin-left: 1rem;
    margin-right: 1rem;
    margin-top: 5rem;
    font-size: small;
    .entity-count{
      font-size: 3rem;
      margin: 0;
    }
    .entity-count-small{
      font-size: 2.5rem;
      margin: 0;
    }
    .card-deck{
      margin-right: -2rem;
      min-width: 100%;
      .card{
        cursor: pointer;
        flex: 1 0 45%;
        box-shadow: 0 15px 30px rgba($black, 0.05);
        max-width: 20%;
        margin-top: 0.66rem;
        border-radius: 1rem;
        @media (min-width: map-get($grid-breakpoints, 'xl')) {
          flex: 1 0 30%;
          max-width: 30%;
        }
        @media (min-width: 1900px) {
          min-height: 17rem;
        }
        .card-header{
          background-color: $gray-200;
          border-top-left-radius: 1rem;
          border-top-right-radius: 1rem;
          border-bottom: 0;
          font-size: medium;
          &.needs-attention{
            background-color: $scarlet-20;
            justify-content: flex-start;
            .fa-exclamation-triangle{
              color: $scarlet;
              padding-right: 0.6rem;
            }
          }
        }
      }
    }
  }
}

.main-content {
  display: flex;
  flex: 1 1 100%;
}

.min-height-0 {
  min-height: 0;
}

.main-content-wrapper {
  width: 80vw;
  padding: $default-padding;
  display: flex;
  position: relative;
  flex: 1 1 100%;
  flex-direction: column;
  background: $gray-200;
  margin-left: 0;
  transition: all 300ms cubic-bezier(0.2, 0, 0, 1) 0s;

  &.email-list-wrapper {
    flex: auto;
  }
}

.left-nav-closed {
  .main-content-wrapper {
    margin-left: 4rem;
    transition: all 300ms cubic-bezier(0.2, 0, 0, 1) 0s;
  }
}

.left-nav-open {
  .main-content-wrapper {
    margin-left: 14rem;
    transition: all 300ms cubic-bezier(0.2, 0, 0, 1) 0s;
  }
}

.api-key-form{
  label{
    margin: 2rem;
    margin-top: 1.5rem;
    margin-left: 1rem;
  }
}

.m-0-5-rem{
  margin: 0.5rem;
}

.key-submit-btn{
  margin: 1.3rem;
}

.form-group{
  margin-bottom: 0.5rem !important;
}

.filter-form{
  label{
    margin-top: 1.3rem;
    font-weight: bold;
  }
}

.form-edit{
  padding-left: 1rem;
}

.help-images{
  height: 23rem;
  margin: 2rem;
}

.header-fixed{
  position: fixed;
  width: 100%;
}

.navbar-fixed{
  position: fixed !important;
}

select.form-control,input.form-control{
  font-size: small;
  height: fit-content;
}

.form-panel-body{
  border: 1px solid #dee2e6;
  border-radius: 0.3rem;
}
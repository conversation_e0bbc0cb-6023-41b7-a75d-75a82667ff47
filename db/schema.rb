# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.0].define(version: 2025_08_18_071020) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "bulk_jobs", force: :cascade do |t|
    t.string "job_id"
    t.string "status"
    t.integer "total_count"
    t.integer "failed_count", default: 0
    t.integer "success_count", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "user_id"
    t.index ["user_id"], name: "index_bulk_jobs_on_user_id"
  end

  create_table "connected_accounts", force: :cascade do |t|
    t.boolean "is_active", default: false
    t.string "access_token"
    t.string "refresh_token"
    t.string "email_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "default_mapping"
    t.index ["user_id"], name: "index_connected_accounts_on_user_id"
  end

  create_table "failed_leads", force: :cascade do |t|
    t.string "lead_gen_id"
    t.jsonb "raw_data"
    t.bigint "form_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "error"
    t.integer "status", default: 0
    t.bigint "kylas_lead_id"
    t.bigint "kylas_contact_id"
    t.index ["form_id"], name: "index_failed_leads_on_form_id"
  end

  create_table "forms", force: :cascade do |t|
    t.string "source_id"
    t.bigint "user_id"
    t.bigint "page_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.integer "campaign_id"
    t.integer "kylas_source_id"
    t.integer "product_id"
    t.integer "owner_id"
    t.boolean "create_lead_check", default: true
    t.boolean "create_contact_check", default: false
    t.boolean "saved_by_user", default: false
    t.index ["page_id"], name: "index_forms_on_page_id"
    t.index ["user_id"], name: "index_forms_on_user_id"
  end

  create_table "mapped_fields", force: :cascade do |t|
    t.string "kylas_field_name"
    t.string "fb_field_name"
    t.boolean "is_standard", default: false
    t.bigint "form_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "is_custom_kylas_attribute", default: false
    t.string "entity_type", default: "lead"
    t.string "kylas_field_type"
    t.index ["form_id"], name: "index_mapped_fields_on_form_id"
  end

  create_table "pages", force: :cascade do |t|
    t.string "source_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "access_token"
    t.string "name"
    t.index ["user_id"], name: "index_pages_on_user_id"
  end

  create_table "users", force: :cascade do |t|
    t.string "email", default: "", null: false
    t.string "encrypted_password", default: "", null: false
    t.string "reset_password_token"
    t.datetime "reset_password_sent_at", precision: nil
    t.datetime "remember_created_at", precision: nil
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "name"
    t.string "confirmation_token"
    t.datetime "confirmed_at", precision: nil
    t.datetime "confirmation_sent_at", precision: nil
    t.string "role"
    t.string "kylas_api_key"
    t.string "kylas_access_token"
    t.string "kylas_refresh_token"
    t.datetime "kylas_access_token_expires_at", precision: nil
    t.bigint "kylas_user_id"
    t.bigint "kylas_tenant_id"
    t.string "country"
    t.bigint "kylas_webhook_id"
    t.string "fetch_forms_job_id"
    t.string "bulk_job_id"
    t.index ["confirmation_token"], name: "index_users_on_confirmation_token", unique: true
    t.index ["email"], name: "index_users_on_email", unique: true
    t.index ["reset_password_token"], name: "index_users_on_reset_password_token", unique: true
  end

  create_table "utm_fields", force: :cascade do |t|
    t.bigint "campaign_id"
    t.bigint "kylas_source_id"
    t.bigint "owner_id"
    t.string "sub_source"
    t.string "utm_source"
    t.string "utm_campaign"
    t.string "utm_medium"
    t.string "utm_content"
    t.string "utm_term"
    t.integer "entity_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "form_id"
    t.index ["form_id"], name: "index_utm_fields_on_form_id"
  end

end

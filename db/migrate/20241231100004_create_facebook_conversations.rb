class CreateFacebookConversations < ActiveRecord::Migration[7.0]
  def change
    create_table :facebook_conversations do |t|
      t.references :page, null: false, foreign_key: true
      t.string :conversation_id, null: false
      t.string :participant_id
      t.string :participant_name
      t.datetime :updated_time
      t.integer :message_count, default: 0
      t.integer :unread_count, default: 0
      t.boolean :can_reply, default: true
      t.boolean :is_subscribed, default: false
      t.boolean :is_lead_candidate, default: false
      t.boolean :is_processed, default: false
      t.integer :lead_score, default: 0
      t.string :status, default: 'active' # 'active', 'archived', 'spam'
      t.jsonb :participant_profile_data, default: {}
      t.jsonb :extracted_contact_info, default: {}
      t.jsonb :conversation_context, default: {}
      t.timestamps

      t.index :conversation_id, unique: true
      t.index :participant_id
      t.index :updated_time
      t.index :is_lead_candidate
      t.index :is_processed
      t.index :lead_score
      t.index :status
    end
  end
end

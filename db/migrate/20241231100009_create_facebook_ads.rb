class CreateFacebookAds < ActiveRecord::Migration[7.0]
  def change
    create_table :facebook_ads do |t|
      t.references :facebook_ad_set, null: false, foreign_key: true
      t.string :ad_id, null: false
      t.string :name, null: false
      t.string :status # 'ACTIVE', 'PAUSED', 'DELETED'
      t.string :effective_status
      t.datetime :created_time
      t.datetime :updated_time
      t.string :creative_id
      t.text :headline
      t.text :body
      t.text :description
      t.string :call_to_action_type
      t.string :link_url
      t.string :image_url
      t.string :video_url
      t.integer :impressions, default: 0
      t.integer :clicks, default: 0
      t.integer :leads, default: 0
      t.decimal :spend, precision: 10, scale: 2, default: 0
      t.decimal :cpm, precision: 10, scale: 2, default: 0
      t.decimal :cpc, precision: 10, scale: 2, default: 0
      t.decimal :cpl, precision: 10, scale: 2, default: 0
      t.decimal :ctr, precision: 5, scale: 4, default: 0
      t.integer :frequency, default: 0
      t.integer :reach, default: 0
      t.jsonb :creative_data, default: {}
      t.jsonb :insights_data, default: {}
      t.timestamps

      t.index :ad_id, unique: true
      t.index :status
      t.index :creative_id
      t.index :created_time
    end
  end
end

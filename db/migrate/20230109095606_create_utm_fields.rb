class CreateUtmFields < ActiveRecord::Migration[7.0]
  def change
    create_table :utm_fields do |t|
      t.bigint :campaign_id
      t.bigint :kylas_source_id
      t.bigint :owner_id
      t.string :sub_source
      t.string :utm_source
      t.string :utm_campaign
      t.string :utm_medium
      t.string :utm_content
      t.string :utm_term
      t.integer :entity_type
      t.timestamps

      t.references :form, index: true
    end
  end
end

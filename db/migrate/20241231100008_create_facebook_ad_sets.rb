class CreateFacebookAdSets < ActiveRecord::Migration[7.0]
  def change
    create_table :facebook_ad_sets do |t|
      t.references :facebook_campaign, null: false, foreign_key: true
      t.string :ad_set_id, null: false
      t.string :name, null: false
      t.string :status # 'ACTIVE', 'PAUSED', 'DELETED'
      t.string :effective_status
      t.datetime :start_time
      t.datetime :end_time
      t.decimal :daily_budget, precision: 10, scale: 2
      t.decimal :lifetime_budget, precision: 10, scale: 2
      t.string :optimization_goal # 'LEAD_GENERATION', 'LINK_CLICKS', etc.
      t.string :billing_event # 'IMPRESSIONS', 'CLICKS', 'ACTIONS'
      t.decimal :bid_amount, precision: 10, scale: 2
      t.integer :impressions, default: 0
      t.integer :clicks, default: 0
      t.integer :leads, default: 0
      t.decimal :spend, precision: 10, scale: 2, default: 0
      t.decimal :cpm, precision: 10, scale: 2, default: 0
      t.decimal :cpc, precision: 10, scale: 2, default: 0
      t.decimal :cpl, precision: 10, scale: 2, default: 0
      t.decimal :ctr, precision: 5, scale: 4, default: 0
      t.jsonb :targeting_data, default: {}
      t.jsonb :insights_data, default: {}
      t.timestamps

      t.index :ad_set_id, unique: true
      t.index :status
      t.index :optimization_goal
      t.index :start_time
    end
  end
end

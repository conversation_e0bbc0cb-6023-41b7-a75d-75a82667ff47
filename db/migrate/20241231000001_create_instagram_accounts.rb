class CreateInstagramAccounts < ActiveRecord::Migration[7.0]
  def change
    create_table :instagram_accounts do |t|
      t.references :user, null: false, foreign_key: true
      t.string :instagram_user_id, null: false
      t.string :username, null: false
      t.string :account_type # 'BUSINESS', 'CREATOR', 'PERSONAL'
      t.text :access_token
      t.text :refresh_token
      t.datetime :token_expires_at
      t.boolean :is_active, default: false
      t.string :profile_picture_url
      t.integer :follower_count, default: 0
      t.integer :following_count, default: 0
      t.integer :media_count, default: 0
      t.text :bio
      t.string :website
      t.string :email
      t.string :phone_number
      t.timestamps

      t.index [:user_id, :instagram_user_id], unique: true
      t.index :username
      t.index :is_active
    end
  end
end

class CreateInstagramWebhookEvents < ActiveRecord::Migration[7.0]
  def change
    create_table :instagram_webhook_events do |t|
      t.references :instagram_account, null: true, foreign_key: true
      t.string :event_type, null: false # 'messages', 'comments', 'mentions', 'story_insights'
      t.string :event_id
      t.string :object_id
      t.jsonb :event_data, default: {}
      t.boolean :is_processed, default: false
      t.datetime :processed_at
      t.string :processing_status, default: 'pending' # 'pending', 'processing', 'completed', 'failed'
      t.string :error_message
      t.integer :retry_count, default: 0
      t.timestamps

      t.index :event_type
      t.index :event_id
      t.index :object_id
      t.index :is_processed
      t.index :processing_status
      t.index :created_at
    end
  end
end

class CreateFacebookComments < ActiveRecord::Migration[7.0]
  def change
    create_table :facebook_comments do |t|
      t.references :facebook_post, null: false, foreign_key: true
      t.string :comment_id, null: false
      t.text :message
      t.string :from_id
      t.string :from_name
      t.datetime :commented_at
      t.integer :like_count, default: 0
      t.integer :comment_count, default: 0
      t.boolean :is_hidden, default: false
      t.boolean :can_hide, default: false
      t.boolean :can_remove, default: false
      t.boolean :can_like, default: false
      t.boolean :user_likes, default: false
      t.boolean :is_lead_candidate, default: false
      t.boolean :is_processed, default: false
      t.integer :lead_score, default: 0
      t.string :parent_comment_id
      t.jsonb :user_profile_data, default: {}
      t.jsonb :extracted_contact_info, default: {}
      t.timestamps

      t.index :comment_id, unique: true
      t.index :from_id
      t.index :commented_at
      t.index :is_lead_candidate
      t.index :is_processed
      t.index :lead_score
      t.index :parent_comment_id
    end
  end
end

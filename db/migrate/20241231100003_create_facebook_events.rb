class CreateFacebookEvents < ActiveRecord::Migration[7.0]
  def change
    create_table :facebook_events do |t|
      t.references :page, null: false, foreign_key: true
      t.string :event_id, null: false
      t.string :name, null: false
      t.text :description
      t.datetime :start_time
      t.datetime :end_time
      t.string :timezone
      t.string :location
      t.string :venue_id
      t.string :cover_photo_url
      t.string :event_type # 'private', 'public', 'group', 'community'
      t.string :category
      t.integer :attending_count, default: 0
      t.integer :declined_count, default: 0
      t.integer :maybe_count, default: 0
      t.integer :noreply_count, default: 0
      t.integer :interested_count, default: 0
      t.boolean :is_canceled, default: false
      t.boolean :is_draft, default: false
      t.boolean :is_online, default: false
      t.boolean :is_monitored, default: false
      t.string :ticket_uri
      t.jsonb :place_data, default: {}
      t.jsonb :insights_data, default: {}
      t.timestamps

      t.index :event_id, unique: true
      t.index :start_time
      t.index :event_type
      t.index :is_monitored
      t.index :is_canceled
    end
  end
end

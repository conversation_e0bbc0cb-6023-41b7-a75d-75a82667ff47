class CreateFacebookLeads < ActiveRecord::Migration[7.0]
  def change
    create_table :facebook_leads do |t|
      t.references :user, null: false, foreign_key: true
      t.references :page, null: false, foreign_key: true
      t.references :form, null: true, foreign_key: true
      t.references :facebook_post, null: true, foreign_key: true
      t.references :facebook_comment, null: true, foreign_key: true
      t.references :facebook_conversation, null: true, foreign_key: true
      t.references :facebook_event, null: true, foreign_key: true
      t.string :source_type, null: false # 'leadgen_form', 'post_comment', 'page_message', 'event_response'
      t.string :source_id, null: false
      t.string :lead_name
      t.string :lead_user_id
      t.jsonb :raw_data, default: {}
      t.jsonb :processed_data, default: {}
      t.jsonb :contact_info, default: {}
      t.integer :lead_score, default: 0
      t.string :qualification_status, default: 'pending' # 'pending', 'qualified', 'disqualified', 'converted'
      t.integer :status, default: 0 # 0: pending, 1: success, 2: failed
      t.string :error_message
      t.bigint :kylas_lead_id
      t.bigint :kylas_contact_id
      t.datetime :processed_at
      t.timestamps

      t.index [:user_id, :source_type, :source_id], unique: true
      t.index :source_type
      t.index :lead_name
      t.index :lead_score
      t.index :qualification_status
      t.index :status
      t.index :processed_at
    end
  end
end

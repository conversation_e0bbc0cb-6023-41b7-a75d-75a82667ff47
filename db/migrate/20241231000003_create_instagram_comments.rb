class CreateInstagramComments < ActiveRecord::Migration[7.0]
  def change
    create_table :instagram_comments do |t|
      t.references :instagram_post, null: false, foreign_key: true
      t.string :comment_id, null: false
      t.text :text
      t.string :username
      t.string :user_id
      t.datetime :commented_at
      t.integer :like_count, default: 0
      t.integer :reply_count, default: 0
      t.boolean :is_hidden, default: false
      t.boolean :is_lead_candidate, default: false
      t.boolean :is_processed, default: false
      t.integer :lead_score, default: 0
      t.jsonb :user_profile_data, default: {}
      t.jsonb :extracted_contact_info, default: {}
      t.string :parent_comment_id # For replies
      t.timestamps

      t.index :comment_id, unique: true
      t.index :username
      t.index :commented_at
      t.index :is_lead_candidate
      t.index :is_processed
      t.index :lead_score
      t.index :parent_comment_id
    end
  end
end

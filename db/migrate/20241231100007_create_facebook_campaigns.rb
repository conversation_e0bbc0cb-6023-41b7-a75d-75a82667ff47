class CreateFacebookCampaigns < ActiveRecord::Migration[7.0]
  def change
    create_table :facebook_campaigns do |t|
      t.references :user, null: false, foreign_key: true
      t.string :campaign_id, null: false
      t.string :name, null: false
      t.string :objective # 'LEAD_GENERATION', 'CONVERSIONS', 'TRAFFIC', etc.
      t.string :status # 'ACTIVE', 'PAUSED', 'DELETED'
      t.string :effective_status
      t.datetime :start_time
      t.datetime :stop_time
      t.decimal :daily_budget, precision: 10, scale: 2
      t.decimal :lifetime_budget, precision: 10, scale: 2
      t.string :bid_strategy
      t.integer :impressions, default: 0
      t.integer :clicks, default: 0
      t.integer :leads, default: 0
      t.decimal :spend, precision: 10, scale: 2, default: 0
      t.decimal :cpm, precision: 10, scale: 2, default: 0
      t.decimal :cpc, precision: 10, scale: 2, default: 0
      t.decimal :cpl, precision: 10, scale: 2, default: 0
      t.decimal :ctr, precision: 5, scale: 4, default: 0
      t.jsonb :insights_data, default: {}
      t.timestamps

      t.index :campaign_id, unique: true
      t.index :objective
      t.index :status
      t.index :start_time
    end
  end
end

class CreateFacebookPosts < ActiveRecord::Migration[7.0]
  def change
    create_table :facebook_posts do |t|
      t.references :page, null: false, foreign_key: true
      t.string :post_id, null: false
      t.text :message
      t.text :story
      t.string :link
      t.string :picture
      t.string :video_url
      t.string :post_type # 'status', 'photo', 'video', 'link', 'event'
      t.datetime :published_at
      t.integer :likes_count, default: 0
      t.integer :comments_count, default: 0
      t.integer :shares_count, default: 0
      t.integer :reactions_count, default: 0
      t.integer :reach, default: 0
      t.integer :impressions, default: 0
      t.boolean :is_published, default: true
      t.boolean :is_monitored, default: false
      t.jsonb :reactions_breakdown, default: {}
      t.jsonb :insights_data, default: {}
      t.jsonb :targeting_data, default: {}
      t.timestamps

      t.index :post_id, unique: true
      t.index :post_type
      t.index :published_at
      t.index :is_monitored
      t.index :is_published
    end
  end
end

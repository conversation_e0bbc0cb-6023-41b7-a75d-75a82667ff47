class CreateFacebookMessages < ActiveRecord::Migration[7.0]
  def change
    create_table :facebook_messages do |t|
      t.references :facebook_conversation, null: false, foreign_key: true
      t.string :message_id, null: false
      t.text :message
      t.string :from_id
      t.string :from_name
      t.string :to_id
      t.string :to_name
      t.datetime :created_time
      t.string :message_type, default: 'text' # 'text', 'image', 'video', 'audio', 'file', 'sticker'
      t.string :attachment_url
      t.string :attachment_type
      t.boolean :is_from_page, default: false
      t.boolean :is_echo, default: false
      t.boolean :contains_contact_info, default: false
      t.jsonb :attachments_data, default: {}
      t.jsonb :extracted_data, default: {}
      t.timestamps

      t.index :message_id, unique: true
      t.index :from_id
      t.index :created_time
      t.index :message_type
      t.index :is_from_page
      t.index :contains_contact_info
    end
  end
end

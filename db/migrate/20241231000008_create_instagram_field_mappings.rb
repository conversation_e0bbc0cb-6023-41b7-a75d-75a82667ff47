class CreateInstagramFieldMappings < ActiveRecord::Migration[7.0]
  def change
    create_table :instagram_field_mappings do |t|
      t.references :user, null: false, foreign_key: true
      t.references :instagram_account, null: false, foreign_key: true
      t.string :instagram_field_name, null: false
      t.string :kylas_field_name, null: false
      t.string :entity_type, default: 'lead' # 'lead', 'contact'
      t.string :source_type, null: false # 'comment', 'message', 'profile'
      t.boolean :is_standard, default: false
      t.boolean :is_custom_kylas_attribute, default: false
      t.string :kylas_field_type
      t.string :extraction_pattern # Regex pattern for extracting data
      t.boolean :is_active, default: true
      t.timestamps

      t.index [:user_id, :instagram_field_name, :entity_type, :source_type], 
              unique: true, 
              name: 'index_instagram_field_mappings_unique'
      t.index :kylas_field_name
      t.index :source_type
      t.index :is_active
    end
  end
end

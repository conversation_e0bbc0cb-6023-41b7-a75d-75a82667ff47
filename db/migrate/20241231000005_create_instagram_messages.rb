class CreateInstagramMessages < ActiveRecord::Migration[7.0]
  def change
    create_table :instagram_messages do |t|
      t.references :instagram_conversation, null: false, foreign_key: true
      t.string :message_id, null: false
      t.text :text
      t.string :sender_id
      t.string :sender_username
      t.datetime :sent_at
      t.string :message_type, default: 'text' # 'text', 'image', 'video', 'audio', 'file'
      t.string :attachment_url
      t.string :attachment_type
      t.boolean :is_from_business, default: false
      t.boolean :is_read, default: false
      t.boolean :contains_contact_info, default: false
      t.jsonb :extracted_data, default: {}
      t.jsonb :attachment_metadata, default: {}
      t.timestamps

      t.index :message_id, unique: true
      t.index :sender_id
      t.index :sent_at
      t.index :message_type
      t.index :is_from_business
      t.index :contains_contact_info
    end
  end
end

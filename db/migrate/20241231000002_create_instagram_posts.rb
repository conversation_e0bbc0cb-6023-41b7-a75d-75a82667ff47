class CreateInstagramPosts < ActiveRecord::Migration[7.0]
  def change
    create_table :instagram_posts do |t|
      t.references :instagram_account, null: false, foreign_key: true
      t.string :media_id, null: false
      t.string :media_type # 'IMAGE', 'VIDEO', 'CAROUSEL_ALBUM', 'STORY'
      t.text :caption
      t.string :permalink
      t.string :thumbnail_url
      t.string :media_url
      t.datetime :published_at
      t.integer :like_count, default: 0
      t.integer :comment_count, default: 0
      t.integer :share_count, default: 0
      t.integer :save_count, default: 0
      t.integer :reach, default: 0
      t.integer :impressions, default: 0
      t.boolean :is_monitored, default: false
      t.jsonb :hashtags, default: []
      t.jsonb :mentions, default: []
      t.jsonb :insights_data, default: {}
      t.timestamps

      t.index :media_id, unique: true
      t.index :media_type
      t.index :published_at
      t.index :is_monitored
      t.index :hashtags, using: :gin
      t.index :mentions, using: :gin
    end
  end
end

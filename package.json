{"name": "kylas-f<PERSON>ad", "private": true, "dependencies": {"@babel/core": "^7.17.8", "@babel/plugin-transform-runtime": "^7.17.0", "@babel/preset-env": "^7.16.11", "@rails/ujs": "^6.0.0", "babel-loader": "^8.2.4", "babel-plugin-macros": "^3.1.0", "bootstrap": "^4.6.0", "css-loader": "^6.7.1", "file-loader": "^6.2.0", "jquery": "^3.6.0", "mini-css-extract-plugin": "^2.6.0", "popper.js": "^1.16.1", "sass": "^1.49.9", "sass-loader": "^12.6.0", "turbolinks": "^5.2.0", "webpack": "^5.70.0", "webpack-cli": "^4.9.2", "webpack-remove-empty-scripts": "^0.7.3"}, "version": "0.1.0", "devDependencies": {}, "scripts": {"build": "webpack --config ./config/webpack/webpack.config.js"}, "babel": {"presets": ["@babel/preset-env"]}}
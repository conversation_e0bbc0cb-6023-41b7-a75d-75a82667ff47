# Kylas Facebook Lead Integration

A Ruby on Rails application that integrates Facebook Lead Ads with Kylas CRM system, automatically capturing leads from Facebook forms and syncing them to <PERSON><PERSON><PERSON> as leads or contacts.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Facebook Integration Setup](#facebook-integration-setup)
- [Kylas Integration Setup](#kylas-integration-setup)
- [Usage](#usage)
- [Field Mapping](#field-mapping)
- [Webhook Configuration](#webhook-configuration)
- [Background Jobs](#background-jobs)
- [Error Handling](#error-handling)
- [API Documentation](#api-documentation)
- [Troubleshooting](#troubleshooting)
- [Instagram Integration](#instagram-integration)
- [Contributing](#contributing)

## Overview

This application serves as a bridge between Facebook Lead Ads and Kylas CRM, enabling automatic lead capture and synchronization. It supports both Kylas and Sell.do environments with comprehensive field mapping, error handling, and retry mechanisms.

## Features

### Facebook Integration
- **OAuth Authentication**: Secure Facebook account connection
- **Page Management**: Access and manage multiple Facebook pages
- **Form Discovery**: Automatic detection of Facebook lead forms
- **Real-time Webhooks**: Instant lead capture via Facebook webhooks
- **Comprehensive Data Extraction**: Captures all form fields and UTM parameters

### Kylas CRM Integration
- **Dual Entity Support**: Creates both Leads and Contacts
- **Dynamic Field Mapping**: Flexible mapping between Facebook and Kylas fields
- **Custom Field Support**: Handles custom attributes in Kylas
- **Product Association**: Links leads to specific products/services
- **Owner Assignment**: Assigns leads to designated Kylas users

### Advanced Features
- **Background Processing**: Asynchronous lead processing with Sidekiq
- **Error Recovery**: Failed lead tracking and retry mechanisms
- **Bulk Operations**: Batch processing for multiple leads
- **Multi-environment Support**: Kylas and Sell.do compatibility
- **Comprehensive Logging**: Detailed logs for debugging and monitoring

## Prerequisites

- Ruby 3.0.0
- Rails 7.0+
- PostgreSQL 12+
- Redis 6+
- Facebook Developer Account
- Kylas/Sell.do Account with API access

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kylas_fblead
   ```

2. **Install dependencies**
   ```bash
   bundle install
   yarn install
   ```

3. **Setup database**
   ```bash
   rails db:create
   rails db:migrate
   rails db:seed
   ```

4. **Setup environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Database
DATABASE_URL=postgresql://username:password@localhost/kylas_fblead_development

# Redis
REDIS_URL=redis://localhost:6379/0

# Facebook App Configuration
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret

# Kylas Configuration
KYLAS_CLIENT_ID=your_kylas_client_id
KYLAS_CLIENT_SECRET=your_kylas_client_secret

# Application Settings
SECRET_KEY_BASE=your_secret_key_base
```

### Rails Credentials

Configure sensitive data using Rails credentials:

```bash
rails credentials:edit
```

Add the following structure:
```yaml
facebook:
  app_id: your_facebook_app_id
  app_secret: your_facebook_app_secret

kylas:
  client_id: your_kylas_client_id
  client_secret: your_kylas_client_secret
  marketplace_app_id: your_marketplace_app_id

sidekiq:
  username: admin
  password: your_sidekiq_password

log_dna_ingestion_key: your_logdna_key
```

## Facebook Integration Setup

### 1. Create Facebook App

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app with "Business" type
3. Add the following products:
   - **Facebook Login**: For user authentication
   - **Webhooks**: For real-time lead notifications

### 2. Configure Facebook Login

1. In Facebook Login settings, add redirect URIs:
   ```
   http://localhost:3000/auth/facebook/callback (development)
   https://yourdomain.com/auth/facebook/callback (production)
   ```

2. Set required permissions:
   - `email`
   - `pages_show_list`
   - `pages_read_engagement`
   - `leads_retrieval`

### 3. Configure Webhooks

1. In Webhooks settings, create a new webhook
2. Set callback URL: `https://yourdomain.com/webhook`
3. Set verify token: `kylassalescrm`
4. Subscribe to `leadgen` events

### 4. App Review

Submit your app for review to access live lead data:
1. Complete App Review for `leads_retrieval` permission
2. Provide detailed use case description
3. Include privacy policy and terms of service

## Kylas Integration Setup

### 1. Obtain API Credentials

1. Log into your Kylas account
2. Go to Settings > Integrations > API
3. Generate API credentials (Client ID and Secret)
4. Note your tenant ID and user ID

### 2. Configure OAuth

1. Set redirect URI in Kylas:
   ```
   https://yourdomain.com/kylas-auth
   ```

2. Configure scopes for:
   - Lead management
   - Contact management
   - Custom field access
   - Product access

## Usage

### 1. User Registration and Setup

1. **Register Account**
   ```
   POST /users/sign_up
   ```
   Users register with email and password

2. **Connect Facebook Account**
   - Navigate to dashboard
   - Click "Connect Facebook Account"
   - Authorize required permissions
   - Select Facebook pages to integrate

3. **Configure Kylas Integration**
   - Enter Kylas API credentials or
   - Complete OAuth flow for token-based access

### 2. Form Management

1. **Sync Facebook Forms**
   ```
   POST /forms/sync
   ```
   Discovers and imports Facebook lead forms

2. **Configure Forms**
   - Select forms to activate
   - Configure field mappings
   - Set UTM parameters
   - Assign products and owners

### 3. Lead Processing

Once configured, leads are automatically:
1. Captured via Facebook webhooks
2. Processed in background jobs
3. Mapped to Kylas format
4. Created in Kylas as leads/contacts
5. Logged for monitoring and debugging

## Field Mapping

### Standard Field Mappings

Default mappings are created for common fields:

| Facebook Field | Kylas Field | Type |
|---------------|-------------|------|
| `full_name` | `lastName` | Standard |
| `email` | `emails` | Standard |
| `phone_number` | `phoneNumbers` | Standard |

### Custom Field Mapping

1. **Access Form Configuration**
   ```
   GET /forms/:id/edit
   ```

2. **Create Field Mapping**
   ```
   POST /forms/:id/mapped_fields
   ```

   Parameters:
   ```json
   {
     "mapped_field": {
       "fb_field_name": "company_name",
       "kylas_field_name": "companyName",
       "entity_type": "lead",
       "is_custom_kylas_attribute": false
     }
   }
   ```

### UTM Parameter Configuration

Configure UTM tracking for leads and contacts:

```json
{
  "utm_field": {
    "campaign_id": 123,
    "kylas_source_id": 456,
    "owner_id": 789,
    "sub_source": "Facebook Ads",
    "utm_source": "facebook",
    "utm_campaign": "summer_sale",
    "utm_medium": "social",
    "utm_content": "ad_variant_a",
    "utm_term": "shoes"
  }
}
```

## Webhook Configuration

### Webhook Endpoint

The application exposes a webhook endpoint for Facebook:

```
POST /webhook
```

### Webhook Verification

Facebook webhook verification is handled automatically using the challenge token `kylassalescrm`.

### Webhook Processing

1. **Receive Webhook**
   ```ruby
   # app/controllers/facebook_controller.rb
   def webhook
     leads = lead_params[:entry]
     leads.each do |l|
       LeadGenProcessorJob.perform_later(l["changes"][0]["value"])
     end
   end
   ```

2. **Background Processing**
   ```ruby
   # app/jobs/lead_gen_processor_job.rb
   class LeadGenProcessorJob < ApplicationJob
     def perform(*args)
       ProcessLeadGen.new(args).call
     end
   end
   ```

## Background Jobs

### Sidekiq Configuration

The application uses Sidekiq for background job processing:

```ruby
# config/application.rb
config.active_job.queue_adapter = :sidekiq
```

### Job Types

1. **LeadGenProcessorJob**: Processes individual leads
2. **FetchAndSaveFormsJob**: Syncs Facebook forms
3. **BulkResyncFailedLeadJob**: Retries failed leads
4. **EntityGenerationReportJob**: Generates processing reports

### Monitoring

Access Sidekiq web interface:
```
https://yourdomain.com/sidekiq
```

Credentials are configured in Rails credentials under `sidekiq`.

## Error Handling

### Failed Lead Tracking

Failed leads are stored in the `failed_leads` table with:
- Lead generation ID
- Raw Facebook data
- Error message
- Retry status

### Retry Mechanisms

1. **Individual Retry**
   ```
   GET /failed_leads/:id/retry
   ```

2. **Bulk Retry**
   ```
   POST /failed_leads/bulk_retry
   ```

### Error Monitoring

The application integrates with Honeybadger for error tracking:

```ruby
# Gemfile
gem "honeybadger", "~> 5.0"
```

## API Documentation

### Authentication

All API endpoints require user authentication via Devise.

### Endpoints

#### Forms Management
- `GET /forms` - List forms
- `POST /forms` - Create form
- `GET /forms/:id/edit` - Edit form
- `PATCH /forms/:id` - Update form
- `DELETE /forms/:id` - Delete form
- `POST /forms/sync` - Sync Facebook forms

#### Field Mapping
- `POST /forms/:id/mapped_fields` - Create field mapping
- `DELETE /mapped_fields/:id` - Delete field mapping

#### Failed Leads
- `GET /failed_leads` - List failed leads
- `GET /failed_leads/:id/retry` - Retry single lead
- `POST /failed_leads/bulk_retry` - Bulk retry leads

## Troubleshooting

### Common Issues

1. **Facebook Permission Denied**
   - Verify app is approved for `leads_retrieval`
   - Check user has admin access to Facebook page
   - Ensure webhook is properly configured

2. **Kylas API Errors**
   - Verify API credentials are correct
   - Check token expiration and refresh
   - Validate field mappings match Kylas schema

3. **Webhook Not Receiving Data**
   - Verify webhook URL is accessible
   - Check Facebook webhook configuration
   - Ensure SSL certificate is valid

4. **Background Jobs Failing**
   - Check Redis connection
   - Verify Sidekiq is running
   - Review job logs for specific errors

### Debugging

1. **Enable Debug Logging**
   ```ruby
   # config/environments/development.rb
   config.log_level = :debug
   ```

2. **Check Application Logs**
   ```bash
   tail -f log/development.log
   ```

3. **Monitor Background Jobs**
   ```bash
   bundle exec sidekiq
   ```

### Support

For additional support:
- Check application logs
- Review Sidekiq job status
- Contact <NAME_EMAIL>

## Instagram Integration

> **Note**: Instagram integration is a separate feature that works alongside the existing Facebook Lead Ads integration without affecting the current flow.

### Overview

The Instagram integration extends the application to capture leads and interactions from Instagram through various Meta APIs:

- **Instagram Graph API**: Business account management and insights
- **Instagram Messaging API**: Handle direct messages and comments
- **Instagram Webhooks**: Real-time notifications for interactions
- **Lead Processing Engine**: AI-powered lead qualification and scoring

### Features

#### Instagram Business Integration
- **Account Connection**: OAuth flow for Instagram Business and Creator accounts
- **Profile Management**: Access to Instagram business profiles and insights
- **Media Monitoring**: Track posts, stories, and reels for engagement
- **Comment Processing**: Capture and process comments as potential leads
- **Direct Message Handling**: Convert DMs to qualified leads/contacts
- **Real-time Webhooks**: Instant notifications for new interactions

#### Lead Generation from Instagram
- **Comment-to-Lead**: Automatically convert high-intent comments into Kylas leads
- **DM-to-Lead**: Process direct message conversations as potential leads
- **Mention Tracking**: Capture when your account is mentioned in posts or stories
- **Story Interactions**: Monitor story replies and reactions for lead opportunities
- **User Engagement Analysis**: Score leads based on engagement patterns and profile data

#### Advanced Instagram Features
- **Intelligent Lead Scoring**: AI-powered scoring based on comment content, user profile, and engagement
- **Automated Lead Qualification**: Automatic qualification based on configurable criteria
- **Content Performance Tracking**: Analyze which posts generate the most qualified leads
- **Audience Insights**: Detailed analytics on follower demographics and behavior
- **Custom Field Mapping**: Flexible mapping between Instagram data and Kylas fields
- **Bulk Operations**: Process multiple leads, retry failed syncs, and bulk qualification updates

### Setup Instructions

#### 1. Instagram App Configuration

1. **Create Instagram App**
   - Go to [Facebook Developers](https://developers.facebook.com/)
   - Create new app or use existing Facebook app (recommended for unified management)
   - Add Instagram Graph API product to your app

2. **Configure Instagram Permissions**
   Required permissions for full functionality:
   - `instagram_basic` - Access basic profile information
   - `instagram_manage_comments` - Read and manage comments on posts
   - `instagram_manage_messages` - Send and receive direct messages
   - `pages_show_list` - Access connected Instagram business accounts
   - `pages_read_engagement` - Read engagement metrics and insights

3. **Set Redirect URIs**
   Add the following redirect URIs in your Facebook app settings:
   ```
   http://localhost:3000/auth/instagram_graph/callback (development)
   https://yourdomain.com/auth/instagram_graph/callback (production)
   ```

4. **Business Verification**
   - Complete Facebook Business Verification for production use
   - Submit app for review to access advanced Instagram features
   - Provide detailed use case description for lead generation

#### 2. Instagram Webhook Setup

1. **Configure Webhook Endpoint**
   Set up webhook URL in your Facebook app:
   ```
   https://yourdomain.com/instagram/webhook
   ```

2. **Webhook Verification**
   Use the verification token: `kylasinstagramcrm`

3. **Subscribe to Events**
   Enable the following webhook events:
   - `messages` - New direct messages and message interactions
   - `comments` - New comments on posts and comment interactions
   - `mentions` - When your account is mentioned in posts or stories
   - `story_insights` - Story interaction events

4. **Test Webhook**
   Use Facebook's webhook testing tool to verify your endpoint is working correctly.

#### 3. Application Configuration

Add Instagram credentials to Rails credentials:

```bash
rails credentials:edit
```

Add the following structure:
```yaml
facebook:
  app_id: your_facebook_app_id  # Same app for both Facebook and Instagram
  app_secret: your_facebook_app_secret

instagram:
  webhook_verify_token: kylasinstagramcrm

# Optional: Separate Instagram app if not using unified Facebook app
# instagram:
#   app_id: your_instagram_app_id
#   app_secret: your_instagram_app_secret
#   webhook_verify_token: kylasinstagramcrm
```

### Usage

#### 1. Connect Instagram Account

1. **Navigate to Instagram Integration**
   - Go to the main dashboard
   - Click on "Instagram Integration" in the navigation menu
   - Or visit `/instagram/accounts` directly

2. **Connect Your Account**
   - Click "Connect Instagram Account"
   - You'll be redirected to Facebook/Instagram OAuth
   - Authorize the required permissions
   - Select your Instagram Business or Creator account(s)

3. **Account Verification**
   - Verify the connected account details
   - Check that the account type is Business or Creator (required for full functionality)
   - Confirm follower count and profile information

#### 2. Configure Lead Capture Settings

1. **Post Monitoring Setup**
   - Navigate to Instagram Posts section
   - Enable monitoring for specific posts or all posts
   - Set up automatic comment monitoring for new posts

2. **Lead Qualification Criteria**
   - Configure minimum lead score threshold (default: 30)
   - Set up keyword-based qualification rules
   - Define engagement-based scoring criteria

3. **Field Mapping Configuration**
   - Go to Account Settings > Field Mappings
   - Map Instagram profile fields to Kylas lead/contact fields
   - Set up custom field mappings for specific data extraction
   - Configure extraction patterns for emails and phone numbers

#### 3. Field Mapping for Instagram

The system supports flexible field mapping between Instagram data and Kylas fields:

##### Default Mappings

| Instagram Field | Kylas Field | Source | Description |
|----------------|-------------|---------|-------------|
| `username` | `lastName` | Profile | Instagram username |
| `bio_email` | `emails` | Profile | Email extracted from bio |
| `bio_phone` | `phoneNumbers` | Profile | Phone extracted from bio |
| `comment_text` | `description` | Comment | Comment content |
| `message_text` | `description` | Message | DM conversation content |

##### Custom Field Mapping Options

**Profile Fields:**
- `username` - Instagram username
- `bio` - Profile biography/description
- `follower_count` - Number of followers
- `following_count` - Number of accounts following
- `website` - Website URL from profile
- `bio_email` - Email extracted from bio text
- `bio_phone` - Phone number extracted from bio text

**Comment Fields:**
- `comment_text` - Full comment text
- `comment_likes` - Number of likes on comment
- `comment_email` - Email extracted from comment
- `comment_phone` - Phone extracted from comment

**Message Fields:**
- `message_text` - Combined text from conversation
- `message_count` - Number of messages in conversation
- `first_message` - Text of the first customer message
- `message_email` - Email extracted from messages
- `message_phone` - Phone extracted from messages

#### 4. Lead Processing Workflow

1. **Automatic Lead Detection**
   - Comments and DMs are automatically analyzed for lead potential
   - Lead scoring is calculated based on content, user profile, and engagement
   - Qualified leads (score ≥ 30) are automatically created in the system

2. **Manual Lead Review**
   - Review detected leads in the Instagram Leads section
   - Update qualification status (Pending, Qualified, Disqualified, Converted)
   - Retry failed lead processing
   - Bulk operations for multiple leads

3. **Kylas Integration**
   - Qualified leads are automatically synced to Kylas CRM
   - Both leads and contacts can be created based on configuration
   - Custom field mappings are applied during sync
   - UTM parameters and source tracking are automatically added

### API Endpoints

#### Instagram Account Management
- `GET /instagram/accounts` - List connected Instagram accounts
- `GET /instagram/accounts/:id` - View account details and statistics
- `POST /instagram/accounts/new` - Initiate Instagram account connection
- `PATCH /instagram/accounts/:id/toggle_status` - Activate/deactivate account
- `POST /instagram/accounts/:id/sync_data` - Manually sync account data
- `DELETE /instagram/accounts/:id` - Disconnect Instagram account

#### Posts and Content Management
- `GET /instagram/posts` - List Instagram posts with filtering options
- `GET /instagram/posts/:id` - View post details and comments
- `POST /instagram/posts/:id/sync_comments` - Manually sync post comments
- `PATCH /instagram/posts/:id/toggle_monitoring` - Enable/disable lead monitoring

#### Conversations and Messages
- `GET /instagram/conversations` - List DM conversations
- `GET /instagram/conversations/:id` - View conversation details and messages
- `POST /instagram/conversations/:id/sync_messages` - Sync conversation messages
- `PATCH /instagram/conversations/:id/mark_as_lead` - Manually mark as lead candidate

#### Lead Management
- `GET /instagram/leads` - List Instagram-sourced leads with filtering
- `GET /instagram/leads/:id` - View lead details and source information
- `POST /instagram/leads/:id/retry_processing` - Retry failed lead processing
- `PATCH /instagram/leads/:id/update_qualification` - Update lead qualification status
- `POST /instagram/leads/bulk_retry` - Bulk retry failed leads
- `POST /instagram/leads/bulk_qualify` - Bulk update lead qualification

#### Field Mapping Management
- `POST /instagram/field_mappings` - Create new field mapping
- `PATCH /instagram/field_mappings/:id` - Update existing field mapping
- `DELETE /instagram/field_mappings/:id` - Delete field mapping

#### Analytics and Reporting
- `GET /instagram/analytics` - Instagram performance metrics and KPIs
- `GET /instagram/analytics/engagement` - Detailed engagement statistics
- `GET /instagram/analytics/lead_performance` - Lead generation performance metrics
- `GET /instagram/analytics/conversion_funnel` - Lead conversion funnel analysis

#### Webhook Endpoints
- `POST /instagram/webhook` - Instagram webhook handler (for Meta webhooks)
- `GET /instagram/webhook` - Webhook verification endpoint

### Lead Scoring and Qualification

#### Automatic Lead Scoring

The system uses an intelligent scoring algorithm to evaluate lead potential:

**Comment Scoring Factors:**
- Contact information present (email: +30, phone: +25)
- Buying intent keywords (+20)
- Inquiry keywords (+15)
- Interest expressions (+10)
- Comment length and quality (*****)
- User profile completeness (+10-15)
- Engagement metrics (*****)

**Message Scoring Factors:**
- Contact information in conversation (+40)
- Message volume and engagement (*****)
- Business-related keywords (+15-30)
- User profile indicators (+10-20)
- Conversation quality and length (+10-15)

**Profile Scoring Factors:**
- Business account type (+15-20)
- Complete profile with bio (+10-15)
- Follower count thresholds (*****)
- Website and contact info (+10-15)

#### Qualification Thresholds

- **Minimum Qualification Score**: 30 points
- **High-Quality Lead Score**: 70+ points
- **Automatic Qualification**: Mentions and story replies (60+ points)

### Troubleshooting Instagram Integration

#### Common Issues

1. **Instagram Permission Denied**
   - Verify Instagram Business or Creator account connection
   - Check Facebook app review status for Instagram permissions
   - Ensure account has necessary admin rights for connected pages
   - Verify app is approved for production use

2. **Webhook Not Receiving Data**
   - Verify webhook URL is publicly accessible (use ngrok for local testing)
   - Check Instagram webhook configuration in Facebook Developer Console
   - Validate webhook verification token matches `kylasinstagramcrm`
   - Ensure SSL certificate is valid for production webhooks

3. **API Rate Limiting**
   - Monitor API usage in Facebook Developer Console
   - Implement exponential backoff for failed requests
   - Use batch requests where possible to reduce API calls
   - Consider upgrading to higher rate limits if needed

4. **Lead Processing Failures**
   - Check Sidekiq job status for failed Instagram jobs
   - Verify Kylas API credentials and permissions
   - Review field mapping configurations for errors
   - Check for invalid phone number or email formats

#### Debugging Instagram Features

1. **Check Application Logs**
   ```bash
   # View Instagram-specific logs
   tail -f log/development.log | grep Instagram

   # Check for webhook processing errors
   grep "Instagram webhook" log/production.log
   ```

2. **Monitor Background Jobs**
   ```bash
   # Check Instagram job queues
   bundle exec rails console
   > Sidekiq::Queue.new('instagram').size
   > Sidekiq::Queue.new('instagram_webhooks').size

   # View failed jobs
   > Sidekiq::DeadSet.new.size
   ```

3. **Test Webhook Locally**
   ```bash
   # Use ngrok for local webhook testing
   ngrok http 3000

   # Update webhook URL in Facebook Developer Console to:
   # https://your-ngrok-url.ngrok.io/instagram/webhook
   ```

4. **Verify Instagram API Access**
   ```bash
   # Test API access in Rails console
   bundle exec rails console
   > account = InstagramAccount.first
   > account.get_valid_access_token
   > Instagram::DataFetching::AccountData.new(account).call
   ```

#### Performance Optimization

1. **Database Indexing**
   - Ensure proper indexes on frequently queried fields
   - Monitor slow query logs for Instagram-related queries

2. **Background Job Optimization**
   - Configure appropriate Sidekiq concurrency settings
   - Use separate queues for different priority levels
   - Monitor job processing times and optimize slow jobs

3. **API Efficiency**
   - Batch API requests where possible
   - Cache frequently accessed data
   - Implement intelligent retry logic with exponential backoff

### Instagram vs Facebook Integration Comparison

| Feature | Facebook Lead Ads | Instagram Integration |
|---------|------------------|----------------------|
| **Lead Source** | Structured Lead Ad Forms | Comments, DMs, Mentions, Story Interactions |
| **Data Structure** | Structured Form Fields | Unstructured Text + Profile Data |
| **Processing Method** | Direct API Integration | AI-Powered Text Analysis + Scoring |
| **Lead Volume** | High (depends on ad spend) | Variable (depends on organic engagement) |
| **Lead Quality** | High (pre-qualified via forms) | Variable (requires intelligent qualification) |
| **Setup Complexity** | Medium (requires ad campaigns) | Low (organic content monitoring) |
| **Cost** | Requires ad spend | Free (organic engagement) |
| **Real-time Processing** | Immediate via webhooks | Immediate via webhooks |
| **Field Mapping** | Direct form field mapping | Intelligent text extraction + mapping |
| **Qualification** | Pre-qualified via form design | AI-based scoring and qualification |
| **Contact Information** | Directly provided in forms | Extracted from text/profile |
| **Scalability** | High (limited by budget) | High (limited by engagement) |

### Best Practices

#### Instagram Lead Generation Strategy

1. **Content Optimization**
   - Create engaging posts that encourage comments
   - Use clear calls-to-action in captions
   - Include contact information in bio
   - Post consistently to maintain engagement

2. **Lead Qualification Setup**
   - Set appropriate lead scoring thresholds
   - Configure keyword-based qualification rules
   - Regularly review and adjust scoring criteria
   - Monitor false positive/negative rates

3. **Response Management**
   - Set up automated responses for common inquiries
   - Monitor high-scoring leads for immediate follow-up
   - Use Instagram's business tools for professional communication
   - Maintain consistent brand voice in responses

4. **Data Quality**
   - Regularly review and update field mappings
   - Validate extracted contact information
   - Clean up duplicate leads across channels
   - Maintain data hygiene in Kylas CRM

#### Integration Maintenance

1. **Regular Monitoring**
   - Check webhook delivery status weekly
   - Monitor API rate limits and usage
   - Review failed job logs and retry as needed
   - Validate lead quality and scoring accuracy

2. **Performance Optimization**
   - Optimize database queries for large datasets
   - Configure appropriate background job concurrency
   - Monitor memory usage during peak processing
   - Implement caching for frequently accessed data

3. **Security and Compliance**
   - Regularly rotate API tokens and credentials
   - Monitor for unauthorized access attempts
   - Ensure compliance with data protection regulations
   - Implement proper data retention policies

### Migration from Facebook-Only to Integrated Setup

If you're upgrading from a Facebook-only setup to include Instagram:

1. **Backup Current Configuration**
   ```bash
   # Backup database
   pg_dump your_database > backup_before_instagram.sql

   # Backup current credentials
   cp config/credentials.yml.enc config/credentials.yml.enc.backup
   ```

2. **Run Instagram Migrations**
   ```bash
   rails db:migrate
   ```

3. **Update Application Configuration**
   - Add Instagram credentials to Rails credentials
   - Update Sidekiq configuration for new queues
   - Configure Instagram webhook endpoints

4. **Test Integration**
   - Connect a test Instagram account
   - Verify webhook delivery
   - Test lead processing workflow
   - Validate Kylas integration

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/instagram-enhancement`)
3. Make your changes following the guidelines below
4. Add comprehensive tests for new functionality
5. Update documentation (README, API docs, inline comments)
6. Submit a pull request with detailed description

### Development Guidelines

#### Code Organization
- Maintain strict separation between Facebook and Instagram flows
- Use namespaced controllers and services (`Instagram::`, `Facebook::`)
- Follow existing code patterns and conventions
- Implement proper error handling and logging

#### Testing Requirements
- Add unit tests for all new models and services
- Include integration tests for API endpoints
- Test webhook processing with sample data
- Verify Kylas integration with mock responses

#### Documentation Standards
- Update README with new configuration steps
- Add inline code documentation for complex logic
- Include API endpoint documentation
- Provide troubleshooting guides for new features

#### Backward Compatibility
- Ensure existing Facebook functionality remains unchanged
- Maintain database schema compatibility
- Preserve existing API endpoints and responses
- Support graceful degradation if Instagram features are disabled

## Support and Maintenance

### Getting Help

1. **Documentation**: Check this README and inline code documentation
2. **Logs**: Review application logs for error details
3. **Sidekiq**: Monitor background job processing
4. **API Status**: Check Facebook/Instagram API status pages
5. **Support**: Contact <EMAIL> for technical assistance

### Regular Maintenance Tasks

1. **Weekly**
   - Monitor webhook delivery rates
   - Review failed job queues
   - Check API rate limit usage
   - Validate lead quality metrics

2. **Monthly**
   - Update dependencies and security patches
   - Review and optimize database performance
   - Analyze lead conversion rates
   - Update field mappings based on usage patterns

3. **Quarterly**
   - Review and update API permissions
   - Audit data retention and cleanup
   - Performance testing and optimization
   - Security review and credential rotation

## License

This project is proprietary software. All rights reserved.

---

**Last Updated**: December 2024
**Version**: 2.0.0 (with Instagram Integration)
**Supported APIs**: Facebook Graph API v18.0+, Instagram Graph API v18.0+
**Minimum Requirements**: Ruby 3.0+, Rails 7.0+, PostgreSQL 12+, Redis 6+

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      rack (~> 2.0, >= 2.2.0)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (*******)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.7.0)
      public_suffix (>= 2.0.2, < 5.0)
    bcrypt (3.1.17)
    bindex (0.8.1)
    bootsnap (1.7.3)
      msgpack (~> 1.0)
    builder (3.2.4)
    byebug (11.1.3)
    chronic (0.10.2)
    coderay (1.1.3)
    concurrent-ruby (1.1.10)
    connection_pool (2.2.5)
    crack (0.4.5)
      rexml
    crass (1.0.6)
    devise (4.8.1)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.4.4)
    digest (3.1.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    erubi (1.10.0)
    erubis (2.7.0)
    factory_bot (6.1.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.1.0)
      factory_bot (~> 6.1.0)
      railties (>= 5.0.0)
    faraday (1.4.1)
      faraday-excon (~> 1.1)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.1)
      multipart-post (>= 1.2, < 3)
      ruby2_keywords (>= 0.0.4)
    faraday-excon (1.1.0)
    faraday-net_http (1.0.1)
    faraday-net_http_persistent (1.1.0)
    ffi (1.15.0)
    globalid (1.0.0)
      activesupport (>= 5.0)
    haml (5.2.1)
      temple (>= 0.8.0)
      tilt
    haml-rails (2.0.1)
      actionpack (>= 5.1)
      activesupport (>= 5.1)
      haml (>= 4.0.6, < 6.0)
      html2haml (>= 1.0.1)
      railties (>= 5.1)
    hashdiff (1.0.1)
    hashie (4.1.0)
    honeybadger (5.0.2)
    html2haml (2.2.0)
      erubis (~> 2.7.0)
      haml (>= 4.0, < 6)
      nokogiri (>= 1.6.0)
      ruby_parser (~> 3.5)
    http-accept (1.7.0)
    http-cookie (1.0.3)
      domain_name (~> 0.5)
    i18n (1.10.0)
      concurrent-ruby (~> 1.0)
    io-wait (0.1.0)
    jbuilder (2.11.2)
      activesupport (>= 5.0.0)
    jsbundling-rails (1.0.2)
      railties (>= 6.0.0)
    json (2.5.1)
    jwt (2.2.3)
    kaminari (1.2.1)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.1)
      kaminari-activerecord (= 1.2.1)
      kaminari-core (= 1.2.1)
    kaminari-actionview (1.2.1)
      actionview
      kaminari-core (= 1.2.1)
    kaminari-activerecord (1.2.1)
      activerecord
      kaminari-core (= 1.2.1)
    kaminari-core (1.2.1)
    koala (3.0.0)
      addressable
      faraday
      json (>= 1.8)
    listen (3.5.1)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logdna (1.5.0)
      concurrent-ruby (~> 1.0)
      json (~> 2.0)
      require_all (~> 1.4)
    lograge (0.11.2)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.15.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    marcel (1.0.2)
    method_source (1.0.0)
    mime-types (3.3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2021.0225)
    mina (1.2.3)
      open4 (~> 1.3.4)
      rake
    mini_mime (1.1.2)
    minitest (5.15.0)
    msgpack (1.4.2)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.1.1)
    net-imap (0.2.3)
      digest
      net-protocol
      strscan
    net-pop (0.1.1)
      digest
      net-protocol
      timeout
    net-protocol (0.1.2)
      io-wait
      timeout
    net-smtp (0.3.1)
      digest
      net-protocol
      timeout
    netrc (0.11.0)
    nio4r (2.5.8)
    nokogiri (1.13.3-x86_64-linux)
      racc (~> 1.4)
    oauth2 (1.4.7)
      faraday (>= 0.8, < 2.0)
      jwt (>= 1.0, < 3.0)
      multi_json (~> 1.3)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 3)
    omniauth (2.0.4)
      hashie (>= 3.4.6)
      rack (>= 1.6.2, < 3)
      rack-protection
    omniauth-facebook (8.0.0)
      omniauth-oauth2 (~> 1.2)
    omniauth-oauth2 (1.7.1)
      oauth2 (~> 1.4)
      omniauth (>= 1.9, < 3)
    open4 (1.3.4)
    orm_adapter (0.5.0)
    pg (1.2.3)
    phonelib (0.6.50)
    pry (0.14.1)
      coderay (~> 1.1)
      method_source (~> 1.0)
    public_suffix (4.0.6)
    puma (5.2.2)
      nio4r (~> 2.0)
    racc (1.6.0)
    rack (2.2.3)
    rack-mini-profiler (2.3.1)
      rack (>= 1.2.0)
    rack-protection (2.1.0)
      rack
    rack-test (1.1.0)
      rack (>= 1.0, < 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.4.2)
      loofah (~> 2.3)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.0.6)
    rb-fsevent (0.10.4)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redis (4.2.5)
    redis-namespace (1.8.1)
      redis (>= 3.0.4)
    request_store (1.5.1)
      rack (>= 1.4)
    require_all (1.5.0)
    responders (3.0.1)
      actionpack (>= 5.0)
      railties (>= 5.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.2.5)
    rspec-collection_matchers (1.2.0)
      rspec-expectations (>= 2.99.0.beta1)
    rspec-core (3.10.1)
      rspec-support (~> 3.10.0)
    rspec-expectations (3.10.1)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.10.0)
    rspec-mocks (3.10.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.10.0)
    rspec-rails (4.0.2)
      actionpack (>= 4.2)
      activesupport (>= 4.2)
      railties (>= 4.2)
      rspec-core (~> 3.10)
      rspec-expectations (~> 3.10)
      rspec-mocks (~> 3.10)
      rspec-support (~> 3.10)
    rspec-support (3.10.2)
    ruby2_keywords (0.0.4)
    ruby_parser (3.15.1)
      sexp_processor (~> 4.9)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    sexp_processor (4.15.2)
    shoulda-matchers (4.5.1)
      activesupport (>= 4.2.0)
    sidekiq (6.2.1)
      connection_pool (>= 2.2.2)
      rack (~> 2.0)
      redis (>= 4.2.0)
    silencer (1.0.1)
    spring (2.1.1)
    sprockets (4.0.3)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    strscan (3.0.0)
    temple (0.8.2)
    thor (1.2.1)
    tilt (2.0.10)
    timeout (0.2.0)
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    tzinfo (2.0.4)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2025.2)
      tzinfo (>= 1.0.0)
    unf (0.1.4)
      unf_ext
    unf_ext (*******)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.1.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.12.2)
      addressable (>= 2.3.6)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    zeitwerk (2.5.4)

PLATFORMS
  x86_64-linux

DEPENDENCIES
  bootsnap (>= 1.4.4)
  byebug
  devise (~> 4.8, >= 4.8.1)
  factory_bot_rails
  haml-rails (~> 2.0)
  honeybadger (~> 5.0)
  io-wait (= 0.1.0)
  jbuilder (~> 2.7)
  jsbundling-rails
  kaminari
  koala
  listen (~> 3.3)
  logdna (~> 1.5)
  lograge (~> 0.11.2)
  mina
  omniauth-facebook
  pg (~> 1.1)
  phonelib
  pry
  puma (~> 5.0)
  rack-mini-profiler (~> 2.0)
  rails (~> 7.0, >= *******)
  redis-namespace
  rest-client
  rspec-collection_matchers
  rspec-mocks
  rspec-rails (~> 4.0.0)
  sass-rails (>= 6)
  shoulda-matchers
  sidekiq
  silencer (~> 1.0, >= 1.0.1)
  spring
  strscan (= 3.0.0)
  turbolinks (~> 5)
  tzinfo-data
  web-console (>= 4.1.0)
  webmock
  whenever

RUBY VERSION
   ruby 3.0.0p0

BUNDLED WITH
   2.3.4

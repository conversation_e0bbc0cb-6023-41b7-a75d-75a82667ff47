require 'rails_helper'

RSpec.describe "BulkJob", type: :request do
  let(:user) { create(:user) }
  let(:other_user) { create(:user) }
  let!(:bulk_job1) { create(:bulk_job, user_id: user.id, status: COMPLETED) }
  let!(:bulk_job2) { create(:bulk_job, user_id: user.id, status: IN_PROGRESS) }
  let!(:bulk_job3) { create(:bulk_job, user_id: user.id, status: FAILED) }
  let!(:other_user_bulk_job) { create(:bulk_job, user_id: other_user.id) }

  before do
    sign_in(user)
  end

  describe "GET /index" do
    it "returns http success" do
      get "/resync-jobs"
      expect(response).to have_http_status(:success)
    end

    it "should show all bulk jobs for the current user" do
      get "/resync-jobs"
      expect(response.body).to include(bulk_job1.id.to_s)
      expect(response.body).to include(bulk_job2.id.to_s)
      expect(response.body).to include(bulk_job3.id.to_s)
    end

    it "should not show bulk jobs for other users" do
      get "/resync-jobs"
      expect(response.body).not_to include(other_user_bulk_job.id.to_s)
    end

    it "should display correct status labels" do
      get "/resync-jobs"
      expect(response.body).to include('Completed')
      expect(response.body).to include('In progress')
      expect(response.body).to include('Failed')
    end
  end
end

require 'rails_helper'

RSpec.describe 'FailedLeadsController#bulk_retry', type: :request do
  let(:user) { create(:user) }
  let(:page) { create(:page, user: user) }
  let(:form) { create(:form, user: user, page: page) }
  let(:failed_lead1) { create(:failed_lead, form: form, status: 'fail') }
  let(:failed_lead2) { create(:failed_lead, form: form, status: 'fail') }
  let(:failed_lead3) { create(:failed_lead, form: form, status: 'fail') }
  let(:successful_lead) { create(:failed_lead, form: form, status: 'success') }

  before do
    sign_in user
    failed_lead1
    failed_lead2
    failed_lead3
    successful_lead
  end

  describe 'POST /failed_leads/bulk_retry' do
    context 'when no log_ids are provided (blank)' do
      it 'redirects back with danger flash message' do
        post bulk_retry_failed_leads_path
        
        expect(response).to redirect_to(failed_leads_path)
        expect(flash[:danger]).to eq(I18n.t('failed_leads.no_selected_logs'))
      end
    end

    context 'when log_ids is provided as ALL' do
      it 'processes all failed leads and creates bulk job' do
        post bulk_retry_failed_leads_path, params: { log_ids: 'ALL' }
        
        expect(response).to redirect_to(failed_leads_path)
        expect(flash[:success]).to eq(I18n.t('failed_leads.bulk_resync_success', retry_count: 3))
      end

      it 'enqueues BulkResyncFailedLeadJob with correct parameters' do
        allow(BulkResyncFailedLeadJob).to receive(:perform_later).and_return(double(job_id: 'test-job-id'))
        
        post bulk_retry_failed_leads_path, params: { log_ids: 'ALL' }
        
        expect(BulkResyncFailedLeadJob).to have_received(:perform_later) do |log_ids, user_id, bulk_job_id|
          expect(log_ids.sort).to eq([failed_lead1.id, failed_lead2.id, failed_lead3.id].sort)
          expect(user_id).to eq(user.id)
        end
      end
    end

    context 'when specific log_ids are provided' do
      it 'processes only selected failed leads' do
        post bulk_retry_failed_leads_path, params: { log_ids: "#{failed_lead1.id},#{failed_lead3.id}" }
        
        expect(response).to redirect_to(failed_leads_path)
        expect(flash[:success]).to eq(I18n.t('failed_leads.bulk_resync_success', retry_count: 2))
      end

      it 'handles comma-separated string of IDs correctly' do
        post bulk_retry_failed_leads_path, params: { log_ids: "#{failed_lead1.id},#{failed_lead2.id},#{failed_lead3.id}" }
        
        expect(flash[:success]).to eq(I18n.t('failed_leads.bulk_resync_success', retry_count: 3))
      end

      it 'handles single ID correctly' do
        post bulk_retry_failed_leads_path, params: { log_ids: failed_lead1.id.to_s }
        
        expect(flash[:success]).to eq(I18n.t('failed_leads.bulk_resync_success', retry_count: 1))
      end
    end

    context 'when provided IDs have no failed leads' do
      it 'redirects back with danger flash message' do
        non_existent_ids = "99999,88888"
        
        post bulk_retry_failed_leads_path, params: { log_ids: non_existent_ids }
        
        expect(response).to redirect_to(failed_leads_path)
        expect(flash[:danger]).to eq(I18n.t('failed_leads.no_failed_logs'))
      end
    end

    context 'when provided IDs include non-failed leads' do
      it 'only processes failed leads' do
        post bulk_retry_failed_leads_path, params: { log_ids: "#{failed_lead1.id},#{successful_lead.id}" }
        
        expect(flash[:success]).to eq(I18n.t('failed_leads.bulk_resync_success', retry_count: 1))
      end
    end

    context 'when log_ids is empty string' do
      it 'treats as blank and shows error' do
        post bulk_retry_failed_leads_path, params: { log_ids: '' }
        
        expect(flash[:danger]).to eq(I18n.t('failed_leads.no_selected_logs'))
      end
    end

    context 'when log_ids contains invalid format' do
      it 'handles gracefully with empty result' do
        post bulk_retry_failed_leads_path, params: { log_ids: 'invalid,format' }
        
        expect(flash[:danger]).to eq(I18n.t('failed_leads.no_failed_logs'))
      end
    end

    context 'with filtering parameters' do
      it 'respects existing filters when processing ALL' do
        create(:failed_lead, form: form, created_at: 1.month.ago)
        
        failed_lead1.update!(created_at: 1.day.ago)
        failed_lead2.update!(created_at: 2.days.ago)
        failed_lead3.update!(created_at: 3.days.ago)
        
          post bulk_retry_failed_leads_path, params: { 
            log_ids: 'ALL',
            start_date: 1.week.ago.to_date,
            end_date: Date.today
          }
        
        # Should only process recent failed leads (3, not 4 including the old one)
        expect(flash[:success]).to eq(I18n.t('failed_leads.bulk_resync_success', retry_count: 3))
      end
    end
  end
end

def stub_webhook_create_request(user:, status_code:, request_body:, response_body:)
  stub_request(:post, "https://api-qa.sling-dev.com/v1/webhooks")
    .with(
      body: request_body.to_json,
      headers: {
        'Content-Type' => 'application/json',
        'User-Agent' => "marketplaceAppId: 070b7d1f-444b-4760-83fc-9900580b652f, tenantId: , userId: #{user.kylas_user_id}, apiKey: true"
      }
    )
    .to_return(status: status_code, body: response_body.to_json, headers: {})
end

def stub_fetch_tenant_details_request(user: ,status_code: 200, response_body:)
  headers = {
    'Content-Type' => 'application/json',
  }

  if user.kylas_api_key
    headers['Api-Key'] = user.kylas_api_key
  else
    headers['Authorization'] = "Bearer #{user.kylas_access_token}"
  end

  stub_request(:get, "https://api-qa.sling-dev.com/v1/tenants")
    .with(headers: headers)
    .to_return(status: status_code, body: response_body, headers: {})
end

FactoryBot.define do
  factory :user, aliases: [:owner] do
    before(:create) {|user| user.skip_confirmation! }
    name { 'Test' }
    sequence(:email){ |n| "test-#{n.to_s.rjust(3, '0')}@sample.com"}
    password { '123456' }
    kylas_user_id { SecureRandom.random_number.to_s[2..6] }
    country { "IN" }
    trait :kylas_tokens do
      kylas_access_token { SecureRandom.uuid }
      kylas_refresh_token { SecureRandom.uuid }
      kylas_access_token_expires_at { 1.hour.from_now }
    end
  end
end

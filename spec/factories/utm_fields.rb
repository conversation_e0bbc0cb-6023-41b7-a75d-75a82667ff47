# frozen_string_literal: true

FactoryBot.define do
  factory :utm_field do
    campaign_id { SecureRandom.random_number.to_s[2..11] }
    kylas_source_id { SecureRandom.random_number.to_s[2..11] }
    owner_id { SecureRandom.random_number.to_s[2..11] }
    sub_source { 'sub source' }
    utm_source { 'utm source' }
    utm_campaign { 'utm campaign' }
    utm_medium { 'utm medium' }
    utm_content { 'utm content' }
    utm_term { 'utm term' }
    entity_type { %w[lead contact].sample }
  end
end

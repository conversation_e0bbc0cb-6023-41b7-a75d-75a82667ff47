require 'rails_helper'

RSpec.describe FormsController, type: :controller do
  before do
    @request.env['devise.mapping'] = Devise.mappings[:user]
    @user = FactoryBot.create(:user)
    sign_in @user
    UsersController.any_instance.stub(:current_user) { @user }
    Koala::Facebook::OAuth.any_instance.stub(:exchange_access_token).and_return('token')
  end

  describe 'GET #sync' do
    context 'when sync is not in progress' do
      it 'starts sync job and redirects to forms path' do
        job = double('job')
        allow(job).to receive(:job_id).and_return('job123')
        allow(FetchAndSaveFormsJob).to receive(:perform_later).with(@user).and_return(job)

        get :sync

        expect(@user.reload.fetch_forms_job_id).to eq('job123')
        expect(flash[:success]).to eq(I18n.t('form.sync_in_progress'))
        expect(response).to redirect_to(forms_path(form_type: 'new'))
      end
    end
  end

  describe 'GET #index' do

    context 'when connected account is not found' do
      it 'redirects to connected accounts page with message' do
        get :index

        expect(response.code).to eq('302')
        expect(flash.to_h['danger']).to eq('Connected account not found.')
      end
    end

    context 'when form_type is EXISTING' do
      it 'assigns only saved forms for the user with pagination' do

        page = create(:page, user_id: @user.id)
        saved_form = create(:form, user_id: @user.id, saved_by_user: true, page: page)
        unsaved_form = create(:form, user_id: @user.id, saved_by_user: false, page: page)
        connected_account = create(:connected_account, user_id: @user.id)

        get :index, params: { form_type: 'existing' }

        expect(controller.instance_variable_get('@forms')).to include(saved_form)
        expect(controller.instance_variable_get('@forms')).not_to include(unsaved_form)
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when form_type is NEW' do
      it 'assigns only unsaved forms for the user with pagination and sets sync flag' do

        page = create(:page, user_id: @user.id)
        saved_form = create(:form, user_id: @user.id, saved_by_user: true, page: page)
        new_form = create(:form, user_id: @user.id, saved_by_user: false, page: page)
        connected_account = create(:connected_account, user_id: @user.id)

        get :index, params: { form_type: 'new' }

        expect(controller.instance_variable_get('@forms')).to include(new_form)
        expect(controller.instance_variable_get('@forms')).not_to include(saved_form)
        expect(controller.instance_variable_get('@form_type')).to eq('new')
        expect(response).to have_http_status(:ok)
      end
    end

    context 'when form_type is not provided' do
      it 'defaults to EXISTING forms' do

        page = create(:page, user_id: @user.id)
        saved_form = create(:form, user_id: @user.id, saved_by_user: true, page: page)
        connected_account = create(:connected_account, user_id: @user.id)

        get :index

        expect(controller.instance_variable_get('@form_type')).to eq('existing')
        expect(controller.instance_variable_get('@forms')).to include(saved_form)
        expect(response).to have_http_status(:ok)
      end
    end
  end

  describe "POST #create" do
    it "returns a success response" do
      page = create(:page, user_id: @user.id)
      Koala::Facebook::OAuth.any_instance.stub(:exchange_access_token).and_return('token')
      connected_account = create(:connected_account, user_id: @user.id)

      post :create, params: { form: { user_id: @user.id, source_id: '********', name: 'test form', kylas_source_id: '12', campaign_id: '67', page_id: page.id } }

      expect(flash[:success]).to eq("Form added Succesfully!")
      expect(Form.count).to eq(1)
      expect(Form.first.source_id).to eq('********')
      expect(Form.first.name).to eq('test form')
      expect(Form.first.kylas_source_id).to eq(12)
      expect(Form.first.campaign_id).to eq(67)
      expect(Form.first.page_id).to eq(page.id)
    end
  end

  describe "PUT #update" do
    context 'when request is for lead fields' do
      it "returns a success response" do
        page = create(:page, user_id: @user.id)
        Koala::Facebook::OAuth.any_instance.stub(:exchange_access_token).and_return('token')
        connected_account = create(:connected_account, user_id: @user.id)
        form = create(:form, user_id: @user.id, page_id: page.id)
        
        if connected_account.default_mapping
          data_to_be_created = [
            { kylas_field_name: 'lastName', fb_field_name: 'full_name', is_standard: true },
            { kylas_field_name: 'emails', fb_field_name: 'email', is_standard: true },
            { kylas_field_name: 'phoneNumbers', fb_field_name: 'phone_number', is_standard: true }
          ]

          form.mapped_fields.create(data_to_be_created)
        end

        form.utm_fields.create!(entity_type: LEAD)
        form.utm_fields.create!(entity_type: CONTACT)


        lead_params = {
          "utm_field"=>
            {
              "form_name"=>"lead_form",
              "campaign_id"=>"38243",
              "kylas_source_id"=>"38244",
              "owner_id"=>"4601",
              "sub_source"=>"sub source",
              "utm_source"=>"utm source",
              "utm_campaign"=>"utm campaign",
              "utm_medium"=>"utm medium",
              "utm_content"=>"",
              "utm_term"=>"utm term"
            },
          "create_lead_check"=>"on",
          "product_id"=>"21483",
          "commit"=>"Update",
          "controller"=>"forms",
          "action"=>"update",
          "id"=> form.id
        }
        put :update, params: lead_params

        expect(flash[:success]).to eq("Form updated Succesfully!")
        expect(form.reload.create_lead_check).to eq(true)
        expect(form.product_id).to eq(21483)
        expect(form.lead_utm_fields.campaign_id).to eq(38243)
        expect(form.lead_utm_fields.owner_id).to eq(4601)
        expect(form.lead_utm_fields.sub_source).to eq('sub source')
        expect(form.lead_utm_fields.utm_source).to eq('utm source')
        expect(form.lead_utm_fields.utm_campaign).to eq('utm campaign')
        expect(form.lead_utm_fields.utm_medium).to eq('utm medium')
        expect(form.lead_utm_fields.utm_content).to eq('')
        expect(form.lead_utm_fields.utm_term).to eq('utm term')
      end
    end

    context 'when request is for contact fields' do
      it 'updates all fields and returns success response' do
        page = create(:page, user_id: @user.id)
        Koala::Facebook::OAuth.any_instance.stub(:exchange_access_token).and_return('token')
        connected_account = create(:connected_account, user_id: @user.id)
        form = create(:form, user_id: @user.id, page_id: page.id)
        
        if connected_account.default_mapping
          data_to_be_created = [
            { kylas_field_name: 'lastName', fb_field_name: 'full_name', is_standard: true },
            { kylas_field_name: 'emails', fb_field_name: 'email', is_standard: true },
            { kylas_field_name: 'phoneNumbers', fb_field_name: 'phone_number', is_standard: true }
          ]

          form.mapped_fields.create(data_to_be_created)
        end

        form.utm_fields.create!(entity_type: LEAD)
        form.utm_fields.create!(entity_type: CONTACT)

        lead_params = {
          "utm_field"=>
            {
              "form_name"=>"contact_form",
              "campaign_id"=>"38243",
              "kylas_source_id"=>"38244",
              "owner_id"=>"4601",
              "sub_source"=>"sub source",
              "utm_source"=>"utm source",
              "utm_campaign"=>"utm campaign",
              "utm_medium"=>"utm medium",
              "utm_content"=>"",
              "utm_term"=>"utm term"
            },
          "create_contact_check"=>"on",
          "commit"=>"Update",
          "controller"=>"forms",
          "action"=>"update",
          "id"=> form.id
        }
        put :update, params: lead_params

        expect(flash[:success]).to eq("Form updated Succesfully!")
        expect(form.reload.create_contact_check).to eq(true)
        expect(form.contact_utm_fields.campaign_id).to eq(38243)
        expect(form.contact_utm_fields.owner_id).to eq(4601)
        expect(form.contact_utm_fields.sub_source).to eq('sub source')
        expect(form.contact_utm_fields.utm_source).to eq('utm source')
        expect(form.contact_utm_fields.utm_campaign).to eq('utm campaign')
        expect(form.contact_utm_fields.utm_medium).to eq('utm medium')
        expect(form.contact_utm_fields.utm_content).to eq('')
        expect(form.contact_utm_fields.utm_term).to eq('utm term')
      end
    end
  end

  describe "DELETE #destroy" do
    it "returns a success response" do
      page = create(:page, user_id: @user.id)
      Koala::Facebook::OAuth.any_instance.stub(:exchange_access_token).and_return('token')
      connected_account = create(:connected_account, user_id: @user.id)
      form = create(:form, user_id: @user.id, page_id: page.id)
      
      if connected_account.default_mapping
        data_to_be_created = [
          { kylas_field_name: 'lastName', fb_field_name: 'full_name', is_standard: true },
          { kylas_field_name: 'emails', fb_field_name: 'email', is_standard: true },
          { kylas_field_name: 'phoneNumbers', fb_field_name: 'phone_number', is_standard: true }
        ]

        form.mapped_fields.create(data_to_be_created)
      end

      form.utm_fields.create!(entity_type: LEAD)
      form.utm_fields.create!(entity_type: CONTACT)

      delete :destroy, params: { id: form.id }

      expect(flash[:success]).to eq("Form deleted Succesfully!")
      expect(Form.count).to eq(0)
    end
  end
end

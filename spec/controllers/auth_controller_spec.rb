require 'rails_helper'

RSpec.describe AuthController, type: :controller do
  describe "GET #index" do

    before do
      @request.env['devise.mapping'] = Devise.mappings[:user]
      @user = FactoryBot.create(:user)
      sign_in @user
      UsersController.any_instance.stub(:current_user) { @user }
    end

    it "returns a success response" do
      get :index
      expect(response).to have_http_status(:ok)
    end
  end
end

require 'rails_helper'

RSpec.describe UsersController, type: :controller do
  describe "PUT #update" do

    before do
      @request.env['devise.mapping'] = Devise.mappings[:user]
      @user = FactoryBot.create(:user)
      sign_in @user
      UsersController.any_instance.stub(:current_user) { @user }
      stub_request(:get, "https://api-qa.sling-dev.com/v1/users/me").
      with(
        headers: {
        'Api-Key'=> 'uwyeriu8374873',
        'Content-Type'=>'application/json',
        }).
      to_return(status: 200, body: file_fixture('user_detail.json'), headers: {})
    end

    it "returns a success response" do
      patch :update_api_key, params: {id: @user.id, user: { kylas_api_key: 'uwyeriu8374873' } }

      expect(flash[:success]).to eq("API Key Updated Succesfully!")
      expect(@user.kylas_api_key).to eq('uwyeriu8374873')
    end
  end
end
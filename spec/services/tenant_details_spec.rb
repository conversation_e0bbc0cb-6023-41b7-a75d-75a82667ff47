require 'rails_helper'
require 'support/stub_helper.rb'

RSpec.describe Kylas::TenantsDetails do
  let(:user) { build(:user, :kylas_tokens) }
  let(:bearer_token) { 'test_bearer_token' }

  describe '#fetch' do
    context 'when Kylas API key is valid' do
      it 'returns success with tenant details' do
        stub_fetch_tenant_details_request(
          user: user,
          response_body: file_fixture('kylas_tenant_details.json').read
        )
        response = described_class.new(user: user).fetch
        expect(response[:data].dig('country')).to eq('IN')
        expect(response[:success]).to eq(true)
      end
    end

    context 'when using a Bearer token' do
      it 'returns success with tenant details' do
        user.kylas_api_key=nil
        stub_fetch_tenant_details_request(
          user: user,
          response_body: file_fixture('kylas_tenant_details.json').read
        )
        response = described_class.new(user: user).fetch
        expect(response[:data].dig('country')).to eq('IN')
        expect(response[:success]).to eq(true)
      end
    end

    context 'when something went wrong' do
      it 'raises an exception' do
        allow(RestClient).to receive(:get).and_raise(StandardError)

        expect {
          described_class.new(user: user).fetch
        }.to raise_error(StandardError)
      end
    end

    context 'when Kylas API key is invalid' do
      it 'returns failure with error message' do
        stub_fetch_tenant_details_request(
          user: user,
          status_code: 400,
          response_body: {
            "code": "001079",
            "message": "Invalid API key",
            "fieldErrors": nil
          }.to_json
        )

        response = described_class.new(user: user).fetch
        expect(response[:success]).to eq(false)
        expect(response[:data]).to eq('Invalid Data!')
      end
    end
  end
end

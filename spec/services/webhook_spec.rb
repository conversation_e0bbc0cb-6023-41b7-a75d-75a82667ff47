#  frozen_string_literal: true

require 'rails_helper'
require 'support/stub_helper'

RSpec.describe 'Kylas::Webhook' do

  let(:user) { build(:user, :kylas_tokens) }
  let(:webhook_api_key) { '************************************' }
  let(:create_webhook_response) do
    JSON.parse(file_fixture('create_webhook_response.json').read)
  end

  describe '#create' do
    let(:create_payload) do
      {
        name: 'Webhook for User availability status on fb leads app.',
        requestType: 'POST',
        url: "http://localhost:3000/webhooks/handler.json",
        authenticationType: 'API_KEY',
        authenticationKey: Base64.strict_encode64(
          { 'keyName': 'Api-Key', 'value': webhook_api_key }.to_json
        ),
        events: %w[USER_UPDATED],
        system: true,
        active: true
      }
    end

    context 'when parameters are not valid for webhook creation' do
      it 'returns success false and error response' do
        invalid_create_payload = create_payload
        invalid_create_payload.except!(:url)
        expect_any_instance_of(Kylas::Webhook).to receive(:webhook_payload)
          .and_return(invalid_create_payload)
        stub_webhook_create_request(
          user: user,
          status_code: 422,
          request_body: invalid_create_payload,
          response_body: create_webhook_response['error']
        )
        result = Kylas::Webhook.new(user).create
        expect(result[:success]).to eq(false)
        expect(result[:data]).to eq(
          {
            'errorCode' => '027003',
            'message' => "Url can't be blank,Url is invalid"
          }
        )
      end
    end

    context 'when parameters are valid for webhook creation' do
      it 'returns success true' do
        expect_any_instance_of(Kylas::Webhook).to receive(:webhook_payload)
          .and_return(create_payload)
        stub_webhook_create_request(
          user: user,
          status_code: 201,
          request_body: create_payload,
          response_body: create_webhook_response['success']
        )
        result = Kylas::Webhook.new(user).create
        expect(result[:success]).to eq(true)
        expect(result[:data]).to eq({ "id"=>442 })
      end
    end
  end
end

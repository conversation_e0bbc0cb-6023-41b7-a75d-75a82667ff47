# unused

=begin
require 'rails_helper'

RSpec.describe Kylas::GetLeadPicklistValues, type: :controller do
  LEAD_LAYOUT_DATA = {
    "layoutItems"=> [{
      "type"=>"SECTION",
      "layoutItems"=> [
        {
          "type"=>"FIELD",
          "layoutItems"=>[],
          "item"=>{
            "id"=>2622,
            "sectionId"=>606,
            "type"=>"PICK_LIST",
            "entity"=>nil,
            "displayName"=>"Campaign",
            "internalName"=>"campaign",
            "pickLists"=>
            [
              {
                "id"=>1022,
                "displayName"=>"Organic",
                "name"=>"ORGANIC",
                "lookupUrl"=>nil,
                "systemDefault"=>false
              }
            ],
            "description"=>nil,
            "length"=>nil,
            "greaterThan"=>nil,
            "lessThan"=>nil,
            "lookupUrl"=>nil,
            "regex"=>nil,
            "showDefaultOptions"=>false,
            "multiValue"=>false,
            "required"=>false,
            "sortable"=>true,
            "internal"=>false,
            "important"=>false,
            "standard"=>true,
            "filterable"=>true,
            "unique"=>false,
            "readOnly"=>false
          },
          "row"=>1,
          "column"=>1,
          "width"=>6,
          "id"=>3228
        },
        {
          "type"=>"FIELD",
          "layoutItems"=>[],
          "item"=>
          {
            "id"=>2623,
            "sectionId"=>606,
            "type"=>"PICK_LIST",
            "entity"=>nil,
            "displayName"=>"Source",
            "internalName"=>"source",
            "pickLists"=>
            [
              {
                "id"=>1023,
                "displayName"=>"Google",
                "name"=>"GOOGLE",
                "lookupUrl"=>nil,
                "systemDefault"=>false
              },
              {
                "id"=>1024,
                "displayName"=>"Facebook",
                "name"=>"FACEBOOK",
                "lookupUrl"=>nil,
                "systemDefault"=>false
              },
              {
                "id"=>1025,
                "displayName"=>"LinkedIn",
                "name"=>"LINKEDIN",
                "lookupUrl"=>nil,
                "systemDefault"=>false
              },
              {
                "id"=>1026,
                "displayName"=>"Exhibition",
                "name"=>"EXHIBITION",
                "lookupUrl"=>nil,
                "systemDefault"=>false
              },
              {
                "id"=>1027,
                "displayName"=>"Cold Calling",
                "name"=>"COLD_CALLING",
                "lookupUrl"=>nil,
                "systemDefault"=>false
              }
            ],
            "description"=>nil,
            "length"=>nil,
            "greaterThan"=>nil,
            "lessThan"=>nil,
            "lookupUrl"=>nil,
            "regex"=>nil,
            "showDefaultOptions"=>false,
            "multiValue"=>false,
            "required"=>false,
            "sortable"=>true,
            "internal"=>false,
            "important"=>false,
            "standard"=>true,
            "filterable"=>true,
            "unique"=>false,
            "readOnly"=>false
          },
          "row"=>1,
          "column"=>2,
          "width"=>6,
          "id"=>3229
        }
      ],
      "item"=>{"id"=>606, "heading"=>"Source", "collapsible"=>false},
      "row"=>7,
      "column"=>1,
      "width"=>12,
      "id"=>3227
    }]
  }

  describe "#call" do
    before(:each) do
      @request.env['devise.mapping'] = Devise.mappings[:user]
      @user = FactoryBot.create(:user, kylas_api_key: 'wuey-8374-oieur-uw4243')
      sign_in @user
    end

    context "with valid input( when current user and api key present)" do
      it "returns expected output" do
        stub_request(:get, APP_KYLAS_HOST + "/v1/ui/layouts/CREATE/LEAD").
        with(
          headers: {
            'api-key': @user.kylas_api_key
          }).
        to_return(status: 200, body: LEAD_LAYOUT_DATA.to_json, headers: {})

        obj = Kylas::GetLeadPicklistValues.new(@user)
        result = obj.call
        expect(result).to eq({:campaigns=>[{:id=>1022, :name=>"Organic"}], :sources=>[{:id=>1023, :name=>"Google"}, {:id=>1024, :name=>"Facebook"}, {:id=>1025, :name=>"LinkedIn"}, {:id=>1026, :name=>"Exhibition"}, {:id=>1027, :name=>"Cold Calling"}]})
      end
    end

    context "with invalid input - " do
      it "when API key is not available should rerturn blank values" do
        @user.update(kylas_api_key: '')

        obj = Kylas::GetLeadPicklistValues.new(@user)
        result = obj.call
        expect(result).to eq({:campaigns=>[], :sources=>[]})
      end
    end
  end
end
=end

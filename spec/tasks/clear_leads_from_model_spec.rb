# frozen_string_literal: true

require 'rails_helper'

Rails.application.load_tasks

RSpec.describe 'ClearLeadsFromModel', type: :task do
  it 'should delete leads data from model after 90 days' do
    user = create(:user)
    Koala::Facebook::OAuth.any_instance.stub(:exchange_access_token).and_return('token')
    connected_account = create(:connected_account, user_id: user.id)
    page = create(:page, user_id: user.id)
    form = create(:form, user_id: user.id, page_id: page.id)
    lead1 = create(:failed_lead, form_id: form.id)
    lead2 = create(:failed_lead, form_id: form.id, created_at: '2022-07-21 07:57:35.769652')
    lead3 = create(:failed_lead, form_id: form.id)
    lead4 = create(:failed_lead, form_id: form.id, created_at: '2022-07-21 07:57:35.769652')
    lead5 = create(:failed_lead, form_id: form.id)
    Rake::Task['clear_leads_from_model:clear_data'].invoke
    expect(FailedLead.count).to eq(3)
  end
end

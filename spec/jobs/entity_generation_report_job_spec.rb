# frozen_string_literal: true

require 'rails_helper'

RSpec.describe EntityGenerationReportJob, type: :job do
  describe '#perform_later' do
    include ActiveJob::TestHelper
    let(:user) { create(:user) }
    it 'should send email to tenant about entities created in last 1 hour if any entity failed to create' do
      expect do
        EntityGenerationReportJob.perform_later(user)
      end.to have_enqueued_job
    end
  end
end

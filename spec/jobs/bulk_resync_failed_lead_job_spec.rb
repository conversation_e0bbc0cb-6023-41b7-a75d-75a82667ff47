# frozen_string_literal: true

require 'rails_helper'

RSpec.describe BulkResyncFailedLeadJob, type: :job do
  let(:user) { create(:user) }
  let(:bulk_job) { create(:bulk_job, user: user, status: IN_PROGRESS) }
  let(:page) { create(:page, user: user) }
  let(:form) { create(:form, user: user,page_id: page.id) }
  let(:failed_lead1) { create(:failed_lead, form: form, lead_gen_id: '123456789', status: :fail) }
  let(:failed_lead2) { create(:failed_lead, form: form, lead_gen_id: '987654321', status: :fail) }
  let(:log_ids) { [failed_lead1.id, failed_lead2.id] }

  describe '#perform' do
    context 'when bulk job exists' do
      before do
        failed_lead1
        failed_lead2
      end

      context 'with successful processing' do
        it 'processes all failed leads successfully' do
          allow(ProcessLeadGen).to receive(:new).and_return(double(call: true))

          described_class.new.perform(log_ids, user.id, bulk_job.id)

          expect(bulk_job.reload.success_count).to eq(2)
          expect(bulk_job.reload.failed_count).to eq(0)
          expect(bulk_job.reload.status).to eq(COMPLETED)
        end
      end

      context 'with failed processing' do
        it 'handles processing failures' do
          allow(ProcessLeadGen).to receive(:new).and_return(double(call: false))

          described_class.new.perform(log_ids, user.id, bulk_job.id)

          expect(bulk_job.reload.success_count).to eq(0)
          expect(bulk_job.reload.failed_count).to eq(2)
          expect(bulk_job.reload.status).to eq(COMPLETED)
        end
      end

      context 'with exceptions during processing' do
        it 'handles exceptions and increments failed count' do
          allow(ProcessLeadGen).to receive(:new).and_raise(StandardError.new('Processing error'))
          described_class.new.perform(log_ids, user.id, bulk_job.id)

          expect(bulk_job.reload.success_count).to eq(0)
          expect(bulk_job.reload.failed_count).to eq(2)
          expect(bulk_job.reload.status).to eq(COMPLETED)
        end
      end

      context 'with mixed results' do
        it 'handles both successful and failed processing' do
          allow(ProcessLeadGen).to receive(:new).with(any_args).and_return(
            double(call: true),
            double(call: false)
          )

          described_class.new.perform(log_ids, user.id, bulk_job.id)

          expect(bulk_job.reload.success_count).to eq(1)
          expect(bulk_job.reload.failed_count).to eq(1)
          expect(bulk_job.reload.status).to eq(COMPLETED)
        end
      end

      it 'processes logs in ascending order by created_at' do
        failed_lead3 = create(:failed_lead, form: form, created_at: 1.day.ago)
        failed_lead4 = create(:failed_lead, form: form, created_at: 2.days.ago)
        
        allow(ProcessLeadGen).to receive(:new).and_return(double(call: true))
        
        expect(FailedLead).to receive(:where).with(id: [failed_lead4.id, failed_lead3.id]).and_call_original
        
        described_class.new.perform([failed_lead4.id, failed_lead3.id], user.id, bulk_job.id)
      end
    end

    context 'when bulk job does not exist' do
      it 'returns early without processing' do
        expect(ProcessLeadGen).not_to receive(:new)
        
        described_class.new.perform(log_ids, user.id, 999999)
      end
    end

    context 'with large number of logs' do
      let(:many_logs) { create_list(:failed_lead, 100, form: form) }
      let(:many_log_ids) { many_logs.map(&:id) }

      it 'processes large batches efficiently' do
        allow(ProcessLeadGen).to receive(:new).and_return(double(call: true))

        described_class.new.perform(many_log_ids, user.id, bulk_job.id)

        expect(bulk_job.reload.success_count).to eq(100)
        expect(bulk_job.reload.failed_count).to eq(0)
        expect(bulk_job.reload.status).to eq(COMPLETED)
      end
    end
  end
end

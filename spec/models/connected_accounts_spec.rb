require 'rails_helper'

RSpec.describe ConnectedAccount, type: :model do
  let(:user){ create(:user) }
  describe 'associations' do
    it { should belong_to(:user).class_name('User') }
  end

  describe 'DB Scema' do
    it do
      should have_db_column(:is_active).of_type(:boolean)
    end

    it do
      should have_db_column(:access_token).of_type(:string)
    end

    it do
      should have_db_column(:refresh_token).of_type(:string)
    end

    it do
      should have_db_column(:email_id).of_type(:string)
    end

    it do
      should have_db_column(:user_id).of_type(:integer)
    end
  end

  describe '#callback' do
    it 'will save long lived token of connected account' do
      Koala::Facebook::OAuth.any_instance.stub(:exchange_access_token).and_return('token')
      connected_account = create(:connected_account, user_id: user.id)
      expect(connected_account.refresh_token).to eq 'token'

    end
  end
end


require 'rails_helper'

RSpec.describe User, type: :model do
  describe 'associations' do
    it { should have_one(:connected_account).class_name('ConnectedAccount') }
    it { should have_many(:pages).class_name('Page') }
    it { should have_many(:forms).class_name('Form') }
  end

  describe 'validations' do
    it { should validate_presence_of(:name) }
    it { should validate_presence_of(:email) }
    it { should validate_uniqueness_of(:email).ignoring_case_sensitivity }
  end

  describe 'DB Scema' do
    it do
      should have_db_column(:name).of_type(:string)
    end

    it do
      should have_db_column(:kylas_api_key).of_type(:string)
    end

    it do
      should have_db_column(:encrypted_password).of_type(:string)
    end

    it do
      should have_db_column(:reset_password_token).of_type(:string)
    end

    it do
      should have_db_column(:reset_password_sent_at).of_type(:datetime)
    end

    it do
      should have_db_column(:remember_created_at).of_type(:datetime)
    end

    it do
      should have_db_column(:email).of_type(:string).with_options(null: false, default: '')
    end
  end
end

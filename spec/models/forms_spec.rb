# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Form, type: :model do
  describe 'associations' do
    it { should belong_to(:user).class_name('User') }
    it { should belong_to(:page).class_name('Page') }

    it { should have_many(:failed_leads).class_name('FailedLead') }
  end

  describe 'DB Scema' do
    it do
      should have_db_column(:source_id).of_type(:string)
    end

    it do
      should have_db_column(:user_id).of_type(:integer)
    end

    it do
      should have_db_column(:page_id).of_type(:integer)
    end
  end

  context 'associations' do
    it 'has_many utm_fields' do
      utm_fields_relation = Form.reflect_on_association(:utm_fields)
      expect(utm_fields_relation.macro).to eq(:has_many)
    end
  end
end

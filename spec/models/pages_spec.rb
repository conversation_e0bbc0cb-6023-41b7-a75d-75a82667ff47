require 'rails_helper'

RSpec.describe Page, type: :model do
  describe 'associations' do
    it { should belong_to(:user).class_name('User') }

    it { should have_many(:forms).class_name('Form') }
  end

  describe 'validations' do
    it { should validate_presence_of(:source_id) }
  end

  describe 'DB Scema' do
    it do
      should have_db_column(:source_id).of_type(:string)
    end

    it do
      should have_db_column(:user_id).of_type(:integer)
    end
  end
end
      
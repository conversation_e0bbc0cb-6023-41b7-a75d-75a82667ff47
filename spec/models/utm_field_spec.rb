# frozen_string_literal: true

require 'rails_helper'

RSpec.describe UtmField, type: :model do
  let(:user){ create(:user) }
  let(:connected_account) { create(:connected_account, user_id: user.id) }
  let(:page) { create(:page, user_id: user.id) }
  let(:form) { create(:form, user_id: user.id, page_id: page.id) }
  let(:lead_utm_fields) { create(:utm_field, entity_type: LEAD, form_id: form.id) }
  let(:contact_utm_fields) { create(:utm_field, entity_type: 'contact', form_id: form.id) }

  describe 'associations' do
    it 'belongs_to form' do
      forms_relation = UtmField.reflect_on_association(:form)
      expect(forms_relation.macro).to eq(:belongs_to)
    end
  end

  describe 'validations' do
    it 'should not create utm_fields for same entity multiple times for single form' do
      lead_utm_fields
      contact_utm_fields.entity_type = LEAD
      contact_utm_fields.validate
      expect(contact_utm_fields.errors.messages[:entity_type]).to eq(['has already been taken'])
    end
  end
end

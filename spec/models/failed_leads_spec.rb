require 'rails_helper'

RSpec.describe FailedLead, type: :model do
  describe 'associations' do
    it { should belong_to(:form).class_name('Form') }
  end

  describe 'validations' do
    it { should validate_presence_of(:lead_gen_id) }
  end

  describe 'DB Scema' do
    it do
      should have_db_column(:lead_gen_id).of_type(:string)
    end

    it do
      should have_db_column(:form_id).of_type(:integer)
    end

    it do
      should have_db_column(:raw_data).of_type(:jsonb)
    end
  end
end
        
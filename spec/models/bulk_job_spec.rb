require 'rails_helper'

RSpec.describe BulkJob, type: :model do
  let (:user) { create(:user) }
  let (:bulk_job) { build(:bulk_job, user_id: user.id)}

  context "with valid data" do
    it "should be valid" do
      expect(bulk_job).to be_valid
    end
  end

  context "with invalid data" do
    it "should not be valid without a user id" do
      bulk_job.user_id = nil
      expect(bulk_job).not_to be_valid
      expect(bulk_job.errors[:user]).to include("must exist")
    end
    
    it "should not be a valid with incorrect status" do
      bulk_job.status = "invalid_status"
      expect(bulk_job).not_to be_valid
      expect(bulk_job.errors[:status]).to include("is not included in the list")
    end
  end
end
